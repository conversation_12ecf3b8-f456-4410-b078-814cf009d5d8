<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canlı Trading Takip - Forex Trading Bot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .signal-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .signal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .signal-buy {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .signal-sell {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }
        .signal-hold {
            background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
            color: white;
        }
        .portfolio-card {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border-radius: 15px;
        }
        .trade-row {
            transition: all 0.3s ease;
        }
        .trade-row:hover {
            background-color: #f8f9fa;
        }
        .profit-positive {
            color: #28a745;
            font-weight: bold;
        }
        .profit-negative {
            color: #dc3545;
            font-weight: bold;
        }
        .live-indicator {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> Forex Trading Bot
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Ana Sayfa</a>
                <a class="nav-link" href="/strategy">Strateji</a>
                <a class="nav-link" href="/bulk-analysis">Toplu Analiz</a>
                <a class="nav-link" href="/smart-optimization">Akıllı Optimizasyon</a>
                <a class="nav-link active" href="/live-trading">Canlı Trading</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Başlık ve Canlı Durum -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-3">
                    <i class="fas fa-broadcast-tower text-success live-indicator"></i>
                    Canlı Trading Takip
                    <small class="text-muted">Gerçek zamanlı forex yatırım takibi</small>
                </h1>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Canlı Sistem:</strong> Veriler gerçek zamanlı güncelleniyor. Son güncelleme: <span id="lastUpdate">--</span>
                </div>
            </div>
        </div>

        <!-- Portföy Özeti -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="portfolio-card p-4">
                    <h4 class="mb-3"><i class="fas fa-wallet"></i> Portföy Özeti</h4>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="totalBalance">$10,000</div>
                                <div class="metric-label">Toplam Bakiye</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value profit-positive" id="totalProfit">+$0</div>
                                <div class="metric-label">Toplam Kar/Zarar</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="openPositions">0</div>
                                <div class="metric-label">Açık Pozisyonlar</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <div class="metric-value" id="winRate">0%</div>
                                <div class="metric-label">Kazanma Oranı</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Canlı Sinyaller -->
        <div class="row mb-4">
            <div class="col-12">
                <h4><i class="fas fa-signal"></i> Canlı Trading Sinyalleri</h4>
                <div class="row" id="signalsContainer">
                    <!-- Sinyaller buraya dinamik olarak eklenecek -->
                </div>
            </div>
        </div>

        <!-- İşlem Kayıt Formu -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-plus"></i> Yeni İşlem Kaydı</h5>
                    </div>
                    <div class="card-body">
                        <form id="tradeForm">
                            <div class="mb-3">
                                <label for="tradeSymbol" class="form-label">Forex Çifti</label>
                                <select class="form-select" id="tradeSymbol" required>
                                    <option value="">Seçiniz...</option>
                                    <option value="EUR/USD">EUR/USD</option>
                                    <option value="GBP/USD">GBP/USD</option>
                                    <option value="USD/JPY">USD/JPY</option>
                                    <option value="AUD/USD">AUD/USD</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tradeType" class="form-label">İşlem Türü</label>
                                <select class="form-select" id="tradeType" required>
                                    <option value="">Seçiniz...</option>
                                    <option value="BUY">Alış (Long)</option>
                                    <option value="SELL">Satış (Short)</option>
                                </select>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tradeAmount" class="form-label">Lot Miktarı</label>
                                        <input type="number" class="form-control" id="tradeAmount" step="0.01" min="0.01" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tradePrice" class="form-label">Giriş Fiyatı</label>
                                        <input type="number" class="form-control" id="tradePrice" step="0.00001" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="stopLoss" class="form-label">Stop Loss</label>
                                        <input type="number" class="form-control" id="stopLoss" step="0.00001">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="takeProfit" class="form-label">Take Profit</label>
                                        <input type="number" class="form-control" id="takeProfit" step="0.00001">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save"></i> İşlemi Kaydet
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- İşlem Kapatma Formu -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-times"></i> İşlem Kapatma</h5>
                    </div>
                    <div class="card-body">
                        <form id="closeTradeForm">
                            <div class="mb-3">
                                <label for="closeTradeId" class="form-label">Kapatılacak İşlem</label>
                                <select class="form-select" id="closeTradeId" required>
                                    <option value="">Açık pozisyon seçiniz...</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="closePrice" class="form-label">Kapanış Fiyatı</label>
                                <input type="number" class="form-control" id="closePrice" step="0.00001" required>
                            </div>

                            <div class="mb-3">
                                <label for="closeReason" class="form-label">Kapanış Nedeni</label>
                                <select class="form-select" id="closeReason">
                                    <option value="MANUAL">Manuel Kapatma</option>
                                    <option value="STOP_LOSS">Stop Loss</option>
                                    <option value="TAKE_PROFIT">Take Profit</option>
                                    <option value="TRAILING_STOP">Trailing Stop</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-times-circle"></i> İşlemi Kapat
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Açık Pozisyonlar -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-chart-line"></i> Açık Pozisyonlar</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Forex Çifti</th>
                                        <th>Tür</th>
                                        <th>Lot</th>
                                        <th>Giriş Fiyatı</th>
                                        <th>Mevcut Fiyat</th>
                                        <th>Kar/Zarar</th>
                                        <th>Stop Loss</th>
                                        <th>Take Profit</th>
                                        <th>Aksiyon</th>
                                    </tr>
                                </thead>
                                <tbody id="openPositionsTable">
                                    <tr>
                                        <td colspan="10" class="text-center text-muted">Açık pozisyon bulunmuyor</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- İşlem Geçmişi -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-history"></i> İşlem Geçmişi</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Tarih</th>
                                        <th>Forex Çifti</th>
                                        <th>Tür</th>
                                        <th>Lot</th>
                                        <th>Giriş</th>
                                        <th>Çıkış</th>
                                        <th>Kar/Zarar</th>
                                        <th>Süre</th>
                                        <th>Durum</th>
                                    </tr>
                                </thead>
                                <tbody id="tradeHistoryTable">
                                    <tr>
                                        <td colspan="10" class="text-center text-muted">Henüz işlem geçmişi bulunmuyor</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/live_trading.js"></script>
</body>
</html>
