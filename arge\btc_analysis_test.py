#!/usr/bin/env python3
"""
BTC/USDT Özel Analiz Testi
2025 yılı için optimize edilmiş strateji testi
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.strategy import TradingStrategy
from app.models.indicators import TechnicalIndicators
from app.models.config import Config
import pandas as pd
from datetime import datetime, timedelta
import json

def test_btc_strategy():
    """BTC/USDT için optimize edilmiş strateji testi"""
    
    print("🚀 BTC/USDT Optimize Edilmiş Strateji Testi Başlatılıyor...")
    
    # Test parametreleri
    symbol = "BTCUSDT"
    timeframe = "1d"  # Günlük veriler daha kararlı
    start_date = "2025-01-01"
    initial_balance = 10000
    
    # BTC için agresif optimize edilmiş strateji parametreleri (v2.0)
    btc_optimized_params = {
        'stopLoss': 4,          # %4 stop loss (daha sıkı risk kontrolü)
        'takeProfit': 20,       # %20 take profit (yüksek kar hedefi)
        'trailingStop': 2.5,    # %2.5 trailing stop (sıkı takip)
        'basePosition': 25,     # %25 temel pozisyon (daha agresif)
        'maxPosition': 40,      # %40 maksimum pozisyon
        'minPosition': 15,      # %15 minimum pozisyon
        'rsiLower': 25,         # RSI alt sınır (çok geniş)
        'rsiUpper': 95,         # RSI üst sınır (çok geniş)
        'emaSpread': 0.05,      # EMA spread (çok dar)
        'momentumScore': 2,     # Momentum skoru (çok düşük - daha fazla işlem)
        'priceChange3': 0.1,    # %0.1 (çok düşük)
        'priceChange10': 0.3,   # %0.3 (çok düşük)
        'volatilityMax': 10,    # %10 maksimum volatilite (yüksek)
        'adxMinimum': 10,       # ADX minimum (çok düşük)
        'commission': 0.01,
        'enableShort': False,
        'dynamicTrailing': True
    }
    
    try:
        # Veri çekme
        print(f"📊 {symbol} verileri çekiliyor ({start_date} - bugün)...")
        config = Config()
        data_fetcher = DataFetcher(config)
        df = data_fetcher.fetch_ohlcv(symbol.replace('USDT', '/USDT'), timeframe, start_date)
        
        if df is None or len(df) < 50:
            print("❌ Yeterli veri bulunamadı!")
            return None
            
        print(f"✅ {len(df)} adet veri noktası alındı")
        
        # Strateji ve simülasyon motorunu başlat
        config = Config()
        simulation_engine = SimulationEngine(config, btc_optimized_params)
        
        # Simülasyonu çalıştır
        print("🔄 Simülasyon çalıştırılıyor...")
        results = simulation_engine.run_simulation(df, initial_balance)
        
        # Sonuçları analiz et
        print("\n" + "="*60)
        print("📈 BTC/USDT OPTİMİZE EDİLMİŞ STRATEJİ SONUÇLARI")
        print("="*60)
        
        print(f"💰 Başlangıç Bakiye: ${initial_balance:,.2f}")
        print(f"💰 Son Bakiye: ${results['final_balance']:,.2f}")
        print(f"📊 Toplam Kar: ${results['total_profit']:,.2f}")
        print(f"📈 Kar Oranı: %{results['profit_percentage']:.2f}")
        print(f"🔢 Toplam İşlem: {results['total_trades']}")
        print(f"✅ Kazanan İşlem: {results['winning_trades']}")
        print(f"❌ Kaybeden İşlem: {results['losing_trades']}")
        print(f"🎯 Kazanma Oranı: %{results['win_rate']:.2f}")
        print(f"📉 Maksimum Düşüş: %{results['max_drawdown']:.2f}")
        print(f"⚡ Sharpe Oranı: {results['sharpe_ratio']:.3f}")
        
        # Hedef analizi
        target_profit_pct = 10.0  # %10 hedef
        if results['profit_percentage'] >= target_profit_pct:
            print(f"\n🎉 BAŞARILI! %{target_profit_pct} hedefine ulaşıldı!")
        else:
            needed_improvement = target_profit_pct - results['profit_percentage']
            print(f"\n⚠️  Hedef için %{needed_improvement:.2f} daha kar gerekli")
            
        # Performans değerlendirmesi
        print("\n" + "="*60)
        print("🔍 PERFORMANS DEĞERLENDİRMESİ")
        print("="*60)
        
        if results['win_rate'] < 40:
            print("❌ Kazanma oranı düşük - Sinyal filtreleri gözden geçirilmeli")
        elif results['win_rate'] > 60:
            print("✅ İyi kazanma oranı")
        else:
            print("⚠️  Orta seviye kazanma oranı")
            
        if results['total_trades'] < 5:
            print("❌ Çok az işlem - Filtreler çok katı")
        elif results['total_trades'] > 50:
            print("⚠️  Çok fazla işlem - Aşırı trading riski")
        else:
            print("✅ Uygun işlem sayısı")
            
        if results['max_drawdown'] > 15:
            print("❌ Yüksek risk - Drawdown çok fazla")
        elif results['max_drawdown'] < 5:
            print("✅ Düşük risk profili")
        else:
            print("⚠️  Orta seviye risk")
            
        # Öneriler
        print("\n" + "="*60)
        print("💡 İYİLEŞTİRME ÖNERİLERİ")
        print("="*60)
        
        if results['profit_percentage'] < target_profit_pct:
            print("🔧 Önerilen iyileştirmeler:")
            
            if results['total_trades'] < 10:
                print("   • Momentum skoru eşiğini düşür (4 → 3)")
                print("   • RSI aralığını genişlet (35-85 → 30-90)")
                print("   • ADX minimum değerini düşür (15 → 12)")
                
            if results['win_rate'] < 45:
                print("   • Take profit oranını artır (12% → 15%)")
                print("   • Stop loss oranını daralt (6% → 5%)")
                print("   • VWAP ve MFI filtrelerini güçlendir")
                
            if results['max_drawdown'] > 10:
                print("   • Pozisyon boyutunu küçült (20% → 15%)")
                print("   • Trailing stop'u sıkılaştır (4% → 3%)")
                print("   • Volatilite filtresi ekle")
                
        return results
        
    except Exception as e:
        print(f"❌ Hata oluştu: {str(e)}")
        return None

def compare_with_buy_hold():
    """Buy & Hold stratejisi ile karşılaştırma"""
    print("\n" + "="*60)
    print("📊 BUY & HOLD KARŞILAŞTIRMASI")
    print("="*60)
    
    try:
        config = Config()
        data_fetcher = DataFetcher(config)
        df = data_fetcher.fetch_ohlcv("BTC/USDT", "1d", "2025-01-01")
        
        if df is None or len(df) < 2:
            print("❌ Karşılaştırma için veri bulunamadı")
            return
            
        start_price = df['close'].iloc[0]
        end_price = df['close'].iloc[-1]
        buy_hold_return = ((end_price - start_price) / start_price) * 100
        
        print(f"📈 Buy & Hold Getiri: %{buy_hold_return:.2f}")
        print(f"🗓️  Başlangıç Fiyat: ${start_price:,.2f}")
        print(f"🗓️  Son Fiyat: ${end_price:,.2f}")
        
    except Exception as e:
        print(f"❌ Buy & Hold hesaplama hatası: {str(e)}")

if __name__ == "__main__":
    # Ana test
    results = test_btc_strategy()
    
    # Buy & Hold karşılaştırması
    compare_with_buy_hold()
    
    print("\n🏁 Test tamamlandı!")
