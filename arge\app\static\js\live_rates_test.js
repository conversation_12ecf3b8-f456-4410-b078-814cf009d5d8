// Live Rates Test JavaScript

let testStats = {
    successCount: 0,
    errorCount: 0,
    totalRequests: 0,
    responseTimes: []
};

let proxyList = [];

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    initializeTestPage();
    loadProxyStatus();
});

// Test sayfasını başlat
function initializeTestPage() {
    updateStats();
    console.log('🧪 Live Rates Test sayfası başlatıldı');
}

// Proxy durumunu yükle
async function loadProxyStatus() {
    try {
        const response = await fetch('/api/proxy-status');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                displayProxyStatus(data.proxy_status);
            }
        }
    } catch (error) {
        console.error('Proxy status yüklenemedi:', error);
    }
}

// Proxy durumunu göster
function displayProxyStatus(status) {
    const container = document.getElementById('proxyStatus');
    container.innerHTML = `
        <div class="col-md-3">
            <div class="metric-box">
                <div class="metric-value text-primary">${status.total_proxies}</div>
                <div class="metric-label">Toplam Proxy</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-box">
                <div class="metric-value text-success">${status.working_proxies}</div>
                <div class="metric-label">Çalışan</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-box">
                <div class="metric-value text-danger">${status.failed_proxies}</div>
                <div class="metric-label">Başarısız</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="metric-box">
                <div class="metric-value text-info">${status.request_count}</div>
                <div class="metric-label">Toplam İstek</div>
            </div>
        </div>
    `;
}

// Direkt bağlantı testi
async function testDirectConnection() {
    const testMode = document.getElementById('testMode').value;
    const iterations = getIterationCount(testMode);
    
    addTestResult('info', 'Direkt bağlantı testi başlatılıyor...', null, true);
    
    for (let i = 0; i < iterations; i++) {
        await performDirectTest(i + 1, iterations);
        if (iterations > 1) {
            await sleep(1000); // 1 saniye bekle
        }
    }
}

// Proxy ile test
async function testWithProxy() {
    const testMode = document.getElementById('testMode').value;
    const iterations = getIterationCount(testMode);
    
    addTestResult('info', 'Proxy ile test başlatılıyor...', null, true);
    
    for (let i = 0; i < iterations; i++) {
        await performProxyTest(i + 1, iterations);
        if (iterations > 1) {
            await sleep(1000); // 1 saniye bekle
        }
    }
}

// Tüm proxy'leri test et
async function testAllProxies() {
    addTestResult('info', 'Tüm proxy\'ler test ediliyor...', null, true);
    
    try {
        const response = await fetch('/api/test-all-proxies', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayProxyTestResults(data.results);
        } else {
            addTestResult('error', 'Proxy test hatası', data.error);
        }
    } catch (error) {
        addTestResult('error', 'Proxy test hatası', error.message);
    }
}

// Direkt test gerçekleştir
async function performDirectTest(iteration, total) {
    const startTime = Date.now();
    
    try {
        const response = await fetch('/api/test-live-rates-direct', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        const data = await response.json();
        
        testStats.totalRequests++;
        testStats.responseTimes.push(responseTime);
        
        if (data.success) {
            testStats.successCount++;
            addTestResult('success', 
                `Direkt bağlantı başarılı (${iteration}/${total})`, 
                data, false, responseTime);
        } else {
            testStats.errorCount++;
            addTestResult('error', 
                `Direkt bağlantı başarısız (${iteration}/${total})`, 
                data.error, false, responseTime);
        }
        
    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        testStats.totalRequests++;
        testStats.errorCount++;
        testStats.responseTimes.push(responseTime);
        
        addTestResult('error', 
            `Direkt bağlantı hatası (${iteration}/${total})`, 
            error.message, false, responseTime);
    }
    
    updateStats();
}

// Proxy test gerçekleştir
async function performProxyTest(iteration, total) {
    const startTime = Date.now();
    
    try {
        const response = await fetch('/api/test-live-rates-proxy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        const data = await response.json();
        
        testStats.totalRequests++;
        testStats.responseTimes.push(responseTime);
        
        if (data.success) {
            testStats.successCount++;
            addTestResult('success', 
                `Proxy test başarılı (${iteration}/${total})`, 
                data, false, responseTime);
        } else {
            testStats.errorCount++;
            addTestResult('error', 
                `Proxy test başarısız (${iteration}/${total})`, 
                data.error, false, responseTime);
        }
        
    } catch (error) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        testStats.totalRequests++;
        testStats.errorCount++;
        testStats.responseTimes.push(responseTime);
        
        addTestResult('error', 
            `Proxy test hatası (${iteration}/${total})`, 
            error.message, false, responseTime);
    }
    
    updateStats();
}

// Test sonucu ekle
function addTestResult(type, message, data, isLoading = false, responseTime = null) {
    const container = document.getElementById('testResults');
    
    // İlk test ise placeholder'ı temizle
    if (testStats.totalRequests === 0 && !isLoading) {
        container.innerHTML = '';
    }
    
    const timestamp = new Date().toLocaleTimeString('tr-TR');
    const responseTimeText = responseTime ? ` (${responseTime}ms)` : '';
    
    const statusClass = type === 'success' ? 'status-success' : 
                       type === 'error' ? 'status-error' : 'status-warning';
    
    const icon = type === 'success' ? 'fa-check-circle' : 
                 type === 'error' ? 'fa-times-circle' : 'fa-info-circle';
    
    const loadingSpinner = isLoading ? '<span class="loading-spinner ms-2"></span>' : '';
    
    const resultHtml = `
        <div class="alert ${statusClass} mb-2">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <i class="fas ${icon} me-2"></i>
                    <strong>${message}</strong>${responseTimeText}${loadingSpinner}
                    <small class="d-block mt-1">${timestamp}</small>
                </div>
                ${data && document.getElementById('showRawData').checked ? 
                    `<button class="btn btn-sm btn-outline-light" onclick="showRawData('${encodeURIComponent(JSON.stringify(data))}')">
                        <i class="fas fa-eye"></i> Veri
                    </button>` : ''}
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('afterbegin', resultHtml);
    
    // Ham veriyi göster
    if (data && document.getElementById('showRawData').checked) {
        updateRawDataViewer(data);
    }
}

// Ham veriyi göster
function showRawData(encodedData) {
    try {
        const data = JSON.parse(decodeURIComponent(encodedData));
        updateRawDataViewer(data);
    } catch (error) {
        console.error('Ham veri gösterilemedi:', error);
    }
}

// Ham veri görüntüleyiciyi güncelle
function updateRawDataViewer(data) {
    const viewer = document.getElementById('rawDataViewer');
    viewer.textContent = JSON.stringify(data, null, 2);
}

// İstatistikleri güncelle
function updateStats() {
    document.getElementById('successCount').textContent = testStats.successCount;
    document.getElementById('errorCount').textContent = testStats.errorCount;
    document.getElementById('totalRequests').textContent = testStats.totalRequests;
    
    // Ortalama yanıt süresi
    if (testStats.responseTimes.length > 0) {
        const avgTime = testStats.responseTimes.reduce((a, b) => a + b, 0) / testStats.responseTimes.length;
        document.getElementById('avgResponseTime').textContent = Math.round(avgTime) + 'ms';
    }
}

// İterasyon sayısını al
function getIterationCount(mode) {
    switch (mode) {
        case 'single': return 1;
        case 'multiple': return 5;
        case 'stress': return 10;
        default: return 1;
    }
}

// Sonuçları temizle
function clearResults() {
    testStats = {
        successCount: 0,
        errorCount: 0,
        totalRequests: 0,
        responseTimes: []
    };
    
    document.getElementById('testResults').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-play-circle fa-3x mb-3"></i>
            <p>Test başlatmak için yukarıdaki butonları kullanın</p>
        </div>
    `;
    
    updateStats();
}

// Ham veriyi temizle
function clearRawData() {
    document.getElementById('rawDataViewer').textContent = 'Ham veri burada görüntülenecek...';
}

// JSON formatla
function formatJson() {
    const viewer = document.getElementById('rawDataViewer');
    try {
        const data = JSON.parse(viewer.textContent);
        viewer.textContent = JSON.stringify(data, null, 2);
    } catch (error) {
        console.error('JSON formatlanamadı:', error);
    }
}

// Panoya kopyala
function copyToClipboard() {
    const viewer = document.getElementById('rawDataViewer');
    navigator.clipboard.writeText(viewer.textContent).then(() => {
        // Başarı mesajı göster
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Kopyalandı';
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
        }, 2000);
    });
}

// Proxy test sonuçlarını göster
function displayProxyTestResults(results) {
    results.forEach((result, index) => {
        const status = result.success ? 'success' : 'error';
        const message = `Proxy ${index + 1}: ${result.proxy} - ${result.success ? 'Başarılı' : 'Başarısız'}`;
        addTestResult(status, message, result.data, false, result.response_time);
    });
}

// Bekleme fonksiyonu
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
