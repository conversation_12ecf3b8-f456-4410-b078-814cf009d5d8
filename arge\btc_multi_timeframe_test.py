#!/usr/bin/env python3
"""
BTC/USDT Multi-Timeframe Analiz Testi
1d trend + 4h entry kombinasyonu ile optimize edilmiş strateji
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.multi_timeframe import MultiTimeframeAnalyzer
from app.models.config import Config

def test_multi_timeframe_strategy():
    """Multi-timeframe analiz ile BTC/USDT strateji testi"""
    
    print("🚀 BTC/USDT Multi-Timeframe Strateji Testi Başlatılıyor...")
    
    # Test parametreleri
    symbol = "BTC/USDT"
    start_date = "2025-01-01"
    initial_balance = 10000
    
    # Multi-timeframe için optimize edilmiş parametreler
    mtf_params = {
        'stopLoss': 3,          # %3 stop loss (daha sıkı)
        'takeProfit': 25,       # %25 take profit (çok yüksek hedef)
        'trailingStop': 2,      # %2 trailing stop (çok sıkı)
        'basePosition': 30,     # %30 temel pozisyon (agresif)
        'maxPosition': 50,      # %50 maksimum pozisyon (çok agresif)
        'minPosition': 20,      # %20 minimum pozisyon
        'rsiLower': 20,         # RSI alt sınır (çok geniş)
        'rsiUpper': 95,         # RSI üst sınır (çok geniş)
        'emaSpread': 0.01,      # EMA spread (çok dar)
        'momentumScore': 1,     # Momentum skoru (çok düşük - çok fazla işlem)
        'priceChange3': 0.05,   # %0.05 (çok düşük)
        'priceChange10': 0.1,   # %0.1 (çok düşük)
        'volatilityMax': 15,    # %15 maksimum volatilite (çok yüksek)
        'adxMinimum': 5,        # ADX minimum (çok düşük)
        'commission': 0.01,
        'enableShort': False,
        'dynamicTrailing': True
    }
    
    try:
        # Config ve multi-timeframe analyzer
        config = Config()
        mtf_analyzer = MultiTimeframeAnalyzer(config)
        
        print("📊 Multi-timeframe analizi yapılıyor...")
        
        # Multi-timeframe sinyali al
        mtf_result = mtf_analyzer.get_multi_timeframe_signal(symbol, start_date)
        
        print("\n" + "="*60)
        print("📈 MULTI-TIMEFRAME ANALİZ SONUÇLARI")
        print("="*60)
        
        print(f"🎯 Genel Sinyal: {mtf_result.get('signal', 0)}")
        print(f"🔥 Güven Seviyesi: {mtf_result.get('confidence', 0)}")
        print(f"💡 Öneri: {mtf_result.get('recommendation', 'HOLD')}")
        
        # Trend bilgileri
        trend_info = mtf_result.get('trend_info', {})
        print(f"\n📊 Trend Analizi (1d):")
        print(f"   • Trend: {trend_info.get('trend', 'neutral')}")
        print(f"   • Güç: {trend_info.get('strength', 0)}/100")
        print(f"   • Fiyat: ${trend_info.get('price', 0):,.2f}")
        print(f"   • EMA20: ${trend_info.get('ema_20', 0):,.2f}")
        print(f"   • EMA50: ${trend_info.get('ema_50', 0):,.2f}")
        print(f"   • MACD: {trend_info.get('macd', 0):.2f}")
        print(f"   • ADX: {trend_info.get('adx', 0):.2f}")
        
        # Entry bilgileri
        entry_info = mtf_result.get('entry_info', {})
        print(f"\n🎯 Entry Analizi (4h):")
        print(f"   • Sinyal: {entry_info.get('signal', 0)}")
        print(f"   • Güç: {entry_info.get('strength', 0)}/100")
        print(f"   • RSI: {entry_info.get('rsi', 0):.2f}")
        print(f"   • MACD: {entry_info.get('macd', 0):.2f}")
        print(f"   • Fiyat: ${entry_info.get('price', 0):,.2f}")
        
        # Destek/Direnç seviyeleri
        print("\n📊 Destek/Direnç Analizi...")
        sr_levels = mtf_analyzer.get_support_resistance_levels(symbol, start_date)
        
        if sr_levels:
            print(f"   • Mevcut Fiyat: ${sr_levels.get('current_price', 0):,.2f}")
            if sr_levels.get('nearest_resistance'):
                print(f"   • En Yakın Direnç: ${sr_levels.get('nearest_resistance'):,.2f}")
            if sr_levels.get('nearest_support'):
                print(f"   • En Yakın Destek: ${sr_levels.get('nearest_support'):,.2f}")
        
        # Simülasyon testi
        print("\n🔄 Multi-timeframe stratejisi ile simülasyon çalıştırılıyor...")
        simulation_engine = SimulationEngine(config, mtf_params)
        
        # Veri çekme
        data_fetcher = DataFetcher(config)
        df = data_fetcher.fetch_ohlcv(symbol, "1d", start_date)
        
        if df is None or len(df) < 50:
            print("❌ Yeterli veri bulunamadı!")
            return None
            
        print(f"✅ {len(df)} adet veri noktası alındı")
        
        # Simülasyonu çalıştır
        results = simulation_engine.run_simulation(df, initial_balance)
        
        # Sonuçları analiz et
        print("\n" + "="*60)
        print("📈 MULTI-TIMEFRAME STRATEJİ SONUÇLARI")
        print("="*60)
        
        print(f"💰 Başlangıç Bakiye: ${initial_balance:,.2f}")
        print(f"💰 Son Bakiye: ${results['final_balance']:,.2f}")
        print(f"📊 Toplam Kar: ${results['total_profit']:,.2f}")
        print(f"📈 Kar Oranı: %{results['profit_percentage']:.2f}")
        print(f"🔢 Toplam İşlem: {results['total_trades']}")
        print(f"✅ Kazanan İşlem: {results['winning_trades']}")
        print(f"❌ Kaybeden İşlem: {results['losing_trades']}")
        print(f"🎯 Kazanma Oranı: %{results['win_rate']:.2f}")
        print(f"📉 Maksimum Düşüş: %{results['max_drawdown']:.2f}")
        print(f"⚡ Sharpe Oranı: {results['sharpe_ratio']:.3f}")
        
        # Hedef analizi
        target_profit_pct = 10.0  # %10 hedef
        if results['profit_percentage'] >= target_profit_pct:
            print(f"\n🎉 BAŞARILI! %{target_profit_pct} hedefine ulaşıldı!")
        else:
            needed_improvement = target_profit_pct - results['profit_percentage']
            print(f"\n⚠️  Hedef için %{needed_improvement:.2f} daha kar gerekli")
        
        # Multi-timeframe vs Normal karşılaştırma
        print("\n" + "="*60)
        print("🔍 MULTI-TIMEFRAME AVANTAJLARI")
        print("="*60)
        
        if mtf_result.get('confidence', 0) > 80:
            print("✅ Yüksek güven seviyesi - Güçlü sinyal")
        elif mtf_result.get('confidence', 0) > 50:
            print("⚠️  Orta güven seviyesi - Dikkatli ol")
        else:
            print("❌ Düşük güven seviyesi - İşlem yapma")
            
        if trend_info.get('trend') in ['strong_bullish', 'bullish']:
            print("✅ Güçlü yükseliş trendi tespit edildi")
        else:
            print("⚠️  Trend belirsiz veya düşüş eğiliminde")
            
        # Öneriler
        print("\n" + "="*60)
        print("💡 MULTI-TIMEFRAME STRATEJİ ÖNERİLERİ")
        print("="*60)
        
        if results['profit_percentage'] < target_profit_pct:
            print("🔧 Önerilen iyileştirmeler:")
            
            if results['total_trades'] < 5:
                print("   • Multi-timeframe filtrelerini gevşet")
                print("   • 4h timeframe yerine 1h kullanmayı dene")
                print("   • Entry confidence eşiğini düşür")
                
            if results['win_rate'] < 50:
                print("   • Trend konfirmasyonunu güçlendir")
                print("   • Support/Resistance seviyelerini kullan")
                print("   • Volume konfirmasyonu ekle")
                
            if results['max_drawdown'] > 15:
                print("   • Position sizing'i küçült")
                print("   • Stop loss'u sıkılaştır")
                print("   • Risk management'i iyileştir")
        
        return results
        
    except Exception as e:
        print(f"❌ Hata oluştu: {str(e)}")
        return None

def compare_strategies():
    """Normal vs Multi-timeframe strateji karşılaştırması"""
    print("\n" + "="*60)
    print("⚔️  STRATEJİ KARŞILAŞTIRMASI")
    print("="*60)
    
    print("🔍 Normal Strateji:")
    print("   • Tek timeframe (1d)")
    print("   • Basit teknik indikatörler")
    print("   • Orta seviye filtreler")
    
    print("\n🚀 Multi-timeframe Strateji:")
    print("   • 1d trend + 4h entry")
    print("   • Çoklu konfirmasyon")
    print("   • Destek/Direnç analizi")
    print("   • Yüksek güven sinyalleri")

if __name__ == "__main__":
    # Multi-timeframe test
    results = test_multi_timeframe_strategy()
    
    # Strateji karşılaştırması
    compare_strategies()
    
    print("\n🏁 Multi-timeframe test tamamlandı!")
