"""
Piyasa verilerini çekmek ve işlemek için model sınıfı
"""
import requests
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional

class MarketData:
    def __init__(self, config):
        """
        MarketData sınıfının başlatıcı metodu
        
        Args:
            config: Konfigürasyon nesnesi
        """
        self.config = config
        self.base_url = config.BINANCE_API_URL
    
    def get_klines(self, symbol: str, interval: str, 
                  start_time: Optional[int] = None, 
                  end_time: Optional[int] = None,
                  limit: int = 1000) -> pd.DataFrame:
        """
        Belirli bir sembol için mum verilerini çeker
        
        Args:
            symbol: K<PERSON>to para çifti (örn: BTCUSDT)
            interval: Zaman aralığı (örn: 1h, 4h, 1d)
            start_time: Başlangıç zamanı (timestamp)
            end_time: <PERSON><PERSON><PERSON>amanı (timestamp)
            limit: Ma<PERSON><PERSON><PERSON> veri sayısı
            
        Returns:
            pandas.DataFrame: Mum verileri
        """
        endpoint = f"{self.base_url}/klines"
        
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
            
        try:
            response = requests.get(endpoint, params=params)
            response.raise_for_status()
            
            # Verileri DataFrame'e dönüştür
            df = pd.DataFrame(response.json(), columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                'taker_buy_quote', 'ignore'
            ])
            
            # Veri tiplerini düzenle
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)
                
            df.set_index('timestamp', inplace=True)
            return df
            
        except requests.exceptions.RequestException as e:
            print(f"Veri çekme hatası: {e}")
            return pd.DataFrame()
    
    def get_exchange_info(self) -> Dict:
        """
        Borsa bilgilerini getirir
        
        Returns:
            Dict: Borsa bilgileri
        """
        endpoint = f"{self.base_url}/exchangeInfo"
        
        try:
            response = requests.get(endpoint)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Borsa bilgileri alınamadı: {e}")
            return {}
    
    def get_ticker_price(self, symbol: str) -> Dict:
        """
        Belirli bir sembol için güncel fiyat bilgisini getirir
        
        Args:
            symbol: Kripto para çifti
            
        Returns:
            Dict: Fiyat bilgisi
        """
        endpoint = f"{self.base_url}/ticker/price"
        
        try:
            response = requests.get(endpoint, params={'symbol': symbol})
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Fiyat bilgisi alınamadı: {e}")
            return {} 