"""
Forex veri çekme işlemlerini yapan sınıf
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
import requests
import time
import logging
import os

class DataFetcher:
    def __init__(self, config=None):
        """
        DataFetcher sınıfının başlatıcı metodu

        Args:
            config: Konfigürasyon nesnesi (opsiyonel)
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Alpha Vantage API anahtarı (ücretsiz hesap için)
        self.alpha_vantage_key = "demo"  # Gerçek kullanım için API anahtarı alın
        self.base_url = "https://www.alphavantage.co/query"

        # Forex çiftleri ve pip değerleri
        self.forex_pairs = {
            'EURUSD': {'pip_value': 0.0001, 'name': 'EUR/USD', 'spread': 0.0001},
            'GBPUSD': {'pip_value': 0.0001, 'name': 'GBP/USD', 'spread': 0.0001},
            'USDJPY': {'pip_value': 0.01, 'name': 'USD/JPY', 'spread': 0.01},
            'AUDUSD': {'pip_value': 0.0001, 'name': 'AUD/USD', 'spread': 0.0001},
            'USDCHF': {'pip_value': 0.0001, 'name': 'USD/CHF', 'spread': 0.0001},
            'NZDUSD': {'pip_value': 0.0001, 'name': 'NZD/USD', 'spread': 0.0001},
            'USDCAD': {'pip_value': 0.0001, 'name': 'USD/CAD', 'spread': 0.0001},
            'EURGBP': {'pip_value': 0.0001, 'name': 'EUR/GBP', 'spread': 0.0001},
            'EURJPY': {'pip_value': 0.01, 'name': 'EUR/JPY', 'spread': 0.01},
            'GBPJPY': {'pip_value': 0.01, 'name': 'GBP/JPY', 'spread': 0.01}
        }

    def fetch_forex_data(self, symbol: str, start_date: str, end_date: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        Forex verilerini Alpha Vantage API'den çeker

        Args:
            symbol (str): Forex çifti (örn: 'EUR/USD')
            start_date (str): Başlangıç tarihi (YYYY-MM-DD formatında)
            end_date (str): Bitiş tarihi (YYYY-MM-DD formatında)
            timeframe (str): Zaman dilimi ('1d', '1wk', '1mo')

        Returns:
            pd.DataFrame: OHLCV verileri
        """
        try:
            # Sembol formatını düzenle (EUR/USD -> EURUSD)
            clean_symbol = symbol.replace('/', '').upper()

            if clean_symbol not in self.forex_pairs:
                self.logger.error(f"Desteklenmeyen forex çifti: {symbol}")
                return None

            self.logger.info(f"Forex verisi çekiliyor: {symbol} ({start_date} - {end_date})")

            # Alpha Vantage fonksiyonunu belirle
            if timeframe == '1d':
                function = 'FX_DAILY'
            elif timeframe == '1wk':
                function = 'FX_WEEKLY'
            elif timeframe == '1mo':
                function = 'FX_MONTHLY'
            else:
                function = 'FX_DAILY'  # Varsayılan

            # API parametreleri
            params = {
                'function': function,
                'from_symbol': clean_symbol[:3],
                'to_symbol': clean_symbol[3:],
                'apikey': self.alpha_vantage_key,
                'outputsize': 'full'
            }

            # API çağrısı
            response = requests.get(self.base_url, params=params)
            data = response.json()

            # Hata kontrolü
            if 'Error Message' in data:
                self.logger.error(f"API Hatası: {data['Error Message']}")
                return None

            if 'Note' in data:
                self.logger.warning(f"API Uyarısı: {data['Note']}")
                return None

            # Veri anahtarını bul
            time_series_key = None
            for key in data.keys():
                if 'Time Series' in key:
                    time_series_key = key
                    break

            if not time_series_key:
                self.logger.error("Zaman serisi verisi bulunamadı")
                return None

            # DataFrame'e dönüştür
            df = pd.DataFrame.from_dict(data[time_series_key], orient='index')
            df.index = pd.to_datetime(df.index)
            df = df.sort_index()

            # Sütun isimlerini düzenle
            df.columns = ['open', 'high', 'low', 'close']
            df = df.astype(float)

            # Volume sütunu ekle (forex için genellikle yok)
            df['volume'] = 0

            # Tarih aralığını filtrele
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            df = df[(df.index >= start_dt) & (df.index <= end_dt)]

            self.logger.info(f"Forex verisi başarıyla çekildi: {len(df)} satır")
            return df

        except Exception as e:
            self.logger.error(f"Forex veri çekme hatası: {str(e)}")
            return None

    def fetch_ohlcv(self, symbol: str, timeframe: str, start_date: str, end_date: str = None) -> pd.DataFrame:
        """
        OHLCV verilerini çeker (geriye dönük uyumluluk için)
        """
        return self.fetch_forex_data(symbol, start_date, end_date, timeframe)

    def fetch_ticker(self, symbol: str) -> Dict:
        """
        Güncel forex fiyat bilgilerini çeker

        Args:
            symbol (str): Forex çifti (örn: 'EUR/USD')

        Returns:
            Dict: Güncel fiyat bilgileri
        """
        try:
            # Sembol formatını düzenle
            clean_symbol = symbol.replace('/', '').upper()

            if clean_symbol not in self.forex_pairs:
                return None

            # Alpha Vantage'den güncel kur bilgisi çek
            params = {
                'function': 'CURRENCY_EXCHANGE_RATE',
                'from_currency': clean_symbol[:3],
                'to_currency': clean_symbol[3:],
                'apikey': self.alpha_vantage_key
            }

            response = requests.get(self.base_url, params=params)
            data = response.json()

            if 'Realtime Currency Exchange Rate' in data:
                rate_data = data['Realtime Currency Exchange Rate']
                rate = float(rate_data['5. Exchange Rate'])
                spread = self.forex_pairs[clean_symbol]['spread']

                return {
                    'symbol': symbol,
                    'last': rate,
                    'bid': rate - spread/2,
                    'ask': rate + spread/2,
                    'volume': 0,  # Forex'te volume genellikle yok
                    'change': 0,  # Değişim hesaplanabilir
                    'high': rate,
                    'low': rate
                }

            return None

        except Exception as e:
            self.logger.error(f"Ticker çekme hatası: {e}")
            return None

    def get_available_symbols(self) -> List[str]:
        """
        Kullanılabilir forex çiftlerini döndürür

        Returns:
            List[str]: Kullanılabilir forex çiftleri listesi
        """
        return [pair_info['name'] for pair_info in self.forex_pairs.values()]

    def get_forex_pairs(self) -> List[Tuple[str, str]]:
        """
        Desteklenen forex çiftlerini döndür
        """
        return [(key, value['name']) for key, value in self.forex_pairs.items()]

    def get_pip_value(self, symbol: str) -> float:
        """
        Forex çifti için pip değerini döndür
        """
        clean_symbol = symbol.replace('/', '').upper()
        return self.forex_pairs.get(clean_symbol, {}).get('pip_value', 0.0001)

    def get_spread(self, symbol: str) -> float:
        """
        Forex çifti için spread değerini döndür
        """
        clean_symbol = symbol.replace('/', '').upper()
        return self.forex_pairs.get(clean_symbol, {}).get('spread', 0.0001)

    def get_timeframes(self) -> List[str]:
        """
        Kullanılabilir zaman dilimlerini döndürür

        Returns:
            List[str]: Kullanılabilir zaman dilimleri listesi
        """
        return ['1d', '1wk', '1mo']

    def save_data_to_csv(self, data: pd.DataFrame, symbol: str, timeframe: str = '1d') -> str:
        """
        Veriyi CSV dosyasına kaydet
        """
        try:
            # Dosya yolu oluştur
            data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
            os.makedirs(data_dir, exist_ok=True)

            filename = f"{symbol.replace('/', '_')}_{timeframe}.csv"
            filepath = os.path.join(data_dir, filename)

            # CSV'ye kaydet
            data.to_csv(filepath)
            self.logger.info(f"Veri kaydedildi: {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Veri kaydetme hatası: {str(e)}")
            return None

    def load_data_from_csv(self, symbol: str, timeframe: str = '1d') -> pd.DataFrame:
        """
        CSV dosyasından veri yükle
        """
        try:
            data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
            filename = f"{symbol.replace('/', '_')}_{timeframe}.csv"
            filepath = os.path.join(data_dir, filename)

            if not os.path.exists(filepath):
                self.logger.warning(f"Dosya bulunamadı: {filepath}")
                return None

            data = pd.read_csv(filepath, index_col=0, parse_dates=True)
            self.logger.info(f"Veri yüklendi: {len(data)} satır")
            return data

        except Exception as e:
            self.logger.error(f"Veri yükleme hatası: {str(e)}")
            return None