"""
Veri çekme işlemlerini yapan sınıf
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
import ccxt
import time

class DataFetcher:
    def __init__(self, config):
        """
        DataFetcher sınıfının başlatıcı metodu
        
        Args:
            config: Konfigürasyon nesnesi
        """
        self.config = config
        self.exchange = ccxt.binance({
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future'
            }
        })
        
    def fetch_ohlcv(self, symbol: str, timeframe: str, start_date: str, end_date: str = None) -> pd.DataFrame:
        """
        OHLCV (Open, High, Low, Close, Volume) verilerini çeker
        
        Args:
            symbol (str): <PERSON><PERSON><PERSON> para sembolü (örn: 'BTC/USDT')
            timeframe (str): Zaman dilimi (örn: '1h', '4h', '1d')
            start_date (str): <PERSON><PERSON>lang<PERSON><PERSON> tarihi (YYYY-MM-DD formatında)
            end_date (str): <PERSON>iş tarihi (YYYY-MM-DD formatında)
            
        Returns:
            pd.DataFrame: OHLCV verileri
        """
        # Tarihleri timestamp'e çevir
        start_timestamp = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
        end_timestamp = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000) if end_date else int(time.time() * 1000)
        
        # Verileri çek
        ohlcv = []
        current_timestamp = start_timestamp
        
        while current_timestamp < end_timestamp:
            try:
                # Rate limit'i aşmamak için bekle
                time.sleep(self.exchange.rateLimit / 1000)
                
                # Verileri çek
                data = self.exchange.fetch_ohlcv(
                    symbol,
                    timeframe,
                    current_timestamp,
                    limit=1000
                )
                
                if not data:
                    break
                
                ohlcv.extend(data)
                current_timestamp = data[-1][0] + 1
                
            except Exception as e:
                print(f"Veri çekme hatası: {e}")
                break
        
        # DataFrame oluştur
        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        return df
    
    def fetch_ticker(self, symbol: str) -> Dict:
        """
        Güncel fiyat bilgilerini çeker
        
        Args:
            symbol (str): Kripto para sembolü (örn: 'BTC/USDT')
            
        Returns:
            Dict: Güncel fiyat bilgileri
        """
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            return {
                'symbol': symbol,
                'last': ticker['last'],
                'bid': ticker['bid'],
                'ask': ticker['ask'],
                'volume': ticker['baseVolume'],
                'change': ticker['percentage'],
                'high': ticker['high'],
                'low': ticker['low']
            }
        except Exception as e:
            print(f"Ticker çekme hatası: {e}")
            return None
    
    def get_available_symbols(self) -> List[str]:
        """
        Kullanılabilir sembolleri döndürür
        
        Returns:
            List[str]: Kullanılabilir semboller listesi
        """
        try:
            markets = self.exchange.load_markets()
            return [symbol for symbol in markets.keys() if symbol.endswith('/USDT')]
        except Exception as e:
            print(f"Sembol listesi çekme hatası: {e}")
            return []
    
    def get_timeframes(self) -> List[str]:
        """
        Kullanılabilir zaman dilimlerini döndürür
        
        Returns:
            List[str]: Kullanılabilir zaman dilimleri listesi
        """
        return ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'] 