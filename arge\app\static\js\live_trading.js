// Canlı Trading JavaScript

let portfolio = {
    balance: 10000,
    totalProfit: 0,
    openTrades: [],
    closedTrades: [],
    winRate: 0
};

let currentPrices = {};
let signals = {};

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    initializeLiveTrading();
    loadPortfolio();
    startPriceUpdates();
    
    // Event listeners
    document.getElementById('tradeForm').addEventListener('submit', handleNewTrade);
    document.getElementById('closeTradeForm').addEventListener('submit', handleCloseTrade);
});

// Canlı trading sistemini başlat
function initializeLiveTrading() {
    updateLastUpdateTime();
    loadSignals();
    updatePortfolioDisplay();
    updateOpenPositionsTable();
    updateTradeHistoryTable();
    
    console.log('🎯 Canlı Trading sistemi başlatıldı');
}

// Portföyü localStorage'dan yükle
function loadPortfolio() {
    const savedPortfolio = localStorage.getItem('livePortfolio');
    if (savedPortfolio) {
        portfolio = JSON.parse(savedPortfolio);
        console.log('💰 Portföy yüklendi:', portfolio);
    }
}

// Portföyü kaydet
function savePortfolio() {
    localStorage.setItem('livePortfolio', JSON.stringify(portfolio));
}

// Fiyat güncellemelerini başlat
function startPriceUpdates() {
    // İlk yükleme
    updatePrices();
    
    // Her 30 saniyede bir güncelle
    setInterval(updatePrices, 30000);
}

// Fiyatları güncelle
async function updatePrices() {
    const symbols = ['EUR/USD', 'GBP/USD', 'USD/JPY', 'AUD/USD'];
    
    for (const symbol of symbols) {
        try {
            // Gerçek API yerine simüle edilmiş fiyat
            const price = await getLatestPrice(symbol);
            currentPrices[symbol] = price;
            
            // Sinyal hesapla
            const signal = await calculateSignal(symbol);
            signals[symbol] = signal;
            
        } catch (error) {
            console.error(`Fiyat güncelleme hatası (${symbol}):`, error);
        }
    }
    
    updateSignalsDisplay();
    updateOpenPositionsPnL();
    updateLastUpdateTime();
}

// Son fiyatı al (simüle edilmiş)
async function getLatestPrice(symbol) {
    // Gerçek uygulamada burası API çağrısı olacak
    const basePrice = {
        'EUR/USD': 1.0850,
        'GBP/USD': 1.2650,
        'USD/JPY': 142.50,
        'AUD/USD': 0.6750
    };
    
    // Rastgele fiyat hareketi simülasyonu
    const variation = (Math.random() - 0.5) * 0.002; // ±0.2% hareket
    return basePrice[symbol] * (1 + variation);
}

// Sinyal hesapla
async function calculateSignal(symbol) {
    try {
        const response = await fetch('/api/get-live-signal', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                symbol: symbol,
                timeframe: '1h'
            })
        });
        
        if (response.ok) {
            return await response.json();
        } else {
            // Fallback: basit sinyal
            return generateSimpleSignal(symbol);
        }
    } catch (error) {
        console.error('Sinyal hesaplama hatası:', error);
        return generateSimpleSignal(symbol);
    }
}

// Basit sinyal üret
function generateSimpleSignal(symbol) {
    const price = currentPrices[symbol];
    const random = Math.random();
    
    if (random > 0.6) {
        return {
            signal: 'BUY',
            strength: Math.floor(random * 100),
            reason: 'Teknik analiz pozitif',
            target: price * 1.01,
            stopLoss: price * 0.99
        };
    } else if (random < 0.4) {
        return {
            signal: 'SELL',
            strength: Math.floor((1 - random) * 100),
            reason: 'Teknik analiz negatif',
            target: price * 0.99,
            stopLoss: price * 1.01
        };
    } else {
        return {
            signal: 'HOLD',
            strength: 50,
            reason: 'Belirsiz trend',
            target: price,
            stopLoss: price
        };
    }
}

// Sinyalleri görüntüle
function updateSignalsDisplay() {
    const container = document.getElementById('signalsContainer');
    container.innerHTML = '';
    
    Object.keys(signals).forEach(symbol => {
        const signal = signals[symbol];
        const price = currentPrices[symbol];
        
        const signalCard = createSignalCard(symbol, price, signal);
        container.appendChild(signalCard);
    });
}

// Sinyal kartı oluştur
function createSignalCard(symbol, price, signal) {
    const col = document.createElement('div');
    col.className = 'col-md-3 mb-3';
    
    const signalClass = signal.signal === 'BUY' ? 'signal-buy' : 
                       signal.signal === 'SELL' ? 'signal-sell' : 'signal-hold';
    
    const icon = signal.signal === 'BUY' ? 'fa-arrow-up' : 
                 signal.signal === 'SELL' ? 'fa-arrow-down' : 'fa-minus';
    
    col.innerHTML = `
        <div class="signal-card ${signalClass} p-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0">${symbol}</h6>
                <i class="fas ${icon} fa-lg"></i>
            </div>
            <div class="mb-2">
                <div class="h5 mb-1">${price ? price.toFixed(5) : '--'}</div>
                <div class="small">Mevcut Fiyat</div>
            </div>
            <div class="mb-2">
                <div class="h6 mb-1">${signal.signal}</div>
                <div class="small">Güç: ${signal.strength}%</div>
            </div>
            <div class="small mb-2">${signal.reason}</div>
            <button class="btn btn-light btn-sm w-100" onclick="quickTrade('${symbol}', '${signal.signal}')">
                <i class="fas fa-bolt"></i> Hızlı İşlem
            </button>
        </div>
    `;
    
    return col;
}

// Hızlı işlem
function quickTrade(symbol, signal) {
    if (signal === 'HOLD') {
        alert('Bu sembol için şu anda işlem önerisi bulunmuyor.');
        return;
    }
    
    // Formu otomatik doldur
    document.getElementById('tradeSymbol').value = symbol;
    document.getElementById('tradeType').value = signal;
    document.getElementById('tradeAmount').value = '0.1';
    document.getElementById('tradePrice').value = currentPrices[symbol].toFixed(5);
    
    // Önerilen stop loss ve take profit
    const signalData = signals[symbol];
    if (signalData.stopLoss) {
        document.getElementById('stopLoss').value = signalData.stopLoss.toFixed(5);
    }
    if (signalData.target) {
        document.getElementById('takeProfit').value = signalData.target.toFixed(5);
    }
    
    // Forma scroll
    document.getElementById('tradeForm').scrollIntoView({ behavior: 'smooth' });
}

// Yeni işlem kaydet
async function handleNewTrade(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const trade = {
        id: Date.now(),
        symbol: formData.get('tradeSymbol') || document.getElementById('tradeSymbol').value,
        type: formData.get('tradeType') || document.getElementById('tradeType').value,
        amount: parseFloat(document.getElementById('tradeAmount').value),
        entryPrice: parseFloat(document.getElementById('tradePrice').value),
        stopLoss: parseFloat(document.getElementById('stopLoss').value) || null,
        takeProfit: parseFloat(document.getElementById('takeProfit').value) || null,
        openTime: new Date(),
        status: 'OPEN'
    };
    
    // Validasyon
    if (!trade.symbol || !trade.type || !trade.amount || !trade.entryPrice) {
        alert('Lütfen tüm gerekli alanları doldurun.');
        return;
    }
    
    // Margin kontrolü
    const requiredMargin = calculateRequiredMargin(trade);
    if (requiredMargin > portfolio.balance) {
        alert(`Yetersiz bakiye! Gerekli margin: $${requiredMargin.toFixed(2)}`);
        return;
    }
    
    // İşlemi ekle
    portfolio.openTrades.push(trade);
    portfolio.balance -= requiredMargin; // Margin'i düş
    
    savePortfolio();
    updatePortfolioDisplay();
    updateOpenPositionsTable();
    updateCloseTradeOptions();
    
    // Formu temizle
    event.target.reset();
    
    alert(`İşlem başarıyla açıldı! ID: ${trade.id}`);
    console.log('📈 Yeni işlem açıldı:', trade);
}

// Gerekli margin hesapla
function calculateRequiredMargin(trade) {
    const leverage = 100; // 1:100 kaldıraç
    const lotSize = 100000; // 1 lot = 100,000 birim
    const positionValue = trade.amount * lotSize;
    return positionValue / leverage;
}

// İşlem kapat
async function handleCloseTrade(event) {
    event.preventDefault();
    
    const tradeId = parseInt(document.getElementById('closeTradeId').value);
    const closePrice = parseFloat(document.getElementById('closePrice').value);
    const closeReason = document.getElementById('closeReason').value;
    
    if (!tradeId || !closePrice) {
        alert('Lütfen tüm alanları doldurun.');
        return;
    }
    
    // İşlemi bul
    const tradeIndex = portfolio.openTrades.findIndex(t => t.id === tradeId);
    if (tradeIndex === -1) {
        alert('İşlem bulunamadı.');
        return;
    }
    
    const trade = portfolio.openTrades[tradeIndex];
    
    // Kar/zarar hesapla
    const pnl = calculatePnL(trade, closePrice);
    
    // İşlemi kapat
    trade.closePrice = closePrice;
    trade.closeTime = new Date();
    trade.closeReason = closeReason;
    trade.pnl = pnl;
    trade.status = 'CLOSED';
    
    // Margin'i geri ver ve kar/zararı ekle
    const margin = calculateRequiredMargin(trade);
    portfolio.balance += margin + pnl;
    portfolio.totalProfit += pnl;
    
    // İşlemi geçmişe taşı
    portfolio.closedTrades.push(trade);
    portfolio.openTrades.splice(tradeIndex, 1);
    
    // Kazanma oranını güncelle
    updateWinRate();
    
    savePortfolio();
    updatePortfolioDisplay();
    updateOpenPositionsTable();
    updateTradeHistoryTable();
    updateCloseTradeOptions();
    
    // Formu temizle
    event.target.reset();
    
    const pnlText = pnl >= 0 ? `+$${pnl.toFixed(2)}` : `-$${Math.abs(pnl).toFixed(2)}`;
    alert(`İşlem kapatıldı! Kar/Zarar: ${pnlText}`);
    console.log('📉 İşlem kapatıldı:', trade);
}

// Kar/zarar hesapla
function calculatePnL(trade, currentPrice) {
    const lotSize = 100000;
    const positionSize = trade.amount * lotSize;
    
    let priceDiff;
    if (trade.type === 'BUY') {
        priceDiff = currentPrice - trade.entryPrice;
    } else {
        priceDiff = trade.entryPrice - currentPrice;
    }
    
    // JPY çiftleri için özel hesaplama
    if (trade.symbol.includes('JPY')) {
        return (priceDiff * positionSize) / 100;
    } else {
        return priceDiff * positionSize;
    }
}

// Kazanma oranını güncelle
function updateWinRate() {
    const totalTrades = portfolio.closedTrades.length;
    if (totalTrades === 0) {
        portfolio.winRate = 0;
        return;
    }
    
    const winningTrades = portfolio.closedTrades.filter(t => t.pnl > 0).length;
    portfolio.winRate = (winningTrades / totalTrades) * 100;
}

// Portföy görünümünü güncelle
function updatePortfolioDisplay() {
    document.getElementById('totalBalance').textContent = `$${portfolio.balance.toFixed(2)}`;
    
    const profitElement = document.getElementById('totalProfit');
    const profitText = portfolio.totalProfit >= 0 ? `+$${portfolio.totalProfit.toFixed(2)}` : `-$${Math.abs(portfolio.totalProfit).toFixed(2)}`;
    profitElement.textContent = profitText;
    profitElement.className = portfolio.totalProfit >= 0 ? 'metric-value profit-positive' : 'metric-value profit-negative';
    
    document.getElementById('openPositions').textContent = portfolio.openTrades.length;
    document.getElementById('winRate').textContent = `${portfolio.winRate.toFixed(1)}%`;
}

// Açık pozisyonlar tablosunu güncelle
function updateOpenPositionsTable() {
    const tbody = document.getElementById('openPositionsTable');
    tbody.innerHTML = '';
    
    if (portfolio.openTrades.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">Açık pozisyon bulunmuyor</td></tr>';
        return;
    }
    
    portfolio.openTrades.forEach(trade => {
        const currentPrice = currentPrices[trade.symbol] || trade.entryPrice;
        const pnl = calculatePnL(trade, currentPrice);
        const pnlClass = pnl >= 0 ? 'profit-positive' : 'profit-negative';
        const pnlText = pnl >= 0 ? `+$${pnl.toFixed(2)}` : `-$${Math.abs(pnl).toFixed(2)}`;
        
        const row = document.createElement('tr');
        row.className = 'trade-row';
        row.innerHTML = `
            <td>${trade.id}</td>
            <td>${trade.symbol}</td>
            <td><span class="badge bg-${trade.type === 'BUY' ? 'success' : 'danger'}">${trade.type}</span></td>
            <td>${trade.amount}</td>
            <td>${trade.entryPrice.toFixed(5)}</td>
            <td>${currentPrice.toFixed(5)}</td>
            <td class="${pnlClass}">${pnlText}</td>
            <td>${trade.stopLoss ? trade.stopLoss.toFixed(5) : '--'}</td>
            <td>${trade.takeProfit ? trade.takeProfit.toFixed(5) : '--'}</td>
            <td>
                <button class="btn btn-sm btn-warning" onclick="quickClose(${trade.id})">
                    <i class="fas fa-times"></i> Kapat
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Açık pozisyonların kar/zararını güncelle
function updateOpenPositionsPnL() {
    updateOpenPositionsTable();
}

// Hızlı kapatma
function quickClose(tradeId) {
    const trade = portfolio.openTrades.find(t => t.id === tradeId);
    if (!trade) return;
    
    const currentPrice = currentPrices[trade.symbol];
    if (!currentPrice) {
        alert('Mevcut fiyat alınamadı. Lütfen manuel olarak fiyat girin.');
        return;
    }
    
    // Kapatma formunu doldur
    document.getElementById('closeTradeId').value = tradeId;
    document.getElementById('closePrice').value = currentPrice.toFixed(5);
    
    // Forma scroll
    document.getElementById('closeTradeForm').scrollIntoView({ behavior: 'smooth' });
}

// İşlem geçmişi tablosunu güncelle
function updateTradeHistoryTable() {
    const tbody = document.getElementById('tradeHistoryTable');
    tbody.innerHTML = '';
    
    if (portfolio.closedTrades.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">Henüz işlem geçmişi bulunmuyor</td></tr>';
        return;
    }
    
    // Son 10 işlemi göster
    const recentTrades = portfolio.closedTrades.slice(-10).reverse();
    
    recentTrades.forEach(trade => {
        const duration = Math.floor((new Date(trade.closeTime) - new Date(trade.openTime)) / (1000 * 60)); // dakika
        const pnlClass = trade.pnl >= 0 ? 'profit-positive' : 'profit-negative';
        const pnlText = trade.pnl >= 0 ? `+$${trade.pnl.toFixed(2)}` : `-$${Math.abs(trade.pnl).toFixed(2)}`;
        const statusClass = trade.pnl >= 0 ? 'success' : 'danger';
        const statusText = trade.pnl >= 0 ? 'Kar' : 'Zarar';
        
        const row = document.createElement('tr');
        row.className = 'trade-row';
        row.innerHTML = `
            <td>${trade.id}</td>
            <td>${new Date(trade.openTime).toLocaleDateString('tr-TR')}</td>
            <td>${trade.symbol}</td>
            <td><span class="badge bg-${trade.type === 'BUY' ? 'success' : 'danger'}">${trade.type}</span></td>
            <td>${trade.amount}</td>
            <td>${trade.entryPrice.toFixed(5)}</td>
            <td>${trade.closePrice.toFixed(5)}</td>
            <td class="${pnlClass}">${pnlText}</td>
            <td>${duration} dk</td>
            <td><span class="badge bg-${statusClass}">${statusText}</span></td>
        `;
        tbody.appendChild(row);
    });
}

// Kapatma seçeneklerini güncelle
function updateCloseTradeOptions() {
    const select = document.getElementById('closeTradeId');
    select.innerHTML = '<option value="">Açık pozisyon seçiniz...</option>';
    
    portfolio.openTrades.forEach(trade => {
        const option = document.createElement('option');
        option.value = trade.id;
        option.textContent = `${trade.id} - ${trade.symbol} ${trade.type} ${trade.amount}`;
        select.appendChild(option);
    });
}

// Son güncelleme zamanını güncelle
function updateLastUpdateTime() {
    const now = new Date();
    document.getElementById('lastUpdate').textContent = now.toLocaleTimeString('tr-TR');
}

// Sinyalleri yükle
function loadSignals() {
    // İlk yükleme için boş sinyaller
    const symbols = ['EUR/USD', 'GBP/USD', 'USD/JPY', 'AUD/USD'];
    symbols.forEach(symbol => {
        signals[symbol] = {
            signal: 'HOLD',
            strength: 50,
            reason: 'Veri yükleniyor...',
            target: 0,
            stopLoss: 0
        };
    });
}
