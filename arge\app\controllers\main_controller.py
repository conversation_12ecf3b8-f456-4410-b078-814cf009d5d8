"""
Ana controller sınıfı
"""
import os
import json
import pickle
import pandas as pd
from datetime import datetime
from flask import render_template, jsonify, request, session
from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.config import Config

class MainController:
    def __init__(self):
        """
        MainController sınıfının başlatıcı metodu
        """
        self.config = Config()
        self.data_fetcher = DataFetcher(self.config)
        self.simulation_engine = SimulationEngine(self.config)
        
    def index(self):
        """
        Ana sayfa
        
        Returns:
            str: HTML sayfası
        """
        return render_template('index.html')
    
    def get_market_data(self):
        """
        Piyasa verilerini döndürür
        
        Returns:
            dict: <PERSON>yasa verileri
        """
        symbol = request.args.get('symbol', self.config.get('DEFAULT_SYMBOL'))
        timeframe = request.args.get('timeframe', self.config.get('DEFAULT_TIMEFRAME'))
        start_date = request.args.get('start_date', self.config.get('DEFAULT_START_DATE'))
        
        df = self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)
        
        return jsonify({
            'symbol': symbol,
            'timeframe': timeframe,
            'data': df.to_dict(orient='records')
        })
    
    def get_exchange_info(self):
        """
        Borsa bilgilerini döndürür
        
        Returns:
            dict: Borsa bilgileri
        """
        symbols = self.data_fetcher.get_available_symbols()
        timeframes = self.data_fetcher.get_timeframes()
        
        return jsonify({
            'symbols': symbols,
            'timeframes': timeframes
        })
    
    def run_simulation(self):
        """
        Simülasyonu çalıştırır
        
        Returns:
            dict: Simülasyon sonuçları
        """
        try:
            data = request.get_json()

            if not data:
                return jsonify({'error': 'Geçersiz istek verisi'}), 400

            symbol = data.get('symbol')
            timeframe = data.get('timeframe')
            start_date = data.get('start_date')
            initial_balance = data.get('initial_balance')
            request_strategy_settings = data.get('strategy_settings')  # Request'ten gelen strateji
            
            # Parametreleri doğrula
            if not all([symbol, timeframe, start_date, initial_balance]):
                return jsonify({'error': 'Eksik parametreler'}), 400
            
            try:
                initial_balance = float(initial_balance)
                if initial_balance <= 0:
                    return jsonify({'error': 'Başlangıç bakiyesi pozitif olmalıdır'}), 400
            except ValueError:
                return jsonify({'error': 'Geçersiz başlangıç bakiyesi'}), 400
            
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Geçersiz tarih formatı'}), 400
            
            # Veri al (yerel veri seti varsa onu kullan)
            df = self._get_data_for_simulation(symbol, timeframe, start_date)
            
            if df.empty:
                return jsonify({'error': 'Veri bulunamadı'}), 404
            
            # Strateji ayarlarını belirle (öncelik: request > session > varsayılan)
            strategy_settings = None
            if request_strategy_settings:
                strategy_settings = request_strategy_settings
                print(f"Request'ten strateji kullanılıyor: {symbol} - Momentum: {strategy_settings.get('momentumScore', 'N/A')}")
            else:
                strategy_settings = session.get('strategy_settings', None)
                if strategy_settings:
                    print(f"Session'dan strateji kullanılıyor: {symbol}")
                else:
                    print(f"Varsayılan strateji kullanılıyor: {symbol}")

            # Strateji ayarları varsa yeni simulation engine oluştur
            if strategy_settings:
                simulation_engine = SimulationEngine(self.config, strategy_settings)
            else:
                simulation_engine = self.simulation_engine

            # Simülasyonu çalıştır
            results = simulation_engine.run_simulation(df, initial_balance)

            # Fiyat verilerini ekle
            results['prices'] = df['close'].tolist()

            # Kullanıcı parametrelerini sonuçlara ekle
            results['symbol'] = symbol
            results['timeframe'] = timeframe
            results['start_date'] = start_date
            results['initial_balance'] = initial_balance

            return jsonify(results)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def get_performance(self):
        """
        Performans metriklerini döndürür
        
        Returns:
            dict: Performans metrikleri
        """
        data = request.get_json()
        results = data.get('results', {})
        
        metrics = self.simulation_engine.get_performance_metrics(results)
        
        return jsonify(metrics)
    
    def get_trade_history(self):
        """
        İşlem geçmişini döndürür
        
        Returns:
            dict: İşlem geçmişi
        """
        data = request.get_json()
        results = data.get('results', {})
        
        trades = self.simulation_engine.get_trade_history(results)
        
        return jsonify(trades)

    def strategy_page(self):
        """
        Strateji ayarları sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('strategy.html')

    def save_strategy(self):
        """
        Strateji ayarlarını kaydeder

        Returns:
            dict: Kaydetme sonucu
        """
        try:
            data = request.get_json()

            if not data:
                return jsonify({'success': False, 'error': 'Geçersiz veri'}), 400

            # Strateji ayarlarını doğrula
            required_fields = [
                'stopLoss', 'takeProfit', 'trailingStop', 'basePosition',
                'maxPosition', 'minPosition', 'rsiLower', 'rsiUpper',
                'emaSpread', 'momentumScore', 'priceChange3', 'priceChange10',
                'volatilityMax', 'commission'
            ]

            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Eksik alan: {field}'}), 400

            # Strateji ayarlarını session'a kaydet (gerçek uygulamada veritabanına kaydedilir)
            session['strategy_settings'] = data

            return jsonify({'success': True, 'message': 'Strateji başarıyla kaydedildi'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def get_strategy(self):
        """
        Kaydedilmiş strateji ayarlarını döndürür

        Returns:
            dict: Strateji ayarları
        """
        strategy_settings = session.get('strategy_settings', {})
        return jsonify(strategy_settings)

    def bulk_analysis_page(self):
        """
        Toplu analiz sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('bulk_analysis.html')

    def generate_ai_strategy(self):
        """
        Toplu analiz sonuçlarından AI strateji önerisi oluşturur

        Returns:
            dict: AI strateji önerisi
        """
        try:
            data = request.get_json()
            analysis_results = data.get('analysis_results', [])
            iteration = data.get('iteration', 1)
            optimization_history = data.get('optimization_history', [])

            if not analysis_results:
                return jsonify({'error': 'Analiz sonuçları bulunamadı'}), 400

            # Başarılı sonuçları filtrele
            successful_results = [r for r in analysis_results if not r.get('error')]

            if len(successful_results) < 1:  # Döngüsel optimizasyon için daha esnek
                return jsonify({'error': 'Hiç başarılı analiz sonucu yok'}), 400

            # Döngüsel AI analizi yap
            try:
                ai_strategy = self._analyze_and_generate_cyclic_strategy(
                    successful_results, iteration, optimization_history
                )
            except Exception as e:
                # Hata durumunda basit strateji döndür
                print(f"Döngüsel AI strateji hatası: {e}")
                ai_strategy = self._analyze_and_generate_strategy(successful_results)
                ai_strategy['iteration'] = iteration
                ai_strategy['fallback'] = True

            return jsonify(ai_strategy)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def _analyze_and_generate_strategy(self, results):
        """
        Analiz sonuçlarından strateji parametreleri oluşturur

        Args:
            results: Başarılı analiz sonuçları listesi

        Returns:
            dict: AI strateji önerisi
        """
        # Karlı sonuçları al
        profitable_results = [r for r in results if r.get('profit_percentage', 0) > 0]

        # İstatistikleri hesapla
        total_results = len(results)
        profitable_count = len(profitable_results)
        profitability_rate = profitable_count / total_results if total_results > 0 else 0

        avg_profit = sum(r.get('profit_percentage', 0) for r in profitable_results) / len(profitable_results) if profitable_results else 0
        avg_win_rate = sum(r.get('win_rate', 0) for r in results) / len(results) if results else 0
        avg_trades = sum(r.get('total_trades', 0) for r in results) / len(results) if results else 0
        avg_sharpe = sum(r.get('sharpe_ratio', 0) for r in results) / len(results) if results else 0
        avg_drawdown = sum(r.get('max_drawdown', 0) for r in results) / len(results) if results else 0

        # Gelişmiş strateji parametrelerini belirle (tüm indikatörler dahil)
        strategy_params = self._determine_advanced_strategy_parameters(
            profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown
        )

        # Analiz metni oluştur
        analysis_text = self._generate_analysis_text(
            total_results, profitable_count, profitability_rate, avg_profit, avg_win_rate, avg_trades
        )

        return {
            'analysis': analysis_text,
            'parameters': strategy_params,
            'expected': {
                'winRate': f"{avg_win_rate * 100:.1f}",
                'avgProfit': f"{avg_profit:.1f}",
                'riskLevel': self._determine_risk_level(avg_drawdown, avg_sharpe)
            }
        }

    def _determine_strategy_parameters(self, profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown):
        """
        İstatistiklere göre strateji parametrelerini belirler
        """
        # Temel parametreler
        params = {
            'stopLoss': 4,
            'takeProfit': 8,
            'trailingStop': 3,
            'basePosition': 15,
            'maxPosition': 25,
            'minPosition': 8,
            'rsiLower': 55,
            'rsiUpper': 75,
            'emaSpread': 0.2,
            'momentumScore': 5,
            'priceChange3': 0.5,
            'priceChange10': 1.0,
            'volatilityMax': 4,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

        # Karlılık oranına göre ayarlama
        if profitability_rate > 0.7:  # %70+ karlı
            # Agresif strateji
            params['takeProfit'] = 12
            params['basePosition'] = 20
            params['maxPosition'] = 30
            params['momentumScore'] = 4
        elif profitability_rate == 0:  # %0 karlı (tüm zararlı)
            # Radikal değişiklik gerekli
            params['stopLoss'] = 2  # Çok dar stop loss
            params['takeProfit'] = 4  # Düşük hedef
            params['basePosition'] = 8  # Küçük pozisyon
            params['maxPosition'] = 12
            params['momentumScore'] = 3  # Daha az seçici
            params['rsiLower'] = 45  # Daha geniş RSI aralığı
            params['rsiUpper'] = 80
            params['volatilityMax'] = 6  # Daha yüksek volatilite toleransı
            params['priceChange3'] = 0.2  # Daha düşük momentum eşiği
            params['priceChange10'] = 0.5
            params['emaSpread'] = 0.1  # Daha düşük trend eşiği
        elif profitability_rate < 0.4:  # %40- karlı
            # Konservatif strateji
            params['stopLoss'] = 3
            params['takeProfit'] = 6
            params['basePosition'] = 10
            params['maxPosition'] = 15
            params['momentumScore'] = 6
            params['volatilityMax'] = 2

        # Kazanma oranına göre ayarlama
        if avg_win_rate < 0.4:  # Düşük kazanma oranı
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['takeProfit'] = min(15, params['takeProfit'] + 2)
        elif avg_win_rate > 0.6:  # Yüksek kazanma oranı
            params['takeProfit'] = max(6, params['takeProfit'] - 2)
            params['basePosition'] = min(25, params['basePosition'] + 5)

        # İşlem sayısına göre ayarlama
        if avg_trades > 50:  # Çok fazla işlem
            params['momentumScore'] = min(6, params['momentumScore'] + 1)
            params['volatilityMax'] = max(2, params['volatilityMax'] - 1)
        elif avg_trades < 5:  # Çok az işlem (kritik durum)
            # Radikal değişiklik - daha fazla işlem için
            params['momentumScore'] = max(2, params['momentumScore'] - 2)
            params['volatilityMax'] = min(10, params['volatilityMax'] + 3)
            params['rsiLower'] = max(30, params['rsiLower'] - 10)
            params['rsiUpper'] = min(85, params['rsiUpper'] + 10)
            params['priceChange3'] = max(0.1, params['priceChange3'] - 0.3)
            params['priceChange10'] = max(0.2, params['priceChange10'] - 0.5)
            params['emaSpread'] = max(0.05, params['emaSpread'] - 0.1)
        elif avg_trades < 10:  # Az işlem
            params['momentumScore'] = max(3, params['momentumScore'] - 1)
            params['volatilityMax'] = min(8, params['volatilityMax'] + 1)

        # Sharpe oranına göre ayarlama
        if avg_sharpe < 0:  # Negatif Sharpe
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['basePosition'] = max(8, params['basePosition'] - 3)

        # Drawdown'a göre ayarlama
        if avg_drawdown > 0.2:  # Yüksek drawdown
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['trailingStop'] = max(1, params['trailingStop'] - 1)
            params['basePosition'] = max(8, params['basePosition'] - 5)

        return params

    def _determine_advanced_strategy_parameters(self, profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown):
        """
        Gelişmiş indikatörler dahil tüm strateji parametrelerini belirler
        """
        # Temel parametreler
        params = {
            'stopLoss': 4,
            'takeProfit': 8,
            'trailingStop': 3,
            'basePosition': 15,
            'maxPosition': 25,
            'minPosition': 8,
            'rsiLower': 55,
            'rsiUpper': 75,
            'emaSpread': 0.2,
            'momentumScore': 8,  # 12 puan sisteminde
            'priceChange3': 0.5,
            'priceChange10': 1.0,
            'volatilityMax': 4,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True,
            # Gelişmiş indikatör ayarları
            'adxMinimum': 20,
            'williamsRThreshold': -80,
            'cciRange': 200,
            'useVWAP': True,
            'useParabolicSAR': True,
            'useIchimoku': True,
            'stochasticThreshold': "30,70"
        }

        # Karlılık oranına göre gelişmiş ayarlama
        if profitability_rate == 0:  # %0 karlı (tüm zararlı) - KRİTİK DURUM
            # Radikal değişiklik gerekli - çok daha az seçici
            params.update({
                'stopLoss': 2,  # Çok dar stop loss
                'takeProfit': 4,  # Düşük hedef
                'basePosition': 8,  # Küçük pozisyon
                'maxPosition': 12,
                'momentumScore': 4,  # 12 puan sisteminde çok az seçici
                'rsiLower': 40,  # Çok geniş RSI aralığı
                'rsiUpper': 85,
                'volatilityMax': 8,  # Yüksek volatilite toleransı
                'priceChange3': 0.2,  # Düşük momentum eşiği
                'priceChange10': 0.4,
                'emaSpread': 0.1,  # Düşük trend eşiği
                # Gelişmiş indikatörler - daha az seçici
                'adxMinimum': 15,  # Çok düşük trend eşiği
                'williamsRThreshold': -85,  # Çok geniş aralık
                'cciRange': 300,  # Çok geniş CCI aralığı
                'useVWAP': False,  # VWAP filtresini kapat (daha az seçici)
                'useParabolicSAR': True,  # SAR'ı kullan
                'useIchimoku': False,  # Ichimoku'yu kapat (çok seçici)
                'stochasticThreshold': "20,80"  # Geniş stochastic aralığı
            })
        elif profitability_rate < 0.3:  # %30- karlı - KÖTÜ DURUM
            # Önemli değişiklikler gerekli
            params.update({
                'stopLoss': 3,
                'takeProfit': 6,
                'basePosition': 10,
                'maxPosition': 15,
                'momentumScore': 6,  # Daha az seçici
                'rsiLower': 45,
                'rsiUpper': 80,
                'volatilityMax': 6,
                'priceChange3': 0.3,
                'priceChange10': 0.6,
                'emaSpread': 0.15,
                # Gelişmiş indikatörler
                'adxMinimum': 18,
                'williamsRThreshold': -82,
                'cciRange': 250,
                'useVWAP': True,
                'useParabolicSAR': True,
                'useIchimoku': True,
                'stochasticThreshold': "25,75"
            })
        elif profitability_rate > 0.7:  # %70+ karlı - İYİ DURUM
            # Agresif strateji - daha yüksek kar hedefi
            params.update({
                'takeProfit': 12,
                'basePosition': 20,
                'maxPosition': 30,
                'momentumScore': 10,  # Daha seçici
                'rsiLower': 60,
                'rsiUpper': 70,
                'volatilityMax': 3,
                'priceChange3': 0.8,
                'priceChange10': 1.5,
                'emaSpread': 0.3,
                # Gelişmiş indikatörler - daha seçici
                'adxMinimum': 25,
                'williamsRThreshold': -70,
                'cciRange': 150,
                'useVWAP': True,
                'useParabolicSAR': True,
                'useIchimoku': True,
                'stochasticThreshold': "25,75"
            })

        # Kazanma oranına göre gelişmiş ayarlama
        if avg_win_rate < 0.3:  # %30- kazanma oranı - ÇOK KÖTÜ
            params.update({
                'stopLoss': max(1.5, params['stopLoss'] - 1),
                'takeProfit': min(15, params['takeProfit'] + 3),
                'momentumScore': max(3, params['momentumScore'] - 2),
                'adxMinimum': max(15, params['adxMinimum'] - 5),
                'williamsRThreshold': min(-70, params['williamsRThreshold'] + 10),
                'cciRange': min(300, params['cciRange'] + 50),
                'useVWAP': False,  # Daha az filtre
                'stochasticThreshold': "20,80"
            })
        elif avg_win_rate > 0.6:  # %60+ kazanma oranı - İYİ
            params.update({
                'takeProfit': max(6, params['takeProfit'] - 2),
                'basePosition': min(25, params['basePosition'] + 5),
                'momentumScore': min(11, params['momentumScore'] + 1),
                'adxMinimum': min(30, params['adxMinimum'] + 3),
                'williamsRThreshold': max(-85, params['williamsRThreshold'] - 5),
                'cciRange': max(100, params['cciRange'] - 25)
            })

        # İşlem sayısına göre gelişmiş ayarlama
        if avg_trades < 3:  # Çok az işlem - KRİTİK
            params.update({
                'momentumScore': max(3, params['momentumScore'] - 3),
                'volatilityMax': min(10, params['volatilityMax'] + 4),
                'rsiLower': max(30, params['rsiLower'] - 15),
                'rsiUpper': min(90, params['rsiUpper'] + 15),
                'priceChange3': max(0.1, params['priceChange3'] - 0.4),
                'priceChange10': max(0.2, params['priceChange10'] - 0.6),
                'emaSpread': max(0.05, params['emaSpread'] - 0.15),
                # Gelişmiş indikatörler - çok daha az seçici
                'adxMinimum': max(12, params['adxMinimum'] - 8),
                'williamsRThreshold': min(-60, params['williamsRThreshold'] + 20),
                'cciRange': min(400, params['cciRange'] + 100),
                'useVWAP': False,
                'useIchimoku': False,
                'stochasticThreshold': "15,85"
            })
        elif avg_trades > 50:  # Çok fazla işlem
            params.update({
                'momentumScore': min(11, params['momentumScore'] + 2),
                'volatilityMax': max(2, params['volatilityMax'] - 2),
                'adxMinimum': min(30, params['adxMinimum'] + 5),
                'williamsRThreshold': max(-90, params['williamsRThreshold'] - 10),
                'cciRange': max(100, params['cciRange'] - 50)
            })

        # Sharpe oranına göre gelişmiş ayarlama
        if avg_sharpe < -0.5:  # Çok kötü Sharpe
            params.update({
                'stopLoss': max(1.5, params['stopLoss'] - 1),
                'basePosition': max(5, params['basePosition'] - 5),
                'momentumScore': max(3, params['momentumScore'] - 2),
                'adxMinimum': max(12, params['adxMinimum'] - 5),
                'useVWAP': False,
                'useIchimoku': False
            })
        elif avg_sharpe > 1:  # Çok iyi Sharpe
            params.update({
                'basePosition': min(30, params['basePosition'] + 5),
                'momentumScore': min(11, params['momentumScore'] + 1),
                'adxMinimum': min(30, params['adxMinimum'] + 3)
            })

        # Drawdown'a göre gelişmiş ayarlama
        if avg_drawdown > 0.3:  # %30+ drawdown - ÇOK RİSKLİ
            params.update({
                'stopLoss': max(1.5, params['stopLoss'] - 1.5),
                'trailingStop': max(1, params['trailingStop'] - 1),
                'basePosition': max(5, params['basePosition'] - 8),
                'maxPosition': max(10, params['maxPosition'] - 10),
                'momentumScore': min(11, params['momentumScore'] + 2),
                'adxMinimum': min(30, params['adxMinimum'] + 5),
                'williamsRThreshold': max(-90, params['williamsRThreshold'] - 10),
                'cciRange': max(100, params['cciRange'] - 50)
            })

        return params

    def _analyze_and_generate_cyclic_strategy(self, results, iteration, optimization_history):
        """
        Döngüsel optimizasyon için gelişmiş AI analizi

        Args:
            results: Mevcut analiz sonuçları
            iteration: Mevcut iterasyon numarası
            optimization_history: Önceki iterasyonların geçmişi

        Returns:
            dict: Döngüsel AI strateji önerisi
        """
        # Mevcut iterasyon istatistikleri
        current_stats = self._calculate_iteration_stats(results)

        # Önceki iterasyonlarla karşılaştırma
        improvement_analysis = self._analyze_improvement_trend(optimization_history, current_stats)

        # HAFIZA SİSTEMİ: Başarısız parametreleri analiz et
        failed_strategies = self._analyze_failed_strategies(optimization_history)

        # Döngüsel strateji parametreleri oluştur (hafıza ile)
        strategy_params = self._determine_cyclic_strategy_parameters_with_memory(
            current_stats, improvement_analysis, iteration, failed_strategies, optimization_history
        )

        # Döngüsel analiz metni oluştur
        analysis_text = self._generate_cyclic_analysis_text(
            current_stats, improvement_analysis, iteration
        )

        return {
            'analysis': analysis_text,
            'parameters': strategy_params,
            'expected': {
                'winRate': f"{current_stats['avg_win_rate'] * 100:.1f}",
                'avgProfit': f"{current_stats['avg_profit']:.1f}",
                'riskLevel': self._determine_risk_level(
                    current_stats['avg_drawdown'],
                    current_stats['avg_sharpe']
                )
            },
            'iteration': iteration,
            'improvement': improvement_analysis
        }

    def _calculate_iteration_stats(self, results):
        """Mevcut iterasyon istatistiklerini hesapla"""
        profitable_results = [r for r in results if r.get('profit_percentage', 0) > 0]

        total_results = len(results)
        profitable_count = len(profitable_results)
        profitability_rate = profitable_count / total_results if total_results > 0 else 0

        avg_profit = sum(r.get('profit_percentage', 0) for r in profitable_results) / len(profitable_results) if profitable_results else 0
        avg_win_rate = sum(r.get('win_rate', 0) for r in results) / len(results) if results else 0
        avg_trades = sum(r.get('total_trades', 0) for r in results) / len(results) if results else 0
        avg_sharpe = sum(r.get('sharpe_ratio', 0) for r in results) / len(results) if results else 0
        avg_drawdown = sum(r.get('max_drawdown', 0) for r in results) / len(results) if results else 0

        return {
            'total_results': total_results,
            'profitable_count': profitable_count,
            'profitability_rate': profitability_rate,
            'avg_profit': avg_profit,
            'avg_win_rate': avg_win_rate,
            'avg_trades': avg_trades,
            'avg_sharpe': avg_sharpe,
            'avg_drawdown': avg_drawdown
        }

    def _analyze_improvement_trend(self, optimization_history, current_stats):
        """İyileştirme trendini analiz et"""
        if not optimization_history:
            return {
                'is_first_iteration': True,
                'trend': 'başlangıç',
                'improvement_rate': 0,
                'best_iteration': 1
            }

        # Son iterasyonla karşılaştır
        last_iteration = optimization_history[-1]['stats']
        profit_improvement = current_stats['avg_profit'] - last_iteration.get('averageProfit', 0)

        # En iyi iterasyonu bul
        best_profit = max([h['stats'].get('averageProfit', -999) for h in optimization_history] + [current_stats['avg_profit']])
        best_iteration = 1
        for i, h in enumerate(optimization_history):
            if h['stats'].get('averageProfit', -999) == best_profit:
                best_iteration = i + 1
                break

        # Trend analizi
        if len(optimization_history) >= 2:
            recent_profits = [h['stats'].get('averageProfit', 0) for h in optimization_history[-2:]]
            if recent_profits[1] > recent_profits[0]:
                trend = 'yükseliş'
            elif recent_profits[1] < recent_profits[0]:
                trend = 'düşüş'
            else:
                trend = 'sabit'
        else:
            trend = 'belirsiz'

        return {
            'is_first_iteration': False,
            'trend': trend,
            'improvement_rate': profit_improvement,
            'best_iteration': best_iteration,
            'iterations_count': len(optimization_history) + 1
        }

    def _determine_cyclic_strategy_parameters(self, current_stats, improvement_analysis, iteration):
        """Döngüsel optimizasyon için strateji parametreleri belirle"""
        # Temel parametrelerle başla
        params = self._determine_advanced_strategy_parameters(
            current_stats['profitability_rate'],
            current_stats['avg_profit'],
            current_stats['avg_win_rate'],
            current_stats['avg_trades'],
            current_stats['avg_sharpe'],
            current_stats['avg_drawdown']
        )

        # İterasyon bazlı radikal değişiklikler - HER İTERASYONDA FARKLI SONUÇ GARANTİSİ
        import random
        random.seed(iteration * 42)  # Tekrarlanabilir ama farklı rastgelelik

        # İşlem garantisi kontrolü - eğer önceki iterasyonda hiç işlem yoksa
        if (not improvement_analysis['is_first_iteration'] and
            current_stats['avg_trades'] < 1):
            # HİÇ İŞLEM YOK - ACİL MÜDAHALE
            params.update({
                'momentumScore': 2,  # En düşük seviye
                'adxMinimum': 10,   # En düşük trend eşiği
                'volatilityMax': 15, # Çok yüksek volatilite toleransı
                'rsiLower': 20,     # Çok geniş RSI
                'rsiUpper': 95,
                'priceChange3': 0.05,  # Çok düşük momentum
                'priceChange10': 0.1,
                'emaSpread': 0.02,     # Çok düşük trend eşiği
                'useVWAP': False,      # Tüm filtreleri kapat
                'useIchimoku': False,
                'useParabolicSAR': False,
                'williamsRThreshold': -95,
                'cciRange': 500,
                'stochasticThreshold': "5,95"
            })
            print(f"İterasyon {iteration}: HİÇ İŞLEM YOK - ACİL MÜDAHALE!")

        # Döngüsel optimizasyon ayarlamaları
        elif not improvement_analysis['is_first_iteration']:
            # İyileştirme oranına göre ayarlama
            improvement_rate = improvement_analysis['improvement_rate']
            avg_trades = current_stats['avg_trades']

            if avg_trades < 5:  # Az işlem - ÖNCELİK: İŞLEM SAYISINI ARTIR
                params.update({
                    'momentumScore': max(3, params['momentumScore'] - 2),
                    'adxMinimum': max(12, params['adxMinimum'] - 5),
                    'volatilityMax': min(10, params['volatilityMax'] + 3),
                    'rsiLower': max(30, params['rsiLower'] - 15),
                    'rsiUpper': min(85, params['rsiUpper'] + 10),
                    'priceChange3': max(0.1, params['priceChange3'] - 0.3),
                    'priceChange10': max(0.2, params['priceChange10'] - 0.5),
                    'useVWAP': False,
                    'useIchimoku': False,
                    'williamsRThreshold': -85,
                    'cciRange': 350
                })
                print(f"İterasyon {iteration}: Az işlem ({avg_trades:.1f}) - Filtreleri gevşetiyorum")
            elif improvement_rate < -0.01:  # Kötüleşme ama işlem var
                params.update({
                    'momentumScore': max(4, params['momentumScore'] - 1),
                    'adxMinimum': max(15, params['adxMinimum'] - 3),
                    'volatilityMax': min(8, params['volatilityMax'] + 1),
                    'rsiLower': max(35, params['rsiLower'] - 10),
                    'rsiUpper': min(80, params['rsiUpper'] + 5),
                    'useVWAP': iteration % 2 == 0
                })
                print(f"İterasyon {iteration}: Kötüleşme var - Orta seviye değişiklik")
            elif improvement_rate < 0.01:  # Az iyileştirme - ORTA DEĞİŞİKLİK
                params.update({
                    'momentumScore': max(3, params['momentumScore'] - 2),
                    'adxMinimum': max(12, params['adxMinimum'] - 5),
                    'volatilityMax': min(10, params['volatilityMax'] + 2),
                    'rsiLower': max(30, params['rsiLower'] - 10),
                    'rsiUpper': min(85, params['rsiUpper'] + 10),
                    'stopLoss': max(1.5, params['stopLoss'] - 1),
                    'basePosition': max(8, params['basePosition'] - 3),
                    'useVWAP': iteration % 2 == 0,
                    'williamsRThreshold': min(-70, params['williamsRThreshold'] + 10),
                    'cciRange': min(350, params['cciRange'] + 50)
                })
            else:  # İyileştirme var - DENEYSEL DEĞİŞİKLİK
                # Her iterasyonda farklı bir parametre değiştir
                change_type = iteration % 5
                if change_type == 0:
                    params['momentumScore'] = max(3, params['momentumScore'] - random.randint(1, 3))
                elif change_type == 1:
                    params['volatilityMax'] = min(8, params['volatilityMax'] + random.randint(1, 2))
                elif change_type == 2:
                    params['rsiLower'] = max(30, params['rsiLower'] - random.randint(5, 15))
                elif change_type == 3:
                    params['adxMinimum'] = max(12, params['adxMinimum'] - random.randint(2, 5))
                else:
                    params['basePosition'] = max(8, params['basePosition'] - random.randint(2, 5))

        # Her iterasyonda garantili değişiklik (aynı sonuçları önlemek için)
        iteration_mod = iteration % 10

        if iteration_mod == 1:
            params['commission'] = 0.005  # Düşük komisyon
            params['momentumScore'] = max(3, params['momentumScore'] - 1)
        elif iteration_mod == 2:
            params['commission'] = 0.015  # Yüksük komisyon
            params['volatilityMax'] = min(8, params['volatilityMax'] + 1)
        elif iteration_mod == 3:
            params['useVWAP'] = False
            params['useIchimoku'] = False
            params['momentumScore'] = max(4, params['momentumScore'] - 1)
        elif iteration_mod == 4:
            params['useVWAP'] = True
            params['useIchimoku'] = True
            params['adxMinimum'] = max(18, params['adxMinimum'] - 2)
        else:
            # Kontrollü rastgele değişiklik (çok radikal olmasın)
            params['momentumScore'] = max(3, min(9, params['momentumScore'] + random.randint(-2, 1)))
            params['volatilityMax'] = max(3, min(8, params['volatilityMax'] + random.randint(-1, 2)))
            params['rsiLower'] = max(30, min(55, params['rsiLower'] + random.randint(-10, 5)))

        # İşlem garantisi - son kontrol
        if params['momentumScore'] < 3:
            params['momentumScore'] = 3
            print(f"İterasyon {iteration}: Momentum score çok düşük, 3'e yükseltildi")

        if params['adxMinimum'] < 12:
            params['adxMinimum'] = 12
            print(f"İterasyon {iteration}: ADX minimum çok düşük, 12'ye yükseltildi")

        if params['volatilityMax'] > 10:
            params['volatilityMax'] = 10
            print(f"İterasyon {iteration}: Volatilite max çok yüksek, 10'a düşürüldü")

        # İterasyon sayısına göre ek değişiklik
        if iteration > 2:
            params['stopLoss'] = max(1, params['stopLoss'] - (iteration - 2) * 0.5)
            params['basePosition'] = max(5, params['basePosition'] - (iteration - 2) * 2)

        print(f"İterasyon {iteration} parametreleri: Momentum={params['momentumScore']}, ADX={params['adxMinimum']}, Vol={params['volatilityMax']}")

        return params

    def _generate_cyclic_analysis_text(self, current_stats, improvement_analysis, iteration):
        """Döngüsel analiz için metin oluştur"""
        text = f"🔄 İterasyon {iteration} Analizi: "

        if improvement_analysis['is_first_iteration']:
            text += "Başlangıç analizi tamamlandı. "
        else:
            improvement = improvement_analysis['improvement_rate']
            if improvement > 1:
                text += f"Mükemmel iyileştirme! (+{improvement:.2f}%) "
            elif improvement > 0:
                text += f"Pozitif iyileştirme (+{improvement:.2f}%) "
            elif improvement > -0.5:
                text += f"Küçük düşüş ({improvement:.2f}%) "
            else:
                text += f"Önemli düşüş ({improvement:.2f}%) "

        text += f"Toplam {current_stats['total_results']} para biriminde "
        text += f"{current_stats['profitable_count']} tanesi karlı (%{current_stats['profitability_rate']*100:.1f}). "

        if current_stats['avg_trades'] < 3:
            text += "⚠️ Çok az işlem! Filtreler gevşetiliyor. "
        elif current_stats['avg_trades'] > 50:
            text += "⚠️ Çok fazla işlem! Filtreler sıkılaştırılıyor. "

        if not improvement_analysis['is_first_iteration']:
            trend = improvement_analysis['trend']
            if trend == 'yükseliş':
                text += "📈 Trend pozitif, mevcut yönde devam ediliyor. "
            elif trend == 'düşüş':
                text += "📉 Trend negatif, strateji değiştiriliyor. "
            else:
                text += "📊 Trend sabit, deneysel değişiklikler yapılıyor. "

        text += f"Bu iterasyon için {len([k for k, v in current_stats.items() if 'use' in str(k) and v])} gelişmiş filtre aktif."

        return text

    def _generate_analysis_text(self, total_results, profitable_count, profitability_rate, avg_profit, avg_win_rate, avg_trades):
        """
        Analiz sonuçlarına göre açıklama metni oluşturur
        """
        text = f"Toplam {total_results} para biriminde yapılan analiz sonucunda, "
        text += f"{profitable_count} tanesi karlı çıktı (%{profitability_rate*100:.1f} başarı oranı). "

        if profitability_rate == 0:
            text += "⚠️ KRİTİK DURUM: Hiçbir para biriminde kar elde edilemedi! "
            text += "Mevcut strateji çok seçici ve muhtemelen piyasa koşullarına uygun değil. "
            if avg_trades < 5:
                text += "Ayrıca çok az işlem yapılıyor, bu da fırsat kaybına neden oluyor. "
            text += "Radikal parametre değişiklikleri öneriliyor. "
        elif profitability_rate > 0.6:
            text += "Bu oldukça iyi bir sonuç! Mevcut strateji genel olarak başarılı görünüyor. "
        elif profitability_rate > 0.4:
            text += "Orta seviyede bir başarı oranı. Strateji parametrelerinde iyileştirme yapılabilir. "
        else:
            text += "Düşük başarı oranı. Strateji parametrelerinde önemli değişiklikler gerekli. "

        text += f"Ortalama kazanma oranı %{avg_win_rate*100:.1f}, "
        text += f"ortalama işlem sayısı {avg_trades:.0f}. "

        if avg_trades < 3:
            text += "🚨 KRİTİK: Çok az işlem yapılıyor! Tüm filtreler gevşetildi. "
        elif avg_trades < 5:
            text += "⚠️ Çok az işlem yapılıyor! Strateji daha az seçici olmalı. "
        elif avg_trades > 50:
            text += "Çok fazla işlem yapılıyor, komisyon maliyetleri yüksek olabilir. "

        if profitable_count > 0:
            if avg_profit > 5:
                text += "Karlı işlemlerde yüksek getiri elde ediliyor. "
            elif avg_profit > 2:
                text += "Karlı işlemlerde orta seviyede getiri elde ediliyor. "
            else:
                text += "Karlı işlemlerde düşük getiri elde ediliyor. "

        # Gelişmiş indikatör önerileri
        if profitability_rate == 0:
            text += "🔧 Gelişmiş İndikatör Ayarları: VWAP ve Ichimoku filtreleri kapatıldı, ADX eşiği düşürüldü, Williams %R ve CCI aralıkları genişletildi. "
        elif avg_trades < 3:
            text += "🔧 Gelişmiş İndikatör Ayarları: Tüm filtreler minimum seçicilik için ayarlandı. "
        else:
            text += "🔧 Gelişmiş İndikatör Ayarları: 12 teknik indikatör optimal değerlere ayarlandı. "

        text += "Aşağıdaki parametreler 12 indikatörlü gelişmiş sistem için optimize edilmiştir."

        return text

    def _determine_risk_level(self, avg_drawdown, avg_sharpe):
        """
        Risk seviyesini belirler
        """
        if avg_drawdown > 0.3 or avg_sharpe < -1:
            return "Yüksek"
        elif avg_drawdown > 0.15 or avg_sharpe < 0:
            return "Orta"
        else:
            return "Düşük"

    def data_manager_page(self):
        """
        Veri yöneticisi sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('data_manager.html')

    def download_dataset(self):
        """
        Veri setini indirir ve kaydeder

        Returns:
            dict: İndirme sonucu
        """
        try:
            data = request.get_json()

            symbols = data.get('symbols', [])
            timeframes = data.get('timeframes', [])
            start_date = data.get('start_date')
            end_date = data.get('end_date')
            dataset_name = data.get('dataset_name')

            if not all([symbols, timeframes, start_date, end_date, dataset_name]):
                return jsonify({'success': False, 'error': 'Eksik parametreler'}), 400

            # Veri setini indir ve kaydet
            dataset_info = self._download_and_save_dataset(
                symbols, timeframes, start_date, end_date, dataset_name
            )

            return jsonify({'success': True, 'dataset': dataset_info})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def list_datasets(self):
        """
        Mevcut veri setlerini listeler

        Returns:
            list: Veri setleri listesi
        """
        try:
            datasets_dir = 'data/datasets'
            if not os.path.exists(datasets_dir):
                return jsonify([])

            datasets = []
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    dataset_name = filename[:-5]  # .json uzantısını kaldır
                    info_path = os.path.join(datasets_dir, filename)

                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    datasets.append({
                        'name': dataset_name,
                        'symbols_count': len(info['symbols']),
                        'timeframes_count': len(info['timeframes']),
                        'created_at': info['created_at'],
                        'is_active': info.get('is_active', False)
                    })

            # Oluşturulma tarihine göre sırala
            datasets.sort(key=lambda x: x['created_at'], reverse=True)

            return jsonify(datasets)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def dataset_details(self, dataset_name):
        """
        Veri seti detaylarını döndürür

        Args:
            dataset_name: Veri seti adı

        Returns:
            dict: Veri seti detayları
        """
        try:
            info_path = f'data/datasets/{dataset_name}.json'

            if not os.path.exists(info_path):
                return jsonify({'error': 'Veri seti bulunamadı'}), 404

            with open(info_path, 'r', encoding='utf-8') as f:
                info = json.load(f)

            # Dosya boyutunu hesapla
            data_path = f'data/datasets/{dataset_name}.pkl'
            file_size = 0
            if os.path.exists(data_path):
                file_size = os.path.getsize(data_path)
                file_size_str = self._format_file_size(file_size)
            else:
                file_size_str = 'Bilinmiyor'

            info['file_size'] = file_size_str

            return jsonify(info)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def set_active_dataset(self):
        """
        Aktif veri setini ayarlar

        Returns:
            dict: Sonuç
        """
        try:
            data = request.get_json()
            dataset_name = data.get('dataset_name')

            if not dataset_name:
                return jsonify({'success': False, 'error': 'Veri seti adı gerekli'}), 400

            # Tüm veri setlerini pasif yap
            datasets_dir = 'data/datasets'
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    info_path = os.path.join(datasets_dir, filename)
                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    info['is_active'] = False

                    with open(info_path, 'w', encoding='utf-8') as f:
                        json.dump(info, f, ensure_ascii=False, indent=2)

            # Seçili veri setini aktif yap
            info_path = f'data/datasets/{dataset_name}.json'
            if os.path.exists(info_path):
                with open(info_path, 'r', encoding='utf-8') as f:
                    info = json.load(f)

                info['is_active'] = True

                with open(info_path, 'w', encoding='utf-8') as f:
                    json.dump(info, f, ensure_ascii=False, indent=2)

                return jsonify({'success': True})
            else:
                return jsonify({'success': False, 'error': 'Veri seti bulunamadı'}), 404

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def delete_dataset(self, dataset_name):
        """
        Veri setini siler

        Args:
            dataset_name: Veri seti adı

        Returns:
            dict: Sonuç
        """
        try:
            info_path = f'data/datasets/{dataset_name}.json'
            data_path = f'data/datasets/{dataset_name}.pkl'

            # Dosyaları sil
            if os.path.exists(info_path):
                os.remove(info_path)

            if os.path.exists(data_path):
                os.remove(data_path)

            return jsonify({'success': True})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def _download_and_save_dataset(self, symbols, timeframes, start_date, end_date, dataset_name):
        """
        Veri setini indirir ve kaydeder
        """
        # Klasör oluştur
        datasets_dir = 'data/datasets'
        os.makedirs(datasets_dir, exist_ok=True)

        dataset_data = {}
        total_records = 0

        # Her sembol ve timeframe için veri indir
        for symbol in symbols:
            dataset_data[symbol] = {}
            for timeframe in timeframes:
                try:
                    df = self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date, end_date)
                    if not df.empty:
                        dataset_data[symbol][timeframe] = df
                        total_records += len(df)
                except Exception as e:
                    print(f"Veri indirme hatası {symbol} {timeframe}: {e}")
                    continue

        # Veri setini kaydet
        data_path = os.path.join(datasets_dir, f'{dataset_name}.pkl')
        with open(data_path, 'wb') as f:
            pickle.dump(dataset_data, f)

        # Bilgi dosyasını kaydet
        info = {
            'name': dataset_name,
            'symbols': symbols,
            'timeframes': timeframes,
            'start_date': start_date,
            'end_date': end_date,
            'total_records': total_records,
            'created_at': datetime.now().isoformat(),
            'is_active': False
        }

        info_path = os.path.join(datasets_dir, f'{dataset_name}.json')
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)

        return info

    def _format_file_size(self, size_bytes):
        """
        Dosya boyutunu formatlar
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def _analyze_failed_strategies(self, optimization_history):
        """
        Başarısız stratejileri analiz et ve tekrarlanmasını önle
        """
        failed_strategies = {
            'zero_trades': [],  # Hiç işlem yapmayan parametreler
            'negative_profit': [],  # Zararlı parametreler
            'low_trades': []  # Az işlem yapan parametreler
        }

        for hist in optimization_history:
            stats = hist['stats']

            # Hiç işlem yapmayan stratejiler
            if stats['averageTrades'] < 1:
                failed_strategies['zero_trades'].append({
                    'iteration': hist['iteration'],
                    'avg_trades': stats['averageTrades'],
                    'profit': stats['averageProfit']
                })

            # Zararlı stratejiler
            elif stats['averageProfit'] < -0.1:
                failed_strategies['negative_profit'].append({
                    'iteration': hist['iteration'],
                    'avg_trades': stats['averageTrades'],
                    'profit': stats['averageProfit']
                })

            # Az işlem yapan stratejiler
            elif stats['averageTrades'] < 5:
                failed_strategies['low_trades'].append({
                    'iteration': hist['iteration'],
                    'avg_trades': stats['averageTrades'],
                    'profit': stats['averageProfit']
                })

        return failed_strategies

    def _determine_cyclic_strategy_parameters_with_memory(self, current_stats, improvement_analysis, iteration, failed_strategies, optimization_history=None):
        """
        Hafıza sistemi ile döngüsel optimizasyon parametreleri
        """
        # Temel parametrelerle başla
        params = self._determine_advanced_strategy_parameters(
            current_stats['profitability_rate'],
            current_stats['avg_profit'],
            current_stats['avg_win_rate'],
            current_stats['avg_trades'],
            current_stats['avg_sharpe'],
            current_stats['avg_drawdown']
        )

        # HAFIZA BAZLI KARAR VERME
        zero_trades_count = len(failed_strategies['zero_trades'])
        negative_profit_count = len(failed_strategies['negative_profit'])
        low_trades_count = len(failed_strategies['low_trades'])

        print(f"İterasyon {iteration} hafıza: {zero_trades_count} sıfır işlem, {negative_profit_count} zararlı, {low_trades_count} az işlem")

        # Eğer çok fazla sıfır işlem varsa, çok agresif gevşetme
        if zero_trades_count >= 3:
            params.update({
                'momentumScore': 2,  # En düşük
                'adxMinimum': 8,    # En düşük
                'volatilityMax': 15, # En yüksek
                'rsiLower': 15,     # Çok geniş
                'rsiUpper': 95,
                'priceChange3': 0.01,  # Çok düşük
                'priceChange10': 0.02,
                'emaSpread': 0.01,
                'useVWAP': False,
                'useIchimoku': False,
                'useParabolicSAR': False,
                'williamsRThreshold': -98,
                'cciRange': 500,
                'stochasticThreshold': "2,98"
            })
            print(f"İterasyon {iteration}: ÇOK FAZLA SIFIR İŞLEM - ULTRA AGRESİF GEVŞETME!")

        # Eğer çok fazla zararlı işlem varsa, daha konservatif ol
        elif negative_profit_count >= 3:
            params.update({
                'momentumScore': 9,   # Daha seçici
                'adxMinimum': 25,    # Yüksek trend
                'volatilityMax': 2,  # Düşük volatilite
                'rsiLower': 60,      # Dar aralık
                'rsiUpper': 70,
                'stopLoss': 2,       # Dar stop
                'takeProfit': 4,     # Düşük hedef
                'basePosition': 5,   # Küçük pozisyon
                'useVWAP': True,
                'useIchimoku': True,
                'useParabolicSAR': True
            })
            print(f"İterasyon {iteration}: ÇOK FAZLA ZARARLI İŞLEM - KONSERVATIF STRATEJI!")

        # BAŞARILI PARAMETRE HAFIZASI - En karlı iterasyonları taklit et
        best_profitable_params = None
        best_profit = 0

        if optimization_history and len(optimization_history) > 0:
            # En karlı iterasyonu bul
            for hist in optimization_history:
                profit = hist['stats'].get('averageProfit', -999)
                if profit > best_profit:
                    best_profit = profit
                    best_profitable_params = hist.get('applied_params')

            if best_profit > 0:
                print(f"İterasyon {iteration}: En karlı sonuç %{best_profit:.2f} bulundu")

        # HEDEF KARA YAKLAŞMA STRATEJİSİ
        if best_profit >= 2.5:  # %2.5+ karlılık varsa, o parametreleri optimize et
            print(f"İterasyon {iteration}: YÜKSEK KARLILIK MODU - En iyi parametreler optimize ediliyor!")

            if best_profitable_params:
                # En başarılı parametreleri kopyala ve çok küçük değişiklikler yap
                for key, value in best_profitable_params.items():
                    if key in params:
                        if isinstance(value, (int, float)):
                            # Çok küçük değişiklik (%5-15)
                            variation = random.uniform(0.85, 1.15)
                            if isinstance(value, int):
                                params[key] = max(1, int(value * variation))
                            else:
                                params[key] = max(0.001, value * variation)
                        else:
                            params[key] = value

                # Risk/ödül oranını iyileştir
                if 'stopLoss' in params and 'takeProfit' in params:
                    # Daha iyi risk/ödül oranı için
                    params['stopLoss'] = random.uniform(1.5, 2.5)  # Dar stop
                    params['takeProfit'] = random.uniform(6, 12)   # Geniş hedef

                # Pozisyon boyutunu artır (daha fazla kar için)
                if 'basePosition' in params:
                    params['basePosition'] = random.randint(15, 25)  # Daha büyük pozisyon

                print(f"İterasyon {iteration}: En iyi parametreler %5-15 ince ayar ile optimize edildi")

        elif best_profitable_params and iteration > 3:
            # Normal başarılı parametre optimizasyonu
            print(f"İterasyon {iteration}: BAŞARILI PARAMETRELER BAZ ALINARAK İYİLEŞTİRİLİYOR!")

            for key, value in best_profitable_params.items():
                if key in params:
                    if isinstance(value, (int, float)):
                        # Orta seviye değişiklik (%10-25)
                        variation = random.uniform(0.75, 1.25)
                        if isinstance(value, int):
                            params[key] = max(1, int(value * variation))
                        else:
                            params[key] = max(0.001, value * variation)
                    else:
                        params[key] = value

            print(f"İterasyon {iteration}: Başarılı parametreler %10-25 değişiklikle uygulandı")

        else:
            # İlk 5 iterasyon veya başarılı parametre yoksa normal yaklaşım
            if current_stats['avg_trades'] < 1:
                # Hiç işlem yok - acil müdahale
                params.update({
                    'momentumScore': max(2, 8 - iteration),
                    'adxMinimum': max(10, 20 - iteration),
                    'volatilityMax': min(12, 4 + iteration),
                    'rsiLower': max(20, 50 - iteration * 2),
                    'rsiUpper': min(90, 70 + iteration * 2),
                    'useVWAP': False,
                    'useIchimoku': False
                })
                print(f"İterasyon {iteration}: HİÇ İŞLEM YOK - PROGRESIF GEVŞETME!")

            elif current_stats['avg_profit'] < 0:
                # Zararlı ama işlem var - dengeyi bul
                params.update({
                    'momentumScore': max(4, params['momentumScore'] - 1),
                    'adxMinimum': max(15, params['adxMinimum'] - 2),
                    'volatilityMax': min(8, params['volatilityMax'] + 1),
                    'stopLoss': max(1.5, params['stopLoss'] - 0.5),
                    'takeProfit': min(8, params['takeProfit'] + 1)
                })
                print(f"İterasyon {iteration}: ZARARLI AMA İŞLEM VAR - DENGE ARAYIŞI!")

            else:
                # Karlı - mevcut yönde devam et
                params.update({
                    'momentumScore': min(10, params['momentumScore'] + 1),
                    'adxMinimum': min(25, params['adxMinimum'] + 1),
                    'takeProfit': min(12, params['takeProfit'] + 1),
                    'basePosition': min(20, params['basePosition'] + 2)
                })
                print(f"İterasyon {iteration}: KARLI - MEVCUT YÖNDE DEVAM!")

        # BENZERSİZLİK GARANTİSİ - Her iterasyonda tamamen farklı parametreler
        import random
        import hashlib

        # Benzersiz seed oluştur (iterasyon + hafıza durumu + zaman)
        unique_seed = iteration * 1000 + zero_trades_count * 100 + negative_profit_count * 10 + low_trades_count
        random.seed(unique_seed)

        # ÇOKLU PARA BİRİMİ KONTROLÜ
        # Eğer ortalama işlem sayısı çok düşükse (çoklu para birimi problemi)
        avg_trades = current_stats.get('avg_trades', 0)
        is_multi_currency = avg_trades < 5  # 5'ten az işlem = çoklu para birimi problemi

        # HAFIZA BAZLI AKILLI BÖLGE SEÇİMİ
        if is_multi_currency:
            # Çoklu para birimi için çok daha agresif yaklaşım
            if zero_trades_count >= 1 or avg_trades < 2:
                available_zones = [0]  # Sadece ultra agresif
                print(f"İterasyon {iteration}: ÇOKLU PARA BİRİMİ + AZ İŞLEM - SADECE ULTRA AGRESİF!")
            else:
                available_zones = [0, 1]  # Sadece agresif bölgeler
                print(f"İterasyon {iteration}: ÇOKLU PARA BİRİMİ - SADECE AGRESİF BÖLGELER!")
        else:
            # Tek para birimi için normal yaklaşım
            if zero_trades_count >= 2:
                available_zones = [0, 1]  # Sadece çok agresif ve agresif
                print(f"İterasyon {iteration}: ÇOK FAZLA SIFIR İŞLEM - SADECE AGRESİF BÖLGELER!")
            elif zero_trades_count >= 1:
                available_zones = [0, 1, 2]  # Konservatif bölgeleri kaldır
                print(f"İterasyon {iteration}: SIFIR İŞLEM VAR - KONSERVATIF BÖLGELER KALDIRILDI!")
            else:
                available_zones = [0, 1, 2, 3]  # Çok konservatif bölgeyi tamamen kaldır

        # Rastgele bölge seç (mevcut iterasyon bazlı değil)
        iteration_zone = random.choice(available_zones)

        if iteration_zone == 0:  # Çok Agresif Bölge - İŞLEM GARANTİLİ
            if is_multi_currency:
                # ÇOKLU PARA BİRİMİ İÇİN ULTRA AGRESİF PARAMETRELER
                params.update({
                    'momentumScore': random.randint(1, 3),      # Çok düşük
                    'adxMinimum': random.randint(5, 12),       # Çok düşük
                    'volatilityMax': random.randint(12, 20),   # Çok yüksek
                    'rsiLower': random.randint(5, 25),         # Çok geniş
                    'rsiUpper': random.randint(85, 99),        # Çok geniş
                    'priceChange3': random.uniform(0.01, 0.2), # Çok düşük
                    'priceChange10': random.uniform(0.02, 0.3),
                    'emaSpread': random.uniform(0.005, 0.1),   # Çok düşük
                    'useVWAP': False,
                    'useIchimoku': False,
                    'useParabolicSAR': False,
                    'williamsRThreshold': random.randint(-99, -80),
                    'cciRange': random.randint(200, 500),
                    'stochasticThreshold': f"{random.randint(1, 15)},{random.randint(85, 99)}"
                })
                print(f"İterasyon {iteration}: ÇOKLU PARA BİRİMİ - ULTRA AGRESİF BÖLGE!")
            else:
                # TEK PARA BİRİMİ İÇİN NORMAL AGRESİF
                params.update({
                    'momentumScore': random.randint(2, 4),
                    'adxMinimum': random.randint(8, 15),
                    'volatilityMax': random.randint(8, 15),
                    'rsiLower': random.randint(10, 30),
                    'rsiUpper': random.randint(85, 98),
                    'priceChange3': random.uniform(0.05, 0.3),
                    'priceChange10': random.uniform(0.1, 0.5),
                    'emaSpread': random.uniform(0.02, 0.15),
                    'useVWAP': False,
                    'useIchimoku': False,
                    'useParabolicSAR': False
                })
                print(f"İterasyon {iteration}: TEK PARA BİRİMİ - ÇOK AGRESİF BÖLGE!")

        elif iteration_zone == 1:  # Agresif Bölge
            if is_multi_currency:
                # ÇOKLU PARA BİRİMİ İÇİN AGRESİF PARAMETRELER
                params.update({
                    'momentumScore': random.randint(2, 5),      # Daha düşük
                    'adxMinimum': random.randint(8, 15),       # Daha düşük
                    'volatilityMax': random.randint(10, 15),   # Daha yüksek
                    'rsiLower': random.randint(10, 35),        # Daha geniş
                    'rsiUpper': random.randint(80, 95),        # Daha geniş
                    'priceChange3': random.uniform(0.05, 0.4), # Daha düşük
                    'priceChange10': random.uniform(0.1, 0.6),
                    'emaSpread': random.uniform(0.02, 0.2),
                    'useVWAP': False,  # Çoklu para biriminde filtre yok
                    'useIchimoku': False,
                    'useParabolicSAR': False
                })
                print(f"İterasyon {iteration}: ÇOKLU PARA BİRİMİ - AGRESİF BÖLGE!")
            else:
                # TEK PARA BİRİMİ İÇİN NORMAL AGRESİF
                params.update({
                    'momentumScore': random.randint(3, 6),
                    'adxMinimum': random.randint(12, 18),
                    'volatilityMax': random.randint(6, 12),
                    'rsiLower': random.randint(20, 40),
                    'rsiUpper': random.randint(75, 90),
                    'priceChange3': random.uniform(0.1, 0.5),
                    'priceChange10': random.uniform(0.2, 0.8),
                    'emaSpread': random.uniform(0.05, 0.25),
                    'useVWAP': random.choice([True, False]),
                    'useIchimoku': False,
                    'useParabolicSAR': random.choice([True, False])
                })
                print(f"İterasyon {iteration}: TEK PARA BİRİMİ - AGRESİF BÖLGE!")

        elif iteration_zone == 2:  # Orta Bölge
            params.update({
                'momentumScore': random.randint(4, 7),
                'adxMinimum': random.randint(15, 22),
                'volatilityMax': random.randint(4, 8),
                'rsiLower': random.randint(30, 50),
                'rsiUpper': random.randint(70, 85),
                'priceChange3': random.uniform(0.2, 0.7),
                'priceChange10': random.uniform(0.4, 1.2),
                'emaSpread': random.uniform(0.1, 0.3),
                'useVWAP': random.choice([True, False]),
                'useIchimoku': random.choice([True, False]),
                'useParabolicSAR': True
            })
            print(f"İterasyon {iteration}: ORTA BÖLGE")

        else:  # Konservatif Bölge (Çok konservatif kaldırıldı)
            params.update({
                'momentumScore': random.randint(6, 9),
                'adxMinimum': random.randint(18, 25),
                'volatilityMax': random.randint(3, 6),
                'rsiLower': random.randint(40, 60),
                'rsiUpper': random.randint(65, 80),
                'priceChange3': random.uniform(0.3, 0.8),
                'priceChange10': random.uniform(0.6, 1.5),
                'emaSpread': random.uniform(0.15, 0.35),
                'useVWAP': True,
                'useIchimoku': random.choice([True, False]),
                'useParabolicSAR': True
            })
            print(f"İterasyon {iteration}: KONSERVATIF BÖLGE")

        # Diğer parametrelerde de benzersizlik
        params.update({
            'stopLoss': random.uniform(1.0, 4.0),
            'takeProfit': random.uniform(3.0, 12.0),
            'basePosition': random.randint(5, 25),
            'maxPosition': random.randint(15, 35),
            'commission': random.choice([0.005, 0.01, 0.015, 0.02]),
            'trailingStop': random.randint(1, 5),
            'williamsRThreshold': random.randint(-95, -60),
            'cciRange': random.randint(100, 400),
            'stochasticThreshold': f"{random.randint(10, 30)},{random.randint(70, 90)}"
        })

        # İŞLEM GARANTİSİ - Son kontrol ve düzeltme
        # 1d zaman diliminde sıfır işlem asla olmamalı
        if zero_trades_count >= 1 or (is_multi_currency and avg_trades < 2):
            # Tek sıfır işlem bile varsa acil müdahale (1d için)
            if is_multi_currency:
                # ÇOKLU PARA BİRİMİ ACİL DURUM - ULTRA ULTRA AGRESİF
                params.update({
                    'momentumScore': 1,        # En düşük
                    'adxMinimum': 3,          # En düşük
                    'volatilityMax': 25,      # En yüksek
                    'rsiLower': 1,            # En geniş
                    'rsiUpper': 99,           # En geniş
                    'priceChange3': 0.005,    # Ultra düşük
                    'priceChange10': 0.01,    # Ultra düşük
                    'emaSpread': 0.001,       # Ultra düşük
                    'useVWAP': False,
                    'useIchimoku': False,
                    'useParabolicSAR': False,
                    'williamsRThreshold': -99,
                    'cciRange': 1000,         # En yüksek
                    'stochasticThreshold': "0,100"  # En geniş
                })
                print(f"İterasyon {iteration}: ÇOKLU PARA BİRİMİ ACİL DURUM - ULTRA ULTRA AGRESİF!")
            else:
                # TEK PARA BİRİMİ ACİL DURUM
                params.update({
                    'momentumScore': 2,
                    'adxMinimum': 8,
                    'volatilityMax': 15,
                    'rsiLower': 5,
                    'rsiUpper': 98,
                    'priceChange3': 0.01,
                    'priceChange10': 0.02,
                    'emaSpread': 0.005,
                    'useVWAP': False,
                    'useIchimoku': False,
                    'useParabolicSAR': False,
                    'williamsRThreshold': -99,
                    'cciRange': 500,
                    'stochasticThreshold': "1,99"
                })
                print(f"İterasyon {iteration}: TEK PARA BİRİMİ ACİL DURUM - ULTRA GEVŞEK PARAMETRELER!")

        # Minimum işlem garantisi için güvenlik kontrolleri
        if params['momentumScore'] > 6 and zero_trades_count > 0:
            params['momentumScore'] = random.randint(2, 5)
            print(f"İterasyon {iteration}: Momentum score düşürüldü (işlem garantisi)")

        if params['adxMinimum'] > 20 and zero_trades_count > 0:
            params['adxMinimum'] = random.randint(8, 18)
            print(f"İterasyon {iteration}: ADX minimum düşürüldü (işlem garantisi)")

        if params['rsiUpper'] - params['rsiLower'] < 30 and zero_trades_count > 0:
            params['rsiLower'] = random.randint(10, 25)
            params['rsiUpper'] = random.randint(85, 95)
            print(f"İterasyon {iteration}: RSI aralığı genişletildi (işlem garantisi)")

        # Parametre hash'i oluştur (benzersizlik kontrolü için)
        param_str = f"{params['momentumScore']}-{params['adxMinimum']}-{params['volatilityMax']}-{params['rsiLower']}-{params['rsiUpper']}"
        param_hash = hashlib.md5(param_str.encode()).hexdigest()[:8]

        print(f"İterasyon {iteration} BENZERSİZ parametreler (Hash: {param_hash}):")
        print(f"  Momentum={params['momentumScore']}, ADX={params['adxMinimum']}, Vol={params['volatilityMax']}")
        print(f"  RSI={params['rsiLower']}-{params['rsiUpper']}, Stop={params['stopLoss']:.1f}%, Take={params['takeProfit']:.1f}%")
        print(f"  VWAP={params['useVWAP']}, Ichimoku={params['useIchimoku']}, SAR={params['useParabolicSAR']}")

        return params

    def _get_data_for_simulation(self, symbol, timeframe, start_date):
        """
        Simülasyon için veri al (önce yerel veri seti, sonra online)

        Args:
            symbol: Para birimi
            timeframe: Zaman dilimi
            start_date: Başlangıç tarihi

        Returns:
            DataFrame: OHLCV verileri
        """
        # Aktif veri setini kontrol et
        active_dataset = self._get_active_dataset()

        if active_dataset and symbol in active_dataset and timeframe in active_dataset[symbol]:
            # Yerel veri setinden al
            df = active_dataset[symbol][timeframe].copy()

            # Tarih filtreleme
            if start_date:
                try:
                    start_datetime = pd.to_datetime(start_date)
                    df = df[df.index >= start_datetime]
                except:
                    pass

            return df
        else:
            # Online'dan al
            return self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)

    def _get_active_dataset(self):
        """
        Aktif veri setini yükle

        Returns:
            dict: Veri seti veya None
        """
        try:
            datasets_dir = 'data/datasets'

            # Aktif veri setini bul
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    info_path = os.path.join(datasets_dir, filename)
                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    if info.get('is_active', False):
                        # Veri dosyasını yükle
                        dataset_name = filename[:-5]  # .json uzantısını kaldır
                        data_path = os.path.join(datasets_dir, f'{dataset_name}.pkl')

                        if os.path.exists(data_path):
                            with open(data_path, 'rb') as f:
                                return pickle.load(f)

            return None

        except Exception as e:
            print(f"Aktif veri seti yükleme hatası: {e}")
            return None