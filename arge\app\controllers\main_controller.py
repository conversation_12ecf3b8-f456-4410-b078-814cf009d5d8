"""
Ana controller sınıfı
"""
import os
import json
import pickle
import numpy as np
import pandas as pd
from datetime import datetime
from flask import render_template, jsonify, request, session
from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.config import Config

class MainController:
    def __init__(self):
        """
        MainController sınıfının başlatıcı metodu
        """
        self.config = Config()
        self.data_fetcher = DataFetcher(self.config)
        self.simulation_engine = SimulationEngine(self.config)
        self.trading_simulator = self.simulation_engine  # AI optimizasyon için alias

        # Data manager alias (live trading için)
        self.data_manager = self.data_fetcher
        
    def index(self):
        """
        Ana sayfa
        
        Returns:
            str: HTML sayfası
        """
        return render_template('index.html')
    
    def get_market_data(self):
        """
        Piyasa verilerini döndürür
        
        Returns:
            dict: Piyasa verileri
        """
        symbol = request.args.get('symbol', self.config.get('DEFAULT_SYMBOL'))
        timeframe = request.args.get('timeframe', self.config.get('DEFAULT_TIMEFRAME'))
        start_date = request.args.get('start_date', self.config.get('DEFAULT_START_DATE'))
        
        df = self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)
        
        return jsonify({
            'symbol': symbol,
            'timeframe': timeframe,
            'data': df.to_dict(orient='records')
        })
    
    def get_exchange_info(self):
        """
        Forex bilgilerini döndürür

        Returns:
            dict: Forex bilgileri
        """
        symbols = self.data_fetcher.get_available_symbols()
        timeframes = self.data_fetcher.get_timeframes()

        return jsonify({
            'symbols': symbols,
            'timeframes': timeframes
        })
    
    def run_simulation(self):
        """
        Simülasyonu çalıştırır
        
        Returns:
            dict: Simülasyon sonuçları
        """
        try:
            data = request.get_json()

            if not data:
                return jsonify({'error': 'Geçersiz istek verisi'}), 400

            symbol = data.get('symbol')
            timeframe = data.get('timeframe')
            start_date = data.get('start_date')
            initial_balance = data.get('initial_balance')
            leverage = data.get('leverage', 100)  # Varsayılan 1:100
            lot_size = data.get('lot_size', 0.1)  # Varsayılan 0.1 lot
            request_strategy_settings = data.get('strategy_settings')  # Request'ten gelen strateji
            request_strategy_params = data.get('strategy_params')  # AI optimizasyon parametreleri

            # Parametreleri doğrula
            if not all([symbol, timeframe, start_date, initial_balance]):
                return jsonify({'error': 'Eksik parametreler'}), 400
            
            try:
                initial_balance = float(initial_balance)
                if initial_balance <= 0:
                    return jsonify({'error': 'Başlangıç bakiyesi pozitif olmalıdır'}), 400
            except ValueError:
                return jsonify({'error': 'Geçersiz başlangıç bakiyesi'}), 400
            
            try:
                datetime.strptime(start_date, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Geçersiz tarih formatı'}), 400
            
            # Veri al (yerel veri seti varsa onu kullan)
            df = self._get_data_for_simulation(symbol, timeframe, start_date)
            
            if df.empty:
                return jsonify({'error': 'Veri bulunamadı'}), 404
            
            # Strateji ayarlarını belirle (öncelik: request > session > varsayılan)
            strategy_settings = None
            if request_strategy_settings:
                strategy_settings = request_strategy_settings
                print(f"Request'ten strateji kullanılıyor: {symbol} - Momentum: {strategy_settings.get('momentumScore', 'N/A')}")
            else:
                strategy_settings = session.get('strategy_settings', None)
                if strategy_settings:
                    print(f"Session'dan strateji kullanılıyor: {symbol}")
                else:
                    print(f"Varsayılan strateji kullanılıyor: {symbol}")

            # Strateji ayarları varsa yeni simulation engine oluştur
            if strategy_settings:
                simulation_engine = SimulationEngine(self.config, strategy_settings)
            else:
                simulation_engine = self.simulation_engine

            # Simülasyonu çalıştır (forex kaldıraç ile)
            # AI optimizasyon parametreleri varsa geçir
            if request_strategy_params:
                print(f"🤖 AI optimizasyon parametreleri kullanılıyor: {len(request_strategy_params)} parametre")
                results = simulation_engine.run_simulation(df, initial_balance, leverage, lot_size, **request_strategy_params)
            else:
                results = simulation_engine.run_simulation(df, initial_balance, leverage, lot_size)

            # Fiyat verilerini ekle
            results['prices'] = df['close'].tolist()

            # Kullanıcı parametrelerini sonuçlara ekle
            results['symbol'] = symbol
            results['timeframe'] = timeframe
            results['start_date'] = start_date
            results['initial_balance'] = initial_balance
            results['leverage'] = leverage
            results['lot_size'] = lot_size

            return jsonify(results)
            
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    
    def get_performance(self):
        """
        Performans metriklerini döndürür
        
        Returns:
            dict: Performans metrikleri
        """
        data = request.get_json()
        results = data.get('results', {})
        
        metrics = self.simulation_engine.get_performance_metrics(results)
        
        return jsonify(metrics)
    
    def get_trade_history(self):
        """
        İşlem geçmişini döndürür
        
        Returns:
            dict: İşlem geçmişi
        """
        data = request.get_json()
        results = data.get('results', {})
        
        trades = self.simulation_engine.get_trade_history(results)
        
        return jsonify(trades)

    def strategy_page(self):
        """
        Strateji ayarları sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('strategy.html')

    def save_strategy(self):
        """
        Strateji ayarlarını kaydeder

        Returns:
            dict: Kaydetme sonucu
        """
        try:
            data = request.get_json()

            if not data:
                return jsonify({'success': False, 'error': 'Geçersiz veri'}), 400

            # Strateji ayarlarını doğrula
            required_fields = [
                'stopLoss', 'takeProfit', 'trailingStop', 'basePosition',
                'maxPosition', 'minPosition', 'rsiLower', 'rsiUpper',
                'emaSpread', 'momentumScore', 'priceChange3', 'priceChange10',
                'volatilityMax', 'commission'
            ]

            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'Eksik alan: {field}'}), 400

            # Strateji ayarlarını session'a kaydet (gerçek uygulamada veritabanına kaydedilir)
            session['strategy_settings'] = data

            return jsonify({'success': True, 'message': 'Strateji başarıyla kaydedildi'})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def get_strategy(self):
        """
        Kaydedilmiş strateji ayarlarını döndürür

        Returns:
            dict: Strateji ayarları
        """
        strategy_settings = session.get('strategy_settings', {})
        return jsonify(strategy_settings)

    def bulk_analysis_page(self):
        """
        Toplu analiz sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('bulk_analysis.html')

    def smart_optimization_page(self):
        """
        Akıllı optimizasyon sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('smart_optimization.html')

    def live_trading_page(self):
        """
        Canlı trading takip sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('live_trading.html')

    def generate_ai_strategy(self):
        """
        Toplu analiz sonuçlarından AI strateji önerisi oluşturur

        Returns:
            dict: AI strateji önerisi
        """
        try:
            data = request.get_json()
            analysis_results = data.get('analysis_results', [])
            iteration = data.get('iteration', 1)
            optimization_history = data.get('optimization_history', [])

            if not analysis_results:
                return jsonify({'error': 'Analiz sonuçları bulunamadı'}), 400

            # Başarılı sonuçları filtrele
            successful_results = [r for r in analysis_results if not r.get('error')]

            if len(successful_results) < 1:  # Döngüsel optimizasyon için daha esnek
                return jsonify({'error': 'Hiç başarılı analiz sonucu yok'}), 400

            # Döngüsel AI analizi yap
            try:
                ai_strategy = self._analyze_and_generate_cyclic_strategy(
                    successful_results, iteration, optimization_history
                )
            except Exception as e:
                # Hata durumunda basit strateji döndür
                print(f"Döngüsel AI strateji hatası: {e}")
                ai_strategy = self._analyze_and_generate_strategy(successful_results)
                ai_strategy['iteration'] = iteration
                ai_strategy['fallback'] = True

            return jsonify(ai_strategy)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def _analyze_and_generate_strategy(self, results):
        """
        Analiz sonuçlarından strateji parametreleri oluşturur

        Args:
            results: Başarılı analiz sonuçları listesi

        Returns:
            dict: AI strateji önerisi
        """
        # Karlı sonuçları al
        profitable_results = [r for r in results if r.get('profit_percentage', 0) > 0]

        # İstatistikleri hesapla
        total_results = len(results)
        profitable_count = len(profitable_results)
        profitability_rate = profitable_count / total_results if total_results > 0 else 0

        avg_profit = sum(r.get('profit_percentage', 0) for r in profitable_results) / len(profitable_results) if profitable_results else 0
        avg_win_rate = sum(r.get('win_rate', 0) for r in results) / len(results) if results else 0
        avg_trades = sum(r.get('total_trades', 0) for r in results) / len(results) if results else 0
        avg_sharpe = sum(r.get('sharpe_ratio', 0) for r in results) / len(results) if results else 0
        avg_drawdown = sum(r.get('max_drawdown', 0) for r in results) / len(results) if results else 0

        # Gelişmiş strateji parametrelerini belirle (tüm indikatörler dahil)
        strategy_params = self._determine_advanced_strategy_parameters(
            profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown
        )

        # Analiz metni oluştur
        analysis_text = self._generate_analysis_text(
            total_results, profitable_count, profitability_rate, avg_profit, avg_win_rate, avg_trades
        )

        return {
            'analysis': analysis_text,
            'parameters': strategy_params,
            'expected': {
                'winRate': f"{avg_win_rate * 100:.1f}",
                'avgProfit': f"{avg_profit:.1f}",
                'riskLevel': self._determine_risk_level(avg_drawdown, avg_sharpe)
            }
        }

    def _determine_strategy_parameters(self, profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown):
        """
        İstatistiklere göre strateji parametrelerini belirler
        """
        # Temel parametreler
        params = {
            'stopLoss': 4,
            'takeProfit': 8,
            'trailingStop': 3,
            'basePosition': 15,
            'maxPosition': 25,
            'minPosition': 8,
            'rsiLower': 55,
            'rsiUpper': 75,
            'emaSpread': 0.2,
            'momentumScore': 5,
            'priceChange3': 0.5,
            'priceChange10': 1.0,
            'volatilityMax': 4,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

        # Karlılık oranına göre ayarlama
        if profitability_rate > 0.7:  # %70+ karlı
            # Agresif strateji
            params['takeProfit'] = 12
            params['basePosition'] = 20
            params['maxPosition'] = 30
            params['momentumScore'] = 4
        elif profitability_rate == 0:  # %0 karlı (tüm zararlı)
            # Radikal değişiklik gerekli
            params['stopLoss'] = 2  # Çok dar stop loss
            params['takeProfit'] = 4  # Düşük hedef
            params['basePosition'] = 8  # Küçük pozisyon
            params['maxPosition'] = 12
            params['momentumScore'] = 3  # Daha az seçici
            params['rsiLower'] = 45  # Daha geniş RSI aralığı
            params['rsiUpper'] = 80
            params['volatilityMax'] = 6  # Daha yüksek volatilite toleransı
            params['priceChange3'] = 0.2  # Daha düşük momentum eşiği
            params['priceChange10'] = 0.5
            params['emaSpread'] = 0.1  # Daha düşük trend eşiği
        elif profitability_rate < 0.4:  # %40- karlı
            # Konservatif strateji
            params['stopLoss'] = 3
            params['takeProfit'] = 6
            params['basePosition'] = 10
            params['maxPosition'] = 15
            params['momentumScore'] = 6
            params['volatilityMax'] = 2

        # Kazanma oranına göre ayarlama
        if avg_win_rate < 0.4:  # Düşük kazanma oranı
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['takeProfit'] = min(15, params['takeProfit'] + 2)
        elif avg_win_rate > 0.6:  # Yüksek kazanma oranı
            params['takeProfit'] = max(6, params['takeProfit'] - 2)
            params['basePosition'] = min(25, params['basePosition'] + 5)

        # İşlem sayısına göre ayarlama
        if avg_trades > 50:  # Çok fazla işlem
            params['momentumScore'] = min(6, params['momentumScore'] + 1)
            params['volatilityMax'] = max(2, params['volatilityMax'] - 1)
        elif avg_trades < 5:  # Çok az işlem (kritik durum)
            # Radikal değişiklik - daha fazla işlem için
            params['momentumScore'] = max(2, params['momentumScore'] - 2)
            params['volatilityMax'] = min(10, params['volatilityMax'] + 3)
            params['rsiLower'] = max(30, params['rsiLower'] - 10)
            params['rsiUpper'] = min(85, params['rsiUpper'] + 10)
            params['priceChange3'] = max(0.1, params['priceChange3'] - 0.3)
            params['priceChange10'] = max(0.2, params['priceChange10'] - 0.5)
            params['emaSpread'] = max(0.05, params['emaSpread'] - 0.1)
        elif avg_trades < 10:  # Az işlem
            params['momentumScore'] = max(3, params['momentumScore'] - 1)
            params['volatilityMax'] = min(8, params['volatilityMax'] + 1)

        # Sharpe oranına göre ayarlama
        if avg_sharpe < 0:  # Negatif Sharpe
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['basePosition'] = max(8, params['basePosition'] - 3)

        # Drawdown'a göre ayarlama
        if avg_drawdown > 0.2:  # Yüksek drawdown
            params['stopLoss'] = max(2, params['stopLoss'] - 1)
            params['trailingStop'] = max(1, params['trailingStop'] - 1)
            params['basePosition'] = max(8, params['basePosition'] - 5)

        return params

    def _determine_advanced_strategy_parameters(self, profitability_rate, avg_profit, avg_win_rate, avg_trades, avg_sharpe, avg_drawdown):
        """
        Gelişmiş indikatörler dahil tüm strateji parametrelerini belirler
        """
        # Temel parametreler
        params = {
            'stopLoss': 4,
            'takeProfit': 8,
            'trailingStop': 3,
            'basePosition': 15,
            'maxPosition': 25,
            'minPosition': 8,
            'rsiLower': 55,
            'rsiUpper': 75,
            'emaSpread': 0.2,
            'momentumScore': 8,  # 12 puan sisteminde
            'priceChange3': 0.5,
            'priceChange10': 1.0,
            'volatilityMax': 4,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True,
            # Gelişmiş indikatör ayarları
            'adxMinimum': 20,
            'williamsRThreshold': -80,
            'cciRange': 200,
            'useVWAP': True,
            'useParabolicSAR': True,
            'useIchimoku': True,
            'stochasticThreshold': "30,70"
        }

        # Karlılık oranına göre gelişmiş ayarlama
        if profitability_rate == 0:  # %0 karlı (tüm zararlı) - KRİTİK DURUM
            # Radikal değişiklik gerekli - çok daha az seçici
            params.update({
                'stopLoss': 2,  # Çok dar stop loss
                'takeProfit': 4,  # Düşük hedef
                'basePosition': 8,  # Küçük pozisyon
                'maxPosition': 12,
                'momentumScore': 4,  # 12 puan sisteminde çok az seçici
                'rsiLower': 40,  # Çok geniş RSI aralığı
                'rsiUpper': 85,
                'volatilityMax': 8,  # Yüksek volatilite toleransı
                'priceChange3': 0.2,  # Düşük momentum eşiği
                'priceChange10': 0.4,
                'emaSpread': 0.1,  # Düşük trend eşiği
                # Gelişmiş indikatörler - daha az seçici
                'adxMinimum': 15,  # Çok düşük trend eşiği
                'williamsRThreshold': -85,  # Çok geniş aralık
                'cciRange': 300,  # Çok geniş CCI aralığı
                'useVWAP': False,  # VWAP filtresini kapat (daha az seçici)
                'useParabolicSAR': True,  # SAR'ı kullan
                'useIchimoku': False,  # Ichimoku'yu kapat (çok seçici)
                'stochasticThreshold': "20,80"  # Geniş stochastic aralığı
            })
        elif profitability_rate < 0.3:  # %30- karlı - KÖTÜ DURUM
            # Önemli değişiklikler gerekli
            params.update({
                'stopLoss': 3,
                'takeProfit': 6,
                'basePosition': 10,
                'maxPosition': 15,
                'momentumScore': 6,  # Daha az seçici
                'rsiLower': 45,
                'rsiUpper': 80,
                'volatilityMax': 6,
                'priceChange3': 0.3,
                'priceChange10': 0.6,
                'emaSpread': 0.15,
                # Gelişmiş indikatörler
                'adxMinimum': 18,
                'williamsRThreshold': -82,
                'cciRange': 250,
                'useVWAP': True,
                'useParabolicSAR': True,
                'useIchimoku': True,
                'stochasticThreshold': "25,75"
            })
        elif profitability_rate > 0.7:  # %70+ karlı - İYİ DURUM
            # Agresif strateji - daha yüksek kar hedefi
            params.update({
                'takeProfit': 12,
                'basePosition': 20,
                'maxPosition': 30,
                'momentumScore': 10,  # Daha seçici
                'rsiLower': 60,
                'rsiUpper': 70,
                'volatilityMax': 3,
                'priceChange3': 0.8,
                'priceChange10': 1.5,
                'emaSpread': 0.3,
                # Gelişmiş indikatörler - daha seçici
                'adxMinimum': 25,
                'williamsRThreshold': -70,
                'cciRange': 150,
                'useVWAP': True,
                'useParabolicSAR': True,
                'useIchimoku': True,
                'stochasticThreshold': "25,75"
            })

        # Kazanma oranına göre gelişmiş ayarlama
        if avg_win_rate < 0.3:  # %30- kazanma oranı - ÇOK KÖTÜ
            params.update({
                'stopLoss': max(1.5, params['stopLoss'] - 1),
                'takeProfit': min(15, params['takeProfit'] + 3),
                'momentumScore': max(3, params['momentumScore'] - 2),
                'adxMinimum': max(15, params['adxMinimum'] - 5),
                'williamsRThreshold': min(-70, params['williamsRThreshold'] + 10),
                'cciRange': min(300, params['cciRange'] + 50),
                'useVWAP': False,  # Daha az filtre
                'stochasticThreshold': "20,80"
            })
        elif avg_win_rate > 0.6:  # %60+ kazanma oranı - İYİ
            params.update({
                'takeProfit': max(6, params['takeProfit'] - 2),
                'basePosition': min(25, params['basePosition'] + 5),
                'momentumScore': min(11, params['momentumScore'] + 1),
                'adxMinimum': min(30, params['adxMinimum'] + 3),
                'williamsRThreshold': max(-85, params['williamsRThreshold'] - 5),
                'cciRange': max(100, params['cciRange'] - 25)
            })

        # İşlem sayısına göre gelişmiş ayarlama
        if avg_trades < 3:  # Çok az işlem - KRİTİK
            params.update({
                'momentumScore': max(3, params['momentumScore'] - 3),
                'volatilityMax': min(10, params['volatilityMax'] + 4),
                'rsiLower': max(30, params['rsiLower'] - 15),
                'rsiUpper': min(90, params['rsiUpper'] + 15),
                'priceChange3': max(0.1, params['priceChange3'] - 0.4),
                'priceChange10': max(0.2, params['priceChange10'] - 0.6),
                'emaSpread': max(0.05, params['emaSpread'] - 0.15),
                # Gelişmiş indikatörler - çok daha az seçici
                'adxMinimum': max(12, params['adxMinimum'] - 8),
                'williamsRThreshold': min(-60, params['williamsRThreshold'] + 20),
                'cciRange': min(400, params['cciRange'] + 100),
                'useVWAP': False,
                'useIchimoku': False,
                'stochasticThreshold': "15,85"
            })
        elif avg_trades > 50:  # Çok fazla işlem
            params.update({
                'momentumScore': min(11, params['momentumScore'] + 2),
                'volatilityMax': max(2, params['volatilityMax'] - 2),
                'adxMinimum': min(30, params['adxMinimum'] + 5),
                'williamsRThreshold': max(-90, params['williamsRThreshold'] - 10),
                'cciRange': max(100, params['cciRange'] - 50)
            })

        # Sharpe oranına göre gelişmiş ayarlama
        if avg_sharpe < -0.5:  # Çok kötü Sharpe
            params.update({
                'stopLoss': max(1.5, params['stopLoss'] - 1),
                'basePosition': max(5, params['basePosition'] - 5),
                'momentumScore': max(3, params['momentumScore'] - 2),
                'adxMinimum': max(12, params['adxMinimum'] - 5),
                'useVWAP': False,
                'useIchimoku': False
            })
        elif avg_sharpe > 1:  # Çok iyi Sharpe
            params.update({
                'basePosition': min(30, params['basePosition'] + 5),
                'momentumScore': min(11, params['momentumScore'] + 1),
                'adxMinimum': min(30, params['adxMinimum'] + 3)
            })

        # Drawdown'a göre gelişmiş ayarlama
        if avg_drawdown > 0.3:  # %30+ drawdown - ÇOK RİSKLİ
            params.update({
                'stopLoss': max(1.5, params['stopLoss'] - 1.5),
                'trailingStop': max(1, params['trailingStop'] - 1),
                'basePosition': max(5, params['basePosition'] - 8),
                'maxPosition': max(10, params['maxPosition'] - 10),
                'momentumScore': min(11, params['momentumScore'] + 2),
                'adxMinimum': min(30, params['adxMinimum'] + 5),
                'williamsRThreshold': max(-90, params['williamsRThreshold'] - 10),
                'cciRange': max(100, params['cciRange'] - 50)
            })

        return params

    def _analyze_and_generate_cyclic_strategy(self, results, iteration, optimization_history):
        """
        🧠 AKILLI DÖNGÜSEL AI ANALİZİ - Her iterasyonda daha iyi sonuçlar
        """
        try:
            print(f"\n🧠 AKILLI AI ANALİZİ - İterasyon {iteration}")

            # Mevcut iterasyon istatistikleri
            current_stats = self._calculate_iteration_stats(results)
            print(f"📊 Mevcut kar: {current_stats['avg_profit']:.2f}%")
            print(f"📈 İşlem sayısı: {current_stats['avg_trades']:.1f}")

            # Önceki iterasyonlarla karşılaştırma
            improvement_analysis = self._analyze_improvement_trend(optimization_history, current_stats)

            # Performans geçmişini analiz et
            performance_history = self._analyze_performance_history(optimization_history)
            print(f"📈 Performans trendi: {performance_history.get('trend', 'belirsiz')}")

            # HAFIZA SİSTEMİ: Başarısız parametreleri analiz et
            failed_strategies = self._analyze_failed_strategies(optimization_history)
            successful_strategies = [h for h in optimization_history if h.get('stats', {}).get('averageProfit', 0) > 0]

            print(f"❌ Başarısız strateji: {len(failed_strategies)}")
            print(f"✅ Başarılı strateji: {len(successful_strategies)}")

            # AKILLI PARAMETRE ÜRETİMİ
            strategy_params = self._generate_smart_cyclic_parameters(
                current_stats,
                improvement_analysis,
                iteration,
                failed_strategies,
                successful_strategies,
                performance_history,
                optimization_history
            )

            # Parametre validasyonu ve düzeltme
            strategy_params = self._validate_and_fix_parameters(strategy_params)

            # Döngüsel analiz metni oluştur
            analysis_text = self._generate_smart_cyclic_analysis_text(
                current_stats, improvement_analysis, iteration, performance_history
            )

            print(f"🎯 Yeni parametreler:")
            print(f"   Momentum: {strategy_params.get('momentumScore', 'N/A')}")
            print(f"   ADX: {strategy_params.get('adxMinimum', 'N/A')}")
            print(f"   RSI: {strategy_params.get('rsiLower', 'N/A')}-{strategy_params.get('rsiUpper', 'N/A')}")
            print(f"   Stop/Take: {strategy_params.get('stopLoss', 'N/A')}%/{strategy_params.get('takeProfit', 'N/A')}%")

            return {
                'analysis': analysis_text,
                'parameters': strategy_params,
                'expected': {
                    'winRate': f"{self._predict_win_rate_cyclic(strategy_params, current_stats):.1f}",
                    'avgProfit': f"{self._predict_profit_cyclic(strategy_params, current_stats):.1f}",
                    'riskLevel': self._determine_risk_level_cyclic(strategy_params, current_stats)
                },
                'iteration': iteration,
                'improvement': improvement_analysis,
                'performance_history': performance_history
            }

        except Exception as e:
            print(f"❌ Döngüsel AI analiz hatası: {e}")
            import traceback
            traceback.print_exc()
            # Fallback olarak basit strateji döndür
            return self._analyze_and_generate_strategy(results)

    def _calculate_iteration_stats(self, results):
        """Mevcut iterasyon istatistiklerini hesapla"""
        profitable_results = [r for r in results if r.get('profit_percentage', 0) > 0]

        total_results = len(results)
        profitable_count = len(profitable_results)
        profitability_rate = profitable_count / total_results if total_results > 0 else 0

        avg_profit = sum(r.get('profit_percentage', 0) for r in profitable_results) / len(profitable_results) if profitable_results else 0
        avg_win_rate = sum(r.get('win_rate', 0) for r in results) / len(results) if results else 0
        avg_trades = sum(r.get('total_trades', 0) for r in results) / len(results) if results else 0
        avg_sharpe = sum(r.get('sharpe_ratio', 0) for r in results) / len(results) if results else 0
        avg_drawdown = sum(r.get('max_drawdown', 0) for r in results) / len(results) if results else 0

        return {
            'total_results': total_results,
            'profitable_count': profitable_count,
            'profitability_rate': profitability_rate,
            'avg_profit': avg_profit,
            'avg_win_rate': avg_win_rate,
            'avg_trades': avg_trades,
            'avg_sharpe': avg_sharpe,
            'avg_drawdown': avg_drawdown
        }

    def _analyze_improvement_trend(self, optimization_history, current_stats):
        """İyileştirme trendini analiz et"""
        if not optimization_history:
            return {
                'is_first_iteration': True,
                'trend': 'başlangıç',
                'improvement_rate': 0,
                'best_iteration': 1
            }

        # Son iterasyonla karşılaştır
        last_iteration = optimization_history[-1]['stats']
        profit_improvement = current_stats['avg_profit'] - last_iteration.get('averageProfit', 0)

        # En iyi iterasyonu bul
        best_profit = max([h['stats'].get('averageProfit', -999) for h in optimization_history] + [current_stats['avg_profit']])
        best_iteration = 1
        for i, h in enumerate(optimization_history):
            if h['stats'].get('averageProfit', -999) == best_profit:
                best_iteration = i + 1
                break

        # Trend analizi
        if len(optimization_history) >= 2:
            recent_profits = [h['stats'].get('averageProfit', 0) for h in optimization_history[-2:]]
            if recent_profits[1] > recent_profits[0]:
                trend = 'yükseliş'
            elif recent_profits[1] < recent_profits[0]:
                trend = 'düşüş'
            else:
                trend = 'sabit'
        else:
            trend = 'belirsiz'

        return {
            'is_first_iteration': False,
            'trend': trend,
            'improvement_rate': profit_improvement,
            'best_iteration': best_iteration,
            'iterations_count': len(optimization_history) + 1
        }

    def _determine_cyclic_strategy_parameters(self, current_stats, improvement_analysis, iteration):
        """Döngüsel optimizasyon için strateji parametreleri belirle"""
        # Temel parametrelerle başla
        params = self._determine_advanced_strategy_parameters(
            current_stats['profitability_rate'],
            current_stats['avg_profit'],
            current_stats['avg_win_rate'],
            current_stats['avg_trades'],
            current_stats['avg_sharpe'],
            current_stats['avg_drawdown']
        )

        # İterasyon bazlı radikal değişiklikler - HER İTERASYONDA FARKLI SONUÇ GARANTİSİ
        import random
        random.seed(iteration * 42)  # Tekrarlanabilir ama farklı rastgelelik

        # İşlem garantisi kontrolü - eğer önceki iterasyonda hiç işlem yoksa
        if (not improvement_analysis['is_first_iteration'] and
            current_stats['avg_trades'] < 1):
            # HİÇ İŞLEM YOK - ACİL MÜDAHALE
            params.update({
                'momentumScore': 2,  # En düşük seviye
                'adxMinimum': 10,   # En düşük trend eşiği
                'volatilityMax': 15, # Çok yüksek volatilite toleransı
                'rsiLower': 20,     # Çok geniş RSI
                'rsiUpper': 95,
                'priceChange3': 0.05,  # Çok düşük momentum
                'priceChange10': 0.1,
                'emaSpread': 0.02,     # Çok düşük trend eşiği
                'useVWAP': False,      # Tüm filtreleri kapat
                'useIchimoku': False,
                'useParabolicSAR': False,
                'williamsRThreshold': -95,
                'cciRange': 500,
                'stochasticThreshold': "5,95"
            })
            print(f"İterasyon {iteration}: HİÇ İŞLEM YOK - ACİL MÜDAHALE!")

        # Döngüsel optimizasyon ayarlamaları
        elif not improvement_analysis['is_first_iteration']:
            # İyileştirme oranına göre ayarlama
            improvement_rate = improvement_analysis['improvement_rate']
            avg_trades = current_stats['avg_trades']

            if avg_trades < 5:  # Az işlem - ÖNCELİK: İŞLEM SAYISINI ARTIR
                params.update({
                    'momentumScore': max(3, params['momentumScore'] - 2),
                    'adxMinimum': max(12, params['adxMinimum'] - 5),
                    'volatilityMax': min(10, params['volatilityMax'] + 3),
                    'rsiLower': max(30, params['rsiLower'] - 15),
                    'rsiUpper': min(85, params['rsiUpper'] + 10),
                    'priceChange3': max(0.1, params['priceChange3'] - 0.3),
                    'priceChange10': max(0.2, params['priceChange10'] - 0.5),
                    'useVWAP': False,
                    'useIchimoku': False,
                    'williamsRThreshold': -85,
                    'cciRange': 350
                })
                print(f"İterasyon {iteration}: Az işlem ({avg_trades:.1f}) - Filtreleri gevşetiyorum")
            elif improvement_rate < -0.01:  # Kötüleşme ama işlem var
                params.update({
                    'momentumScore': max(4, params['momentumScore'] - 1),
                    'adxMinimum': max(15, params['adxMinimum'] - 3),
                    'volatilityMax': min(8, params['volatilityMax'] + 1),
                    'rsiLower': max(35, params['rsiLower'] - 10),
                    'rsiUpper': min(80, params['rsiUpper'] + 5),
                    'useVWAP': iteration % 2 == 0
                })
                print(f"İterasyon {iteration}: Kötüleşme var - Orta seviye değişiklik")
            elif improvement_rate < 0.01:  # Az iyileştirme - ORTA DEĞİŞİKLİK
                params.update({
                    'momentumScore': max(3, params['momentumScore'] - 2),
                    'adxMinimum': max(12, params['adxMinimum'] - 5),
                    'volatilityMax': min(10, params['volatilityMax'] + 2),
                    'rsiLower': max(30, params['rsiLower'] - 10),
                    'rsiUpper': min(85, params['rsiUpper'] + 10),
                    'stopLoss': max(1.5, params['stopLoss'] - 1),
                    'basePosition': max(8, params['basePosition'] - 3),
                    'useVWAP': iteration % 2 == 0,
                    'williamsRThreshold': min(-70, params['williamsRThreshold'] + 10),
                    'cciRange': min(350, params['cciRange'] + 50)
                })
            else:  # İyileştirme var - DENEYSEL DEĞİŞİKLİK
                # Her iterasyonda farklı bir parametre değiştir
                change_type = iteration % 5
                if change_type == 0:
                    params['momentumScore'] = max(3, params['momentumScore'] - random.randint(1, 3))
                elif change_type == 1:
                    params['volatilityMax'] = min(8, params['volatilityMax'] + random.randint(1, 2))
                elif change_type == 2:
                    params['rsiLower'] = max(30, params['rsiLower'] - random.randint(5, 15))
                elif change_type == 3:
                    params['adxMinimum'] = max(12, params['adxMinimum'] - random.randint(2, 5))
                else:
                    params['basePosition'] = max(8, params['basePosition'] - random.randint(2, 5))

        # Her iterasyonda garantili değişiklik (aynı sonuçları önlemek için)
        iteration_mod = iteration % 10

        if iteration_mod == 1:
            params['commission'] = 0.005  # Düşük komisyon
            params['momentumScore'] = max(3, params['momentumScore'] - 1)
        elif iteration_mod == 2:
            params['commission'] = 0.015  # Yüksük komisyon
            params['volatilityMax'] = min(8, params['volatilityMax'] + 1)
        elif iteration_mod == 3:
            params['useVWAP'] = False
            params['useIchimoku'] = False
            params['momentumScore'] = max(4, params['momentumScore'] - 1)
        elif iteration_mod == 4:
            params['useVWAP'] = True
            params['useIchimoku'] = True
            params['adxMinimum'] = max(18, params['adxMinimum'] - 2)
        else:
            # Kontrollü rastgele değişiklik (çok radikal olmasın)
            params['momentumScore'] = max(3, min(9, params['momentumScore'] + random.randint(-2, 1)))
            params['volatilityMax'] = max(3, min(8, params['volatilityMax'] + random.randint(-1, 2)))
            params['rsiLower'] = max(30, min(55, params['rsiLower'] + random.randint(-10, 5)))

        # İşlem garantisi - son kontrol
        if params['momentumScore'] < 3:
            params['momentumScore'] = 3
            print(f"İterasyon {iteration}: Momentum score çok düşük, 3'e yükseltildi")

        if params['adxMinimum'] < 12:
            params['adxMinimum'] = 12
            print(f"İterasyon {iteration}: ADX minimum çok düşük, 12'ye yükseltildi")

        if params['volatilityMax'] > 10:
            params['volatilityMax'] = 10
            print(f"İterasyon {iteration}: Volatilite max çok yüksek, 10'a düşürüldü")

        # İterasyon sayısına göre ek değişiklik
        if iteration > 2:
            params['stopLoss'] = max(1, params['stopLoss'] - (iteration - 2) * 0.5)
            params['basePosition'] = max(5, params['basePosition'] - (iteration - 2) * 2)

        print(f"İterasyon {iteration} parametreleri: Momentum={params['momentumScore']}, ADX={params['adxMinimum']}, Vol={params['volatilityMax']}")

        return params

    def _generate_cyclic_analysis_text(self, current_stats, improvement_analysis, iteration):
        """Döngüsel analiz için metin oluştur"""
        text = f"🔄 İterasyon {iteration} Analizi: "

        if improvement_analysis['is_first_iteration']:
            text += "Başlangıç analizi tamamlandı. "
        else:
            improvement = improvement_analysis['improvement_rate']
            if improvement > 1:
                text += f"Mükemmel iyileştirme! (+{improvement:.2f}%) "
            elif improvement > 0:
                text += f"Pozitif iyileştirme (+{improvement:.2f}%) "
            elif improvement > -0.5:
                text += f"Küçük düşüş ({improvement:.2f}%) "
            else:
                text += f"Önemli düşüş ({improvement:.2f}%) "

        text += f"Toplam {current_stats['total_results']} para biriminde "
        text += f"{current_stats['profitable_count']} tanesi karlı (%{current_stats['profitability_rate']*100:.1f}). "

        if current_stats['avg_trades'] < 3:
            text += "⚠️ Çok az işlem! Filtreler gevşetiliyor. "
        elif current_stats['avg_trades'] > 50:
            text += "⚠️ Çok fazla işlem! Filtreler sıkılaştırılıyor. "

        if not improvement_analysis['is_first_iteration']:
            trend = improvement_analysis['trend']
            if trend == 'yükseliş':
                text += "📈 Trend pozitif, mevcut yönde devam ediliyor. "
            elif trend == 'düşüş':
                text += "📉 Trend negatif, strateji değiştiriliyor. "
            else:
                text += "📊 Trend sabit, deneysel değişiklikler yapılıyor. "

        text += f"Bu iterasyon için {len([k for k, v in current_stats.items() if 'use' in str(k) and v])} gelişmiş filtre aktif."

        return text

    def _generate_analysis_text(self, total_results, profitable_count, profitability_rate, avg_profit, avg_win_rate, avg_trades):
        """
        Analiz sonuçlarına göre açıklama metni oluşturur
        """
        text = f"Toplam {total_results} para biriminde yapılan analiz sonucunda, "
        text += f"{profitable_count} tanesi karlı çıktı (%{profitability_rate*100:.1f} başarı oranı). "

        if profitability_rate == 0:
            text += "⚠️ KRİTİK DURUM: Hiçbir para biriminde kar elde edilemedi! "
            text += "Mevcut strateji çok seçici ve muhtemelen piyasa koşullarına uygun değil. "
            if avg_trades < 5:
                text += "Ayrıca çok az işlem yapılıyor, bu da fırsat kaybına neden oluyor. "
            text += "Radikal parametre değişiklikleri öneriliyor. "
        elif profitability_rate > 0.6:
            text += "Bu oldukça iyi bir sonuç! Mevcut strateji genel olarak başarılı görünüyor. "
        elif profitability_rate > 0.4:
            text += "Orta seviyede bir başarı oranı. Strateji parametrelerinde iyileştirme yapılabilir. "
        else:
            text += "Düşük başarı oranı. Strateji parametrelerinde önemli değişiklikler gerekli. "

        text += f"Ortalama kazanma oranı %{avg_win_rate*100:.1f}, "
        text += f"ortalama işlem sayısı {avg_trades:.0f}. "

        if avg_trades < 3:
            text += "🚨 KRİTİK: Çok az işlem yapılıyor! Tüm filtreler gevşetildi. "
        elif avg_trades < 5:
            text += "⚠️ Çok az işlem yapılıyor! Strateji daha az seçici olmalı. "
        elif avg_trades > 50:
            text += "Çok fazla işlem yapılıyor, komisyon maliyetleri yüksek olabilir. "

        if profitable_count > 0:
            if avg_profit > 5:
                text += "Karlı işlemlerde yüksek getiri elde ediliyor. "
            elif avg_profit > 2:
                text += "Karlı işlemlerde orta seviyede getiri elde ediliyor. "
            else:
                text += "Karlı işlemlerde düşük getiri elde ediliyor. "

        # Gelişmiş indikatör önerileri
        if profitability_rate == 0:
            text += "🔧 Gelişmiş İndikatör Ayarları: VWAP ve Ichimoku filtreleri kapatıldı, ADX eşiği düşürüldü, Williams %R ve CCI aralıkları genişletildi. "
        elif avg_trades < 3:
            text += "🔧 Gelişmiş İndikatör Ayarları: Tüm filtreler minimum seçicilik için ayarlandı. "
        else:
            text += "🔧 Gelişmiş İndikatör Ayarları: 12 teknik indikatör optimal değerlere ayarlandı. "

        text += "Aşağıdaki parametreler 12 indikatörlü gelişmiş sistem için optimize edilmiştir."

        return text

    def _determine_risk_level(self, avg_drawdown, avg_sharpe):
        """
        Risk seviyesini belirler
        """
        if avg_drawdown > 0.3 or avg_sharpe < -1:
            return "Yüksek"
        elif avg_drawdown > 0.15 or avg_sharpe < 0:
            return "Orta"
        else:
            return "Düşük"

    def data_manager_page(self):
        """
        Veri yöneticisi sayfasını gösterir

        Returns:
            str: HTML sayfası
        """
        return render_template('data_manager.html')

    def download_dataset(self):
        """
        Veri setini indirir ve kaydeder

        Returns:
            dict: İndirme sonucu
        """
        try:
            data = request.get_json()

            symbols = data.get('symbols', [])
            timeframes = data.get('timeframes', [])
            start_date = data.get('start_date')
            end_date = data.get('end_date')
            dataset_name = data.get('dataset_name')

            if not all([symbols, timeframes, start_date, end_date, dataset_name]):
                return jsonify({'success': False, 'error': 'Eksik parametreler'}), 400

            # Veri setini indir ve kaydet
            dataset_info = self._download_and_save_dataset(
                symbols, timeframes, start_date, end_date, dataset_name
            )

            return jsonify({'success': True, 'dataset': dataset_info})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def list_datasets(self):
        """
        Mevcut veri setlerini listeler

        Returns:
            list: Veri setleri listesi
        """
        try:
            datasets_dir = 'data/datasets'
            if not os.path.exists(datasets_dir):
                return jsonify([])

            datasets = []
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    dataset_name = filename[:-5]  # .json uzantısını kaldır
                    info_path = os.path.join(datasets_dir, filename)

                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    datasets.append({
                        'name': dataset_name,
                        'symbols_count': len(info['symbols']),
                        'timeframes_count': len(info['timeframes']),
                        'created_at': info['created_at'],
                        'is_active': info.get('is_active', False)
                    })

            # Oluşturulma tarihine göre sırala
            datasets.sort(key=lambda x: x['created_at'], reverse=True)

            return jsonify(datasets)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def dataset_details(self, dataset_name):
        """
        Veri seti detaylarını döndürür

        Args:
            dataset_name: Veri seti adı

        Returns:
            dict: Veri seti detayları
        """
        try:
            info_path = f'data/datasets/{dataset_name}.json'

            if not os.path.exists(info_path):
                return jsonify({'error': 'Veri seti bulunamadı'}), 404

            with open(info_path, 'r', encoding='utf-8') as f:
                info = json.load(f)

            # Dosya boyutunu hesapla
            data_path = f'data/datasets/{dataset_name}.pkl'
            file_size = 0
            if os.path.exists(data_path):
                file_size = os.path.getsize(data_path)
                file_size_str = self._format_file_size(file_size)
            else:
                file_size_str = 'Bilinmiyor'

            info['file_size'] = file_size_str

            return jsonify(info)

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    def set_active_dataset(self):
        """
        Aktif veri setini ayarlar

        Returns:
            dict: Sonuç
        """
        try:
            data = request.get_json()
            dataset_name = data.get('dataset_name')

            if not dataset_name:
                return jsonify({'success': False, 'error': 'Veri seti adı gerekli'}), 400

            # Tüm veri setlerini pasif yap
            datasets_dir = 'data/datasets'
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    info_path = os.path.join(datasets_dir, filename)
                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    info['is_active'] = False

                    with open(info_path, 'w', encoding='utf-8') as f:
                        json.dump(info, f, ensure_ascii=False, indent=2)

            # Seçili veri setini aktif yap
            info_path = f'data/datasets/{dataset_name}.json'
            if os.path.exists(info_path):
                with open(info_path, 'r', encoding='utf-8') as f:
                    info = json.load(f)

                info['is_active'] = True

                with open(info_path, 'w', encoding='utf-8') as f:
                    json.dump(info, f, ensure_ascii=False, indent=2)

                return jsonify({'success': True})
            else:
                return jsonify({'success': False, 'error': 'Veri seti bulunamadı'}), 404

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def delete_dataset(self, dataset_name):
        """
        Veri setini siler

        Args:
            dataset_name: Veri seti adı

        Returns:
            dict: Sonuç
        """
        try:
            info_path = f'data/datasets/{dataset_name}.json'
            data_path = f'data/datasets/{dataset_name}.pkl'

            # Dosyaları sil
            if os.path.exists(info_path):
                os.remove(info_path)

            if os.path.exists(data_path):
                os.remove(data_path)

            return jsonify({'success': True})

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    def _download_and_save_dataset(self, symbols, timeframes, start_date, end_date, dataset_name):
        """
        Veri setini indirir ve kaydeder
        """
        # Klasör oluştur
        datasets_dir = 'data/datasets'
        os.makedirs(datasets_dir, exist_ok=True)

        dataset_data = {}
        total_records = 0

        # Her sembol ve timeframe için veri indir
        for symbol in symbols:
            dataset_data[symbol] = {}
            for timeframe in timeframes:
                try:
                    df = self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date, end_date)
                    if not df.empty:
                        dataset_data[symbol][timeframe] = df
                        total_records += len(df)
                except Exception as e:
                    print(f"Veri indirme hatası {symbol} {timeframe}: {e}")
                    continue

        # Veri setini kaydet
        data_path = os.path.join(datasets_dir, f'{dataset_name}.pkl')
        with open(data_path, 'wb') as f:
            pickle.dump(dataset_data, f)

        # Bilgi dosyasını kaydet
        info = {
            'name': dataset_name,
            'symbols': symbols,
            'timeframes': timeframes,
            'start_date': start_date,
            'end_date': end_date,
            'total_records': total_records,
            'created_at': datetime.now().isoformat(),
            'is_active': False
        }

        info_path = os.path.join(datasets_dir, f'{dataset_name}.json')
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(info, f, ensure_ascii=False, indent=2)

        return info

    def _format_file_size(self, size_bytes):
        """
        Dosya boyutunu formatlar
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

    def _analyze_failed_strategies(self, optimization_history):
        """
        Başarısız stratejileri analiz et ve tekrarlanmasını önle
        """
        failed_strategies = {
            'zero_trades': [],  # Hiç işlem yapmayan parametreler
            'negative_profit': [],  # Zararlı parametreler
            'low_trades': []  # Az işlem yapan parametreler
        }

        for hist in optimization_history:
            stats = hist['stats']

            # Hiç işlem yapmayan stratejiler
            if stats['averageTrades'] < 1:
                failed_strategies['zero_trades'].append({
                    'iteration': hist['iteration'],
                    'avg_trades': stats['averageTrades'],
                    'profit': stats['averageProfit']
                })

            # Zararlı stratejiler
            elif stats['averageProfit'] < -0.1:
                failed_strategies['negative_profit'].append({
                    'iteration': hist['iteration'],
                    'avg_trades': stats['averageTrades'],
                    'profit': stats['averageProfit']
                })

            # Az işlem yapan stratejiler
            elif stats['averageTrades'] < 5:
                failed_strategies['low_trades'].append({
                    'iteration': hist['iteration'],
                    'avg_trades': stats['averageTrades'],
                    'profit': stats['averageProfit']
                })

        return failed_strategies

    def _determine_cyclic_strategy_parameters_with_memory(self, current_stats, improvement_analysis, iteration, failed_strategies, optimization_history=None):
        """
        Gelişmiş AI Döngüsel Optimizasyon Sistemi
        ML Regime Detection + Multi-timeframe + Ultimate Hybrid yaklaşımı
        """
        print(f"\n🤖 AI Döngüsel Optimizasyon - İterasyon {iteration}")

        # 1. ML Regime Detection
        regime_info = self._detect_market_regime()
        print(f"   📊 Tespit Edilen Rejim: {regime_info.get('regime', 'Unknown')}")
        print(f"   🔥 Güven Seviyesi: %{regime_info.get('confidence', 0)*100:.1f}")

        # 2. Multi-timeframe Analysis
        mtf_info = self._analyze_multi_timeframe()
        print(f"   🚀 Multi-timeframe Sinyal: {mtf_info.get('signal', 0)}")
        print(f"   💡 MTF Güven: {mtf_info.get('confidence', 0)}")

        # 3. Performance-based Strategy Selection
        strategy_type = self._determine_strategy_type(current_stats, iteration, optimization_history)
        print(f"   ⚡ Seçilen Strateji Tipi: {strategy_type}")

        # 4. AI-Enhanced Parameter Generation
        if strategy_type == "ULTIMATE_HYBRID":
            params = self._get_ultimate_hybrid_parameters(regime_info, mtf_info, current_stats)
        elif strategy_type == "ML_ADAPTIVE":
            params = self._get_ml_adaptive_parameters(regime_info, current_stats)
        elif strategy_type == "MULTI_TIMEFRAME":
            params = self._get_multi_timeframe_parameters(mtf_info, current_stats)
        elif strategy_type == "CONVERGENCE":
            params = self._apply_convergence_algorithm(
                self._get_btc_optimized_base_parameters(),
                optimization_history,
                iteration
            )
        else:
            # Fallback to basic parameters
            params = self._get_btc_optimized_base_parameters()

        # 5. Iterative Improvement Logic
        params = self._apply_iterative_improvements(params, current_stats, iteration, failed_strategies)

        # 6. Strateji tipini parametrelere ekle (optimization_history için)
        params['strategy_type'] = strategy_type

        print(f"   🎯 Final Parametreler: TP={params.get('takeProfit', 0)}%, SL={params.get('stopLoss', 0)}%, Pos={params.get('basePosition', 0)}%")
        print(f"   📋 Strateji Tipi: {strategy_type}")

        return params

    def _get_btc_optimized_base_parameters(self):
        """
        BTC/USDT için optimize edilmiş temel parametreler
        2025 piyasa koşullarına göre ayarlanmış
        """
        return {
            # Risk Yönetimi - BTC volatilitesine uygun (Agresif Kar Hedefi)
            'stopLoss': 4,          # %4 stop loss (daha sıkı risk kontrolü)
            'takeProfit': 20,       # %20 take profit (yüksek kar hedefi)
            'trailingStop': 2.5,    # %2.5 trailing stop (sıkı takip)

            # Pozisyon Yönetimi
            'basePosition': 25,     # %25 temel pozisyon (daha agresif)
            'maxPosition': 40,      # %40 maksimum pozisyon
            'minPosition': 15,      # %15 minimum pozisyon

            # Teknik İndikatörler - ÇOK DAHA ESNEK FİLTRELER
            'rsiLower': 25,         # RSI alt sınır (çok geniş)
            'rsiUpper': 95,         # RSI üst sınır (çok geniş)
            'emaSpread': 0.05,      # EMA spread (çok dar)
            'momentumScore': 2,     # Momentum skoru (çok düşük)

            # Fiyat Değişim Filtreleri - Çok esnek
            'priceChange3': 0.1,    # %0.1 (çok düşük)
            'priceChange10': 0.3,   # %0.3 (çok düşük)
            'volatilityMax': 10,    # %10 maksimum volatilite (yüksek)

            # Gelişmiş İndikatörler - Esnek
            'adxMinimum': 10,       # ADX minimum (çok düşük)
            'useVWAP': True,        # VWAP kullan
            'useIchimoku': False,   # Ichimoku kullanma (karmaşık)
            'useParabolicSAR': False, # SAR kullanma (gürültülü)

            # Diğer
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

    def _apply_convergence_algorithm(self, base_params, optimization_history, iteration):
        """
        Gradient Descent benzeri convergence algoritması
        En iyi sonuçlara doğru yakınsama sağlar
        """
        # En iyi 3 iterasyonu bul
        best_iterations = sorted(
            optimization_history,
            key=lambda x: x['stats'].get('averageProfit', -999),
            reverse=True
        )[:3]

        if not best_iterations:
            return base_params

        # En iyi parametrelerin ağırlıklı ortalamasını al
        best_params = best_iterations[0].get('applied_params', {})
        if not best_params:
            return base_params

        # Convergence faktörü (iterasyon arttıkça daha küçük değişiklikler)
        convergence_factor = max(0.1, 1.0 / (iteration * 0.3))

        # Parametreleri optimize et
        optimized_params = base_params.copy()

        for key, best_value in best_params.items():
            if key in optimized_params and isinstance(best_value, (int, float)):
                current_value = optimized_params[key]

                # Gradient yönünde hareket et
                direction = (best_value - current_value) * convergence_factor

                if isinstance(best_value, int):
                    optimized_params[key] = max(1, int(current_value + direction))
                else:
                    optimized_params[key] = max(0.001, current_value + direction)

        print(f"İterasyon {iteration}: Convergence algoritması uygulandı (faktör: {convergence_factor:.3f})")
        return optimized_params

    def _get_data_for_simulation(self, symbol, timeframe, start_date):
        """
        Simülasyon için veri al (önce yerel veri seti, sonra online)

        Args:
            symbol: Para birimi
            timeframe: Zaman dilimi
            start_date: Başlangıç tarihi

        Returns:
            DataFrame: OHLCV verileri
        """
        # Aktif veri setini kontrol et
        active_dataset = self._get_active_dataset()

        if active_dataset and symbol in active_dataset and timeframe in active_dataset[symbol]:
            # Yerel veri setinden al
            df = active_dataset[symbol][timeframe].copy()

            # Tarih filtreleme
            if start_date:
                try:
                    start_datetime = pd.to_datetime(start_date)
                    df = df[df.index >= start_datetime]
                except:
                    pass

            return df
        else:
            # Online'dan al
            return self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)

    def _get_active_dataset(self):
        """
        Aktif veri setini yükle

        Returns:
            dict: Veri seti veya None
        """
        try:
            datasets_dir = 'data/datasets'

            # Aktif veri setini bul
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    info_path = os.path.join(datasets_dir, filename)
                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    if info.get('is_active', False):
                        # Veri dosyasını yükle
                        dataset_name = filename[:-5]  # .json uzantısını kaldır
                        data_path = os.path.join(datasets_dir, f'{dataset_name}.pkl')

                        if os.path.exists(data_path):
                            with open(data_path, 'rb') as f:
                                return pickle.load(f)

            return None

        except Exception as e:
            print(f"Aktif veri seti yükleme hatası: {e}")
            return None

    def _detect_market_regime(self):
        """
        ML tabanlı piyasa rejimi tespiti
        """
        try:
            from app.models.ml_regime_detection import MLRegimeDetector
            ml_detector = MLRegimeDetector()

            # Basit rejim tespiti (gerçek implementasyon için veri gerekli)
            return {
                'regime': 'Sideways_Market',  # Varsayılan
                'confidence': 0.7
            }
        except Exception as e:
            print(f"ML Regime Detection hatası: {str(e)}")
            return {'regime': 'Unknown', 'confidence': 0.5}

    def _analyze_multi_timeframe(self):
        """
        Multi-timeframe analiz
        """
        try:
            from app.models.multi_timeframe import MultiTimeframeAnalyzer
            # Basit multi-timeframe analizi
            return {
                'signal': 1,  # Varsayılan pozitif sinyal
                'confidence': 80
            }
        except Exception as e:
            print(f"Multi-timeframe analiz hatası: {str(e)}")
            return {'signal': 0, 'confidence': 50}

    def _determine_strategy_type(self, current_stats, iteration, optimization_history):
        """
        Performansa göre strateji tipi belirleme
        """
        avg_profit = current_stats.get('avg_profit', 0)
        avg_trades = current_stats.get('avg_trades', 0)

        # İlk 3 iterasyon: Farklı stratejileri dene
        if iteration <= 3:
            strategy_types = ["ULTIMATE_HYBRID", "ML_ADAPTIVE", "MULTI_TIMEFRAME"]
            return strategy_types[(iteration - 1) % 3]

        # Sonraki iterasyonlar: Performansa göre seç
        if optimization_history:
            # En iyi performans gösteren strateji tipini bul
            best_profit = -999
            best_strategy = "ULTIMATE_HYBRID"

            for hist in optimization_history:
                profit = hist['stats'].get('averageProfit', -999)
                strategy_type = hist.get('strategy_type', 'UNKNOWN')
                if profit > best_profit:
                    best_profit = profit
                    best_strategy = strategy_type

            if best_profit > 0:
                return best_strategy

        # Varsayılan: Ultimate Hybrid
        return "ULTIMATE_HYBRID"

    def _get_ultimate_hybrid_parameters(self, regime_info, mtf_info, current_stats):
        """
        Ultimate Hybrid strateji parametreleri
        """
        base_params = {
            'stopLoss': 2,
            'takeProfit': 40,
            'trailingStop': 1,
            'basePosition': 50,
            'maxPosition': 80,
            'minPosition': 30,
            'rsiLower': 10,
            'rsiUpper': 99,
            'emaSpread': 0.001,
            'momentumScore': 0,
            'priceChange3': 0.01,
            'priceChange10': 0.01,
            'volatilityMax': 50,
            'adxMinimum': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

        # Rejim bazlı ayarlamalar
        regime = regime_info.get('regime', 'Unknown')
        confidence = regime_info.get('confidence', 0.5)

        if regime == "Bull_Market":
            base_params.update({
                'takeProfit': 50,
                'basePosition': 60,
                'stopLoss': 1.5
            })
        elif regime == "Bear_Market":
            base_params.update({
                'takeProfit': 25,
                'basePosition': 40,
                'stopLoss': 3,
                'enableShort': True
            })

        # Multi-timeframe bazlı ayarlamalar
        if mtf_info.get('confidence', 0) > 80:
            base_params['basePosition'] = min(90, base_params['basePosition'] + 20)
            base_params['takeProfit'] = base_params['takeProfit'] + 10

        return base_params

    def _get_ml_adaptive_parameters(self, regime_info, current_stats):
        """
        ML Adaptive strateji parametreleri
        """
        regime = regime_info.get('regime', 'Sideways_Market')
        confidence = regime_info.get('confidence', 0.5)

        if regime == "Sideways_Market":
            return {
                'stopLoss': 5,
                'takeProfit': 15,
                'basePosition': int(20 * confidence),
                'maxPosition': int(27 * confidence),
                'rsiLower': 25,
                'rsiUpper': 95,
                'momentumScore': 2,
                'commission': 0.01,
                'enableShort': False,
                'dynamicTrailing': True
            }
        else:
            return self._get_btc_optimized_base_parameters()

    def _get_multi_timeframe_parameters(self, mtf_info, current_stats):
        """
        Multi-timeframe strateji parametreleri
        """
        confidence = mtf_info.get('confidence', 50)

        return {
            'stopLoss': 3,
            'takeProfit': 25,
            'trailingStop': 2,
            'basePosition': min(50, int(30 * confidence / 100)),
            'maxPosition': min(70, int(50 * confidence / 100)),
            'rsiLower': 20,
            'rsiUpper': 95,
            'momentumScore': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

    def _apply_iterative_improvements(self, params, current_stats, iteration, failed_strategies):
        """
        İteratif iyileştirmeler uygula
        """
        avg_profit = current_stats.get('avg_profit', 0)
        avg_trades = current_stats.get('avg_trades', 0)

        # Çok az işlem varsa filtreleri gevşet
        if avg_trades < 3:
            params.update({
                'momentumScore': max(0, params.get('momentumScore', 2) - 1),
                'adxMinimum': max(5, params.get('adxMinimum', 15) - 5),
                'rsiLower': max(5, params.get('rsiLower', 25) - 10),
                'rsiUpper': min(99, params.get('rsiUpper', 85) + 10)
            })
            print(f"   🔧 Az işlem nedeniyle filtreler gevşetildi")

        # Zararlı işlemler varsa risk yönetimini sıkılaştır
        if avg_profit < 0:
            params.update({
                'stopLoss': max(1, params.get('stopLoss', 3) - 0.5),
                'basePosition': max(10, params.get('basePosition', 25) - 5)
            })
            print(f"   🛡️ Zarar nedeniyle risk yönetimi sıkılaştırıldı")

        # Başarılı iterasyonlarda agresifleştir
        if avg_profit > 2:
            params.update({
                'takeProfit': min(50, params.get('takeProfit', 20) + 5),
                'basePosition': min(80, params.get('basePosition', 25) + 10)
            })
            print(f"   🚀 Başarı nedeniyle strateji agresifleştirildi")

        return params

    def _get_btc_optimized_base_parameters(self):
        """
        BTC/USDT için optimize edilmiş temel parametreler
        2025 piyasa koşullarına göre ayarlanmış
        """
        return {
            # Risk Yönetimi - BTC volatilitesine uygun (Agresif Kar Hedefi)
            'stopLoss': 4,          # %4 stop loss (daha sıkı risk kontrolü)
            'takeProfit': 20,       # %20 take profit (yüksek kar hedefi)
            'trailingStop': 2.5,    # %2.5 trailing stop (sıkı takip)

            # Pozisyon Yönetimi
            'basePosition': 25,     # %25 temel pozisyon (daha agresif)
            'maxPosition': 40,      # %40 maksimum pozisyon
            'minPosition': 15,      # %15 minimum pozisyon

            # Teknik İndikatörler - ÇOK DAHA ESNEK FİLTRELER
            'rsiLower': 25,         # RSI alt sınır (çok geniş)
            'rsiUpper': 95,         # RSI üst sınır (çok geniş)
            'emaSpread': 0.05,      # EMA spread (çok dar)
            'momentumScore': 2,     # Momentum skoru (çok düşük)

            # Fiyat Değişim Filtreleri - Çok esnek
            'priceChange3': 0.1,    # %0.1 (çok düşük)
            'priceChange10': 0.3,   # %0.3 (çok düşük)
            'volatilityMax': 10,    # %10 maksimum volatilite (yüksek)

            # Gelişmiş İndikatörler - Esnek
            'adxMinimum': 10,       # ADX minimum (çok düşük)
            'useVWAP': True,        # VWAP kullan
            'useIchimoku': False,   # Ichimoku kullanma (karmaşık)
            'useParabolicSAR': False, # SAR kullanma (gürültülü)

            # Diğer
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

    def _apply_convergence_algorithm(self, base_params, optimization_history, iteration):
        """
        Gradient Descent benzeri convergence algoritması
        En iyi sonuçlara doğru yakınsama sağlar
        """
        # En iyi 3 iterasyonu bul
        best_iterations = sorted(
            optimization_history,
            key=lambda x: x['stats'].get('averageProfit', -999),
            reverse=True
        )[:3]

        if not best_iterations:
            return base_params

        # En iyi parametrelerin ağırlıklı ortalamasını al
        best_params = best_iterations[0].get('applied_params', {})
        if not best_params:
            return base_params

        # Convergence faktörü (iterasyon arttıkça daha küçük değişiklikler)
        convergence_factor = max(0.1, 1.0 / (iteration * 0.3))

        # Parametreleri optimize et
        optimized_params = base_params.copy()

        for key, best_value in best_params.items():
            if key in optimized_params and isinstance(best_value, (int, float)):
                current_value = optimized_params[key]

                # Gradient yönünde hareket et
                direction = (best_value - current_value) * convergence_factor

                if isinstance(best_value, int):
                    optimized_params[key] = max(1, int(current_value + direction))
                else:
                    optimized_params[key] = max(0.001, current_value + direction)

        print(f"İterasyon {iteration}: Convergence algoritması uygulandı (faktör: {convergence_factor:.3f})")
        return optimized_params

    def _get_data_for_simulation(self, symbol, timeframe, start_date):
        """
        Simülasyon için veri al (önce yerel veri seti, sonra online)

        Args:
            symbol: Para birimi
            timeframe: Zaman dilimi
            start_date: Başlangıç tarihi

        Returns:
            DataFrame: OHLCV verileri
        """
        # Aktif veri setini kontrol et
        active_dataset = self._get_active_dataset()

        if active_dataset and symbol in active_dataset and timeframe in active_dataset[symbol]:
            # Yerel veri setinden al
            df = active_dataset[symbol][timeframe].copy()

            # Tarih filtreleme
            if start_date:
                try:
                    start_datetime = pd.to_datetime(start_date)
                    df = df[df.index >= start_datetime]
                except:
                    pass

            return df
        else:
            # Online'dan al
            return self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)

    def _get_active_dataset(self):
        """
        Aktif veri setini yükle

        Returns:
            dict: Veri seti veya None
        """
        try:
            datasets_dir = 'data/datasets'

            # Aktif veri setini bul
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    info_path = os.path.join(datasets_dir, filename)
                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    if info.get('is_active', False):
                        # Veri dosyasını yükle
                        dataset_name = filename[:-5]  # .json uzantısını kaldır
                        data_path = os.path.join(datasets_dir, f'{dataset_name}.pkl')

                        if os.path.exists(data_path):
                            with open(data_path, 'rb') as f:
                                return pickle.load(f)

            return None

        except Exception as e:
            print(f"Aktif veri seti yükleme hatası: {e}")
            return None

    def _detect_market_regime(self):
        """
        ML tabanlı piyasa rejimi tespiti
        """
        try:
            from app.models.ml_regime_detection import MLRegimeDetector
            ml_detector = MLRegimeDetector()

            # Basit rejim tespiti (gerçek implementasyon için veri gerekli)
            return {
                'regime': 'Sideways_Market',  # Varsayılan
                'confidence': 0.7
            }
        except Exception as e:
            print(f"ML Regime Detection hatası: {str(e)}")
            return {'regime': 'Unknown', 'confidence': 0.5}

    def _analyze_multi_timeframe(self):
        """
        Multi-timeframe analiz
        """
        try:
            from app.models.multi_timeframe import MultiTimeframeAnalyzer
            # Basit multi-timeframe analizi
            return {
                'signal': 1,  # Varsayılan pozitif sinyal
                'confidence': 80
            }
        except Exception as e:
            print(f"Multi-timeframe analiz hatası: {str(e)}")
            return {'signal': 0, 'confidence': 50}

    def _determine_strategy_type(self, current_stats, iteration, optimization_history):
        """
        Performansa göre strateji tipi belirleme
        """
        # İlk 3 iterasyon: Farklı stratejileri dene
        if iteration <= 3:
            strategy_types = ["ULTIMATE_HYBRID", "ML_ADAPTIVE", "MULTI_TIMEFRAME"]
            return strategy_types[(iteration - 1) % 3]

        # Sonraki iterasyonlar: Performansa göre seç
        if optimization_history:
            # En iyi performans gösteren strateji tipini bul
            best_profit = -999
            best_strategy = "ULTIMATE_HYBRID"

            for hist in optimization_history:
                profit = hist['stats'].get('averageProfit', -999)
                strategy_type = hist.get('strategy_type', 'UNKNOWN')
                if profit > best_profit:
                    best_profit = profit
                    best_strategy = strategy_type

            if best_profit > 0:
                return best_strategy

        # Varsayılan: Ultimate Hybrid
        return "ULTIMATE_HYBRID"

    def _get_ultimate_hybrid_parameters(self, regime_info, mtf_info, current_stats):
        """
        Ultimate Hybrid strateji parametreleri
        """
        base_params = {
            'stopLoss': 2,
            'takeProfit': 40,
            'trailingStop': 1,
            'basePosition': 50,
            'maxPosition': 80,
            'minPosition': 30,
            'rsiLower': 10,
            'rsiUpper': 99,
            'emaSpread': 0.001,
            'momentumScore': 0,
            'priceChange3': 0.01,
            'priceChange10': 0.01,
            'volatilityMax': 50,
            'adxMinimum': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

        # Rejim bazlı ayarlamalar
        regime = regime_info.get('regime', 'Unknown')

        if regime == "Bull_Market":
            base_params.update({
                'takeProfit': 50,
                'basePosition': 60,
                'stopLoss': 1.5
            })
        elif regime == "Bear_Market":
            base_params.update({
                'takeProfit': 25,
                'basePosition': 40,
                'stopLoss': 3,
                'enableShort': True
            })

        # Multi-timeframe bazlı ayarlamalar
        if mtf_info.get('confidence', 0) > 80:
            base_params['basePosition'] = min(90, base_params['basePosition'] + 20)
            base_params['takeProfit'] = base_params['takeProfit'] + 10

        return base_params

    def _get_ml_adaptive_parameters(self, regime_info, current_stats):
        """
        ML Adaptive strateji parametreleri
        """
        regime = regime_info.get('regime', 'Sideways_Market')
        confidence = regime_info.get('confidence', 0.5)

        if regime == "Sideways_Market":
            return {
                'stopLoss': 5,
                'takeProfit': 15,
                'basePosition': int(20 * confidence),
                'maxPosition': int(27 * confidence),
                'rsiLower': 25,
                'rsiUpper': 95,
                'momentumScore': 2,
                'commission': 0.01,
                'enableShort': False,
                'dynamicTrailing': True
            }
        else:
            return self._get_btc_optimized_base_parameters()

    def _get_multi_timeframe_parameters(self, mtf_info, current_stats):
        """
        Multi-timeframe strateji parametreleri
        """
        confidence = mtf_info.get('confidence', 50)

        return {
            'stopLoss': 3,
            'takeProfit': 25,
            'trailingStop': 2,
            'basePosition': min(50, int(30 * confidence / 100)),
            'maxPosition': min(70, int(50 * confidence / 100)),
            'rsiLower': 20,
            'rsiUpper': 95,
            'momentumScore': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

    def _apply_iterative_improvements(self, params, current_stats, iteration, failed_strategies):
        """
        İteratif iyileştirmeler uygula - GERÇEK DEĞİŞİKLİKLER
        """
        avg_profit = current_stats.get('avg_profit', 0)
        avg_trades = current_stats.get('avg_trades', 0)

        print(f"   📊 Mevcut durum: {avg_trades:.1f} işlem, {avg_profit:.2f}% kar")

        # SIFIR İŞLEM SORUNU - RADIKAL ÇÖZÜM
        if avg_trades == 0 or iteration > 5:
            import random
            random.seed(iteration * 42)  # Her iterasyonda farklı

            # Tamamen farklı parametreler üret
            params.update({
                'momentumScore': random.randint(0, 2),  # Çok düşük
                'adxMinimum': random.randint(1, 8),    # Çok düşük
                'volatilityMax': random.randint(20, 50), # Yüksek
                'rsiLower': random.randint(1, 20),     # Çok geniş
                'rsiUpper': random.randint(85, 99),    # Çok geniş
                'priceChange3': random.uniform(0.001, 0.1),  # Çok düşük
                'priceChange10': random.uniform(0.001, 0.2), # Çok düşük
                'emaSpread': random.uniform(0.001, 0.05),    # Çok düşük
                'stopLoss': random.uniform(0.5, 3),    # Değişken
                'takeProfit': random.uniform(5, 25),   # Değişken
                'basePosition': random.randint(20, 70), # Değişken
                'useVWAP': False,                      # Filtreleri kapat
                'useIchimoku': False,
                'useParabolicSAR': False,
                'williamsRThreshold': random.randint(-99, -70),
                'cciRange': random.randint(150, 400),
                'stochasticThreshold': f"{random.randint(5, 25)},{random.randint(75, 95)}"
            })
            print(f"   🚨 SIFIR İŞLEM - RADIKAL DEĞİŞİKLİK! (İterasyon {iteration})")
            print(f"   🎲 Yeni: Mom={params['momentumScore']}, ADX={params['adxMinimum']}, RSI={params['rsiLower']}-{params['rsiUpper']}")
            return params

        # Çok az işlem varsa filtreleri gevşet
        if avg_trades < 5:
            params.update({
                'momentumScore': max(0, params.get('momentumScore', 2) - 2),
                'adxMinimum': max(1, params.get('adxMinimum', 15) - 8),
                'rsiLower': max(1, params.get('rsiLower', 25) - 15),
                'rsiUpper': min(99, params.get('rsiUpper', 85) + 15),
                'priceChange3': max(0.001, params.get('priceChange3', 0.2) - 0.1),
                'priceChange10': max(0.001, params.get('priceChange10', 0.5) - 0.2),
                'useVWAP': False,
                'useIchimoku': False
            })
            print(f"   🔧 Az işlem ({avg_trades:.1f}) - Filtreler gevşetildi")

        # Zararlı işlemler varsa risk yönetimini sıkılaştır
        elif avg_profit < 0:
            params.update({
                'stopLoss': max(0.5, params.get('stopLoss', 3) - 1),
                'basePosition': max(10, params.get('basePosition', 25) - 10),
                'takeProfit': min(15, params.get('takeProfit', 20) - 3)
            })
            print(f"   🛡️ Zarar ({avg_profit:.2f}%) - Risk yönetimi sıkılaştırıldı")

        # Başarılı iterasyonlarda agresifleştir
        elif avg_profit > 2:
            params.update({
                'takeProfit': min(50, params.get('takeProfit', 20) + 10),
                'basePosition': min(80, params.get('basePosition', 25) + 15),
                'stopLoss': min(5, params.get('stopLoss', 2) + 1)
            })
            print(f"   🚀 Başarı ({avg_profit:.2f}%) - Strateji agresifleştirildi")

        return params

    def _analyze_performance_history(self, optimization_history):
        """
        Performans geçmişini analiz et
        """
        if not optimization_history:
            return {'trend': 'başlangıç', 'stability': 'unknown', 'best_iteration': 1}

        profits = [h.get('stats', {}).get('averageProfit', 0) for h in optimization_history]

        if len(profits) < 2:
            return {'trend': 'belirsiz', 'stability': 'unknown', 'best_iteration': 1}

        # Trend analizi
        recent_trend = 'sabit'
        if len(profits) >= 3:
            last_three = profits[-3:]
            if last_three[-1] > last_three[-2] > last_three[-3]:
                recent_trend = 'yükseliş'
            elif last_three[-1] < last_three[-2] < last_three[-3]:
                recent_trend = 'düşüş'

        # En iyi iterasyon
        best_profit = max(profits)
        best_iteration = profits.index(best_profit) + 1

        # Stabilite analizi
        profit_std = np.std(profits) if len(profits) > 1 else 0
        stability = 'kararlı' if profit_std < 2 else 'değişken'

        return {
            'trend': recent_trend,
            'stability': stability,
            'best_iteration': best_iteration,
            'best_profit': best_profit,
            'profit_std': profit_std,
            'total_iterations': len(profits)
        }

    def _generate_smart_cyclic_parameters(self, current_stats, improvement_analysis, iteration,
                                        failed_strategies, successful_strategies, performance_history, optimization_history):
        """
        Akıllı döngüsel parametre üretimi
        """
        print(f"🧠 Akıllı parametre üretimi başlıyor...")

        # Başlangıç parametreleri
        if iteration == 1:
            return self._get_first_iteration_parameters()

        # Başarılı stratejilerden öğren
        if successful_strategies:
            best_strategy = max(successful_strategies, key=lambda x: x.get('stats', {}).get('averageProfit', 0))
            base_params = best_strategy.get('applied_params', {}).copy()
            print(f"✅ En iyi strateji baz alınıyor (kar: {best_strategy.get('stats', {}).get('averageProfit', 0):.2f}%)")
        else:
            base_params = self._get_btc_optimized_base_parameters()
            print(f"⚠️ Başarılı strateji yok, varsayılan parametreler kullanılıyor")

        # Performans trendine göre ayarlama
        trend = performance_history.get('trend', 'sabit')

        if trend == 'düşüş':
            # Performans düşüyorsa radikal değişiklik
            base_params = self._apply_radical_changes(base_params, iteration)
            print(f"📉 Düşüş trendi - radikal değişiklikler uygulandı")
        elif trend == 'yükseliş':
            # Performans yükseliyorsa ince ayar
            base_params = self._apply_fine_tuning(base_params, iteration)
            print(f"📈 Yükseliş trendi - ince ayarlar uygulandı")
        else:
            # Sabit trend - deneysel değişiklikler
            base_params = self._apply_experimental_changes(base_params, iteration)
            print(f"📊 Sabit trend - deneysel değişiklikler uygulandı")

        # İşlem sayısı kontrolü
        avg_trades = current_stats.get('avg_trades', 0)
        if avg_trades < 3:
            base_params = self._boost_trade_frequency(base_params)
            print(f"🚀 Az işlem ({avg_trades:.1f}) - işlem sıklığı artırıldı")

        # Başarısız parametrelerden kaçın
        base_params = self._avoid_failed_parameters(base_params, failed_strategies)

        return base_params

    def _get_first_iteration_parameters(self):
        """İlk iterasyon için optimized parametreler"""
        return {
            'stopLoss': 2,
            'takeProfit': 40,
            'trailingStop': 1,
            'basePosition': 50,
            'maxPosition': 80,
            'minPosition': 30,
            'rsiLower': 10,
            'rsiUpper': 99,
            'emaSpread': 0.001,
            'momentumScore': 0,
            'priceChange3': 0.01,
            'priceChange10': 0.01,
            'volatilityMax': 50,
            'adxMinimum': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True,
            'useVWAP': False,
            'useIchimoku': False,
            'useParabolicSAR': False,
            'williamsRThreshold': -80,
            'cciRange': 200,
            'stochasticThreshold': "20,80"
        }

    def _apply_radical_changes(self, params, iteration):
        """Radikal değişiklikler uygula"""
        import random
        random.seed(iteration * 123)

        params.update({
            'momentumScore': random.randint(0, 3),
            'adxMinimum': random.randint(1, 10),
            'volatilityMax': random.randint(20, 50),
            'rsiLower': random.randint(5, 25),
            'rsiUpper': random.randint(85, 99),
            'stopLoss': random.uniform(1, 3),
            'takeProfit': random.uniform(20, 50),
            'basePosition': random.randint(30, 70),
            'useVWAP': False,
            'useIchimoku': False,
            'useParabolicSAR': False
        })
        return params

    def _apply_fine_tuning(self, params, iteration):
        """İnce ayarlar uygula"""
        import random
        random.seed(iteration * 456)

        # Mevcut değerleri %10-20 oranında değiştir
        for key in ['stopLoss', 'takeProfit', 'basePosition']:
            if key in params:
                current_val = params[key]
                variation = random.uniform(0.9, 1.1)
                params[key] = max(1, current_val * variation)

        return params

    def _apply_experimental_changes(self, params, iteration):
        """Deneysel değişiklikler uygula"""
        import random
        random.seed(iteration * 789)

        # Her iterasyonda farklı bir parametre grubunu değiştir
        change_group = iteration % 4

        if change_group == 0:
            params.update({
                'momentumScore': random.randint(1, 5),
                'adxMinimum': random.randint(5, 15)
            })
        elif change_group == 1:
            params.update({
                'rsiLower': random.randint(15, 35),
                'rsiUpper': random.randint(75, 95)
            })
        elif change_group == 2:
            params.update({
                'stopLoss': random.uniform(1.5, 4),
                'takeProfit': random.uniform(15, 35)
            })
        else:
            params.update({
                'basePosition': random.randint(20, 60),
                'volatilityMax': random.randint(10, 30)
            })

        return params

    def _boost_trade_frequency(self, params):
        """İşlem sıklığını artır"""
        params.update({
            'momentumScore': max(0, params.get('momentumScore', 5) - 2),
            'adxMinimum': max(1, params.get('adxMinimum', 15) - 5),
            'volatilityMax': min(50, params.get('volatilityMax', 10) + 10),
            'rsiLower': max(5, params.get('rsiLower', 30) - 10),
            'rsiUpper': min(99, params.get('rsiUpper', 80) + 10),
            'useVWAP': False,
            'useIchimoku': False,
            'useParabolicSAR': False
        })
        return params

    def _avoid_failed_parameters(self, params, failed_strategies):
        """Başarısız parametrelerden kaçın"""
        if not failed_strategies:
            return params

        # Başarısız stratejilerin ortak özelliklerini bul
        failed_momentum = [s.get('applied_params', {}).get('momentumScore', 5) for s in failed_strategies if isinstance(s, dict)]
        # failed_strategies kontrolü
        if not failed_strategies or not isinstance(failed_strategies, list):
            return params

        # Sadece dict olan stratejileri filtrele
        valid_failed_strategies = [s for s in failed_strategies if isinstance(s, dict)]

        if not valid_failed_strategies:
            return params

        failed_adx = [s.get('applied_params', {}).get('adxMinimum', 15) for s in valid_failed_strategies]

        if failed_momentum:
            avg_failed_momentum = sum(failed_momentum) / len(failed_momentum)
            # Başarısız momentum değerlerinden kaçın
            if abs(params.get('momentumScore', 5) - avg_failed_momentum) < 2:
                params['momentumScore'] = max(0, min(10, avg_failed_momentum + 3))

        if failed_adx:
            avg_failed_adx = sum(failed_adx) / len(failed_adx)
            # Başarısız ADX değerlerinden kaçın
            if abs(params.get('adxMinimum', 15) - avg_failed_adx) < 5:
                params['adxMinimum'] = max(1, min(25, avg_failed_adx + 5))

        return params

    def _validate_and_fix_parameters(self, params):
        """Parametreleri doğrula ve düzelt"""
        # Zorunlu parametreleri kontrol et ve ekle
        required_params = {
            'stopLoss': 2,
            'takeProfit': 20,
            'trailingStop': 1,
            'basePosition': 25,
            'maxPosition': 40,
            'minPosition': 10,
            'rsiLower': 25,
            'rsiUpper': 85,
            'emaSpread': 0.1,
            'momentumScore': 3,
            'priceChange3': 0.2,
            'priceChange10': 0.5,
            'volatilityMax': 10,
            'adxMinimum': 15,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True,
            'useVWAP': True,
            'useIchimoku': True,
            'useParabolicSAR': True,
            'williamsRThreshold': -80,
            'cciRange': 200,
            'stochasticThreshold': "30,70"
        }

        for key, default_value in required_params.items():
            if key not in params or params[key] is None:
                params[key] = default_value

        # Değer aralıklarını kontrol et
        params['stopLoss'] = max(0.5, min(10, params['stopLoss']))
        params['takeProfit'] = max(2, min(100, params['takeProfit']))
        params['basePosition'] = max(5, min(100, params['basePosition']))
        params['rsiLower'] = max(1, min(50, params['rsiLower']))
        params['rsiUpper'] = max(50, min(99, params['rsiUpper']))
        params['momentumScore'] = max(0, min(12, params['momentumScore']))
        params['adxMinimum'] = max(1, min(50, params['adxMinimum']))
        params['volatilityMax'] = max(1, min(100, params['volatilityMax']))

        # RSI aralığını kontrol et
        if params['rsiLower'] >= params['rsiUpper']:
            params['rsiLower'] = max(1, params['rsiUpper'] - 20)

        return params

    def _generate_smart_cyclic_analysis_text(self, current_stats, improvement_analysis, iteration, performance_history):
        """Akıllı döngüsel analiz metni"""
        text = f"🧠 Akıllı AI Analizi - İterasyon {iteration}\n\n"

        # Performans durumu
        avg_profit = current_stats.get('avg_profit', 0)
        if avg_profit > 5:
            text += "🎉 Mükemmel performans! "
        elif avg_profit > 2:
            text += "✅ İyi performans! "
        elif avg_profit > 0:
            text += "📈 Pozitif performans. "
        else:
            text += "⚠️ Negatif performans. "

        # Trend analizi
        trend = performance_history.get('trend', 'belirsiz')
        if trend == 'yükseliş':
            text += "Performans trendi yükselişte, ince ayarlar uygulandı. "
        elif trend == 'düşüş':
            text += "Performans trendi düşüşte, radikal değişiklikler uygulandı. "
        else:
            text += "Performans trendi sabit, deneysel değişiklikler uygulandı. "

        # İşlem analizi
        avg_trades = current_stats.get('avg_trades', 0)
        if avg_trades < 3:
            text += f"Az işlem ({avg_trades:.1f}) tespit edildi, işlem sıklığı artırıldı. "
        elif avg_trades > 50:
            text += f"Çok fazla işlem ({avg_trades:.1f}), filtreler sıkılaştırıldı. "

        # AI öğrenme durumu
        total_iterations = performance_history.get('total_iterations', 1)
        if total_iterations > 5:
            text += f"AI {total_iterations} iterasyondan öğrenerek parametreleri optimize etti. "

        text += "Sistem her iterasyonda daha akıllı hale geliyor! 🚀"

        return text

    def _predict_win_rate_cyclic(self, params, current_stats):
        """Kazanma oranı tahmini"""
        base_rate = 50

        # Momentum score etkisi
        momentum_effect = (10 - params.get('momentumScore', 5)) * 2

        # RSI aralığı etkisi
        rsi_range = params.get('rsiUpper', 80) - params.get('rsiLower', 30)
        rsi_effect = min(20, rsi_range / 3)

        predicted_rate = base_rate + momentum_effect + rsi_effect
        return max(30, min(90, predicted_rate))

    def _predict_profit_cyclic(self, params, current_stats):
        """Kar tahmini"""
        base_profit = 2

        # Take profit etkisi
        tp_effect = params.get('takeProfit', 20) / 10

        # Position size etkisi
        pos_effect = params.get('basePosition', 25) / 25

        predicted_profit = base_profit + tp_effect + pos_effect
        return max(0.5, min(15, predicted_profit))

    def _determine_risk_level_cyclic(self, params, current_stats):
        """Risk seviyesi belirleme"""
        risk_score = 0

        # Stop loss etkisi
        if params.get('stopLoss', 3) < 2:
            risk_score += 2

        # Position size etkisi
        if params.get('basePosition', 25) > 50:
            risk_score += 2

        # Take profit etkisi
        if params.get('takeProfit', 20) > 40:
            risk_score += 1

        if risk_score >= 4:
            return "Yüksek"
        elif risk_score >= 2:
            return "Orta"
        else:
            return "Düşük"

    def _calculate_current_stats(self, optimization_history):
        """
        Mevcut optimizasyon istatistiklerini hesapla
        """
        if not optimization_history:
            return {
                'avg_profit': 0,
                'avg_trades': 0,
                'avg_win_rate': 0,
                'best_profit': 0,
                'worst_profit': 0
            }

        profits = [h['stats'].get('averageProfit', 0) for h in optimization_history]
        trades = [h['stats'].get('averageTrades', 0) for h in optimization_history]
        win_rates = [h['stats'].get('winRate', 0) for h in optimization_history]

        return {
            'avg_profit': sum(profits) / len(profits) if profits else 0,
            'avg_trades': sum(trades) / len(trades) if trades else 0,
            'avg_win_rate': sum(win_rates) / len(win_rates) if win_rates else 0,
            'best_profit': max(profits) if profits else 0,
            'worst_profit': min(profits) if profits else 0
        }

    def _calculate_average_stats(self, simulation_results):
        """
        Simülasyon sonuçlarının ortalamasını hesapla
        """
        if not simulation_results:
            return {}

        total_profit = sum(r.get('totalProfit', 0) for r in simulation_results)
        total_trades = sum(r.get('totalTrades', 0) for r in simulation_results)
        total_wins = sum(r.get('winningTrades', 0) for r in simulation_results)
        max_drawdowns = [r.get('maxDrawdown', 0) for r in simulation_results]

        return {
            'averageProfit': total_profit / len(simulation_results),
            'averageTrades': total_trades / len(simulation_results),
            'winRate': (total_wins / total_trades * 100) if total_trades > 0 else 0,
            'maxDrawdown': max(max_drawdowns) if max_drawdowns else 0,
            'totalResults': len(simulation_results)
        }

    def run_ai_optimization(self):
        """
        Temiz AI tabanlı strateji optimizasyonu
        """
        try:
            # Parametreleri al
            data = request.get_json()
            symbols = data.get('symbols', ['BTC/USDT'])
            timeframe = data.get('timeframe', '1d')
            start_date = data.get('start_date', '2024-01-01')
            iterations = data.get('iterations', 15)

            print(f"\n🤖 TEMİZ AI OPTİMİZASYON BAŞLADI")
            print(f"📊 Semboller: {symbols}")
            print(f"⏰ Zaman dilimi: {timeframe}")
            print(f"📅 Başlangıç: {start_date}")
            print(f"🔄 İterasyon: {iterations}")

            # Optimizasyon geçmişi
            optimization_history = []

            for iteration in range(1, iterations + 1):
                print(f"\n{'='*60}")
                print(f"🔄 İTERASYON {iteration}/{iterations}")
                print(f"{'='*60}")

                # Mevcut istatistikleri hesapla
                current_stats = self._calculate_current_stats(optimization_history)

                # Strateji tipini belirle
                strategy_type = self._determine_strategy_type(current_stats, iteration, optimization_history)
                print(f"🎯 Seçilen strateji: {strategy_type}")

                # Piyasa rejimi tespiti
                regime_info = self._detect_market_regime()
                print(f"📈 Piyasa rejimi: {regime_info['regime']} (güven: {regime_info['confidence']:.1%})")

                # Multi-timeframe analiz
                mtf_info = self._analyze_multi_timeframe()
                print(f"⏱️ Multi-timeframe sinyal: {mtf_info['signal']} (güven: {mtf_info['confidence']}%)")

                # Strateji parametrelerini oluştur
                if strategy_type == "ULTIMATE_HYBRID":
                    params = self._get_ultimate_hybrid_parameters(regime_info, mtf_info, current_stats)
                elif strategy_type == "ML_ADAPTIVE":
                    params = self._get_ml_adaptive_parameters(regime_info, current_stats)
                elif strategy_type == "MULTI_TIMEFRAME":
                    params = self._get_multi_timeframe_parameters(mtf_info, current_stats)
                elif strategy_type == "AGGRESSIVE_SCALP":
                    params = self._get_aggressive_scalp_parameters(iteration)
                elif strategy_type == "CONSERVATIVE_SWING":
                    params = self._get_conservative_swing_parameters(iteration)
                else:
                    params = self._get_btc_optimized_base_parameters()

                # İteratif iyileştirmeler uygula
                failed_strategies = [h for h in optimization_history if h['stats'].get('averageProfit', 0) <= 0]
                params = self._apply_iterative_improvements(params, current_stats, iteration, failed_strategies)

                print(f"⚙️ Parametreler: Stop={params.get('stopLoss', 0):.1f}%, Take={params.get('takeProfit', 0):.1f}%, Pos={params.get('basePosition', 0)}%")

                # Simülasyonu çalıştır
                simulation_results = []

                for symbol in symbols:
                    print(f"\n📊 {symbol} simülasyonu...")

                    # Veri al
                    df = self._get_data_for_simulation(symbol, timeframe, start_date)

                    if df is None or df.empty:
                        print(f"❌ {symbol} için veri alınamadı")
                        continue

                    # Parametrelerle yeni simulation engine oluştur
                    from app.models.simulation import SimulationEngine
                    simulation_engine = SimulationEngine(self.config)

                    # Sadece strateji parametrelerini filtrele
                    strategy_params = {k: v for k, v in params.items() if k not in ['symbol', 'timeframe', 'start_date']}
                    print(f"🔧 Filtrelenmiş parametreler: {len(strategy_params)} adet")
                    for key, value in strategy_params.items():
                        print(f"   {key}: {value}")

                    # Simülasyon çalıştır - FİLTRELENMİŞ PARAMETRELERİ GEÇİR!
                    result = simulation_engine.run_simulation(df, 10000, 100, 0.1, **strategy_params)

                    # Sonuçlara ek bilgiler ekle
                    if result:
                        result['symbol'] = symbol
                        result['timeframe'] = timeframe

                    if result:
                        simulation_results.append(result)
                        print(f"✅ {symbol}: {result.get('totalTrades', 0)} işlem, {result.get('totalProfit', 0):.2f}% kar")
                    else:
                        print(f"❌ {symbol} simülasyon hatası")

                # Sonuçları analiz et
                if simulation_results:
                    # Ortalama istatistikleri hesapla
                    avg_stats = self._calculate_average_stats(simulation_results)

                    # Geçmişe ekle
                    optimization_history.append({
                        'iteration': iteration,
                        'strategy_type': strategy_type,
                        'applied_params': params.copy(),
                        'regime_info': regime_info,
                        'mtf_info': mtf_info,
                        'stats': avg_stats,
                        'results': simulation_results
                    })

                    print(f"\n📈 İterasyon {iteration} Sonuçları:")
                    print(f"   💰 Ortalama kar: {avg_stats.get('averageProfit', 0):.2f}%")
                    print(f"   📊 Ortalama işlem: {avg_stats.get('averageTrades', 0):.1f}")
                    print(f"   🎯 Kazanma oranı: {avg_stats.get('winRate', 0):.1f}%")
                    print(f"   📉 Maksimum düşüş: {avg_stats.get('maxDrawdown', 0):.2f}%")
                else:
                    print(f"❌ İterasyon {iteration}: Hiç sonuç alınamadı")

            # En iyi sonuçları bul
            if optimization_history:
                best_iteration = max(optimization_history, key=lambda x: x['stats'].get('averageProfit', -999))

                print(f"\n🏆 EN İYİ SONUÇ:")
                print(f"   🔄 İterasyon: {best_iteration['iteration']}")
                print(f"   🎯 Strateji: {best_iteration['strategy_type']}")
                print(f"   💰 Kar: {best_iteration['stats'].get('averageProfit', 0):.2f}%")
                print(f"   📊 İşlem: {best_iteration['stats'].get('averageTrades', 0):.1f}")
                print(f"   🎯 Kazanma: {best_iteration['stats'].get('winRate', 0):.1f}%")

                return jsonify({
                    'success': True,
                    'message': 'Temiz AI optimizasyon tamamlandı',
                    'best_result': best_iteration,
                    'optimization_history': optimization_history,
                    'total_iterations': len(optimization_history)
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Hiç başarılı iterasyon bulunamadı'
                })

        except Exception as e:
            print(f"❌ AI optimizasyon hatası: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({
                'success': False,
                'message': f'AI optimizasyon hatası: {str(e)}'
            })

    def _get_btc_optimized_base_parameters(self):
        """
        BTC/USDT için optimize edilmiş temel parametreler
        2025 piyasa koşullarına göre ayarlanmış
        """
        return {
            # Risk Yönetimi - BTC volatilitesine uygun (Agresif Kar Hedefi)
            'stopLoss': 4,          # %4 stop loss (daha sıkı risk kontrolü)
            'takeProfit': 20,       # %20 take profit (yüksek kar hedefi)
            'trailingStop': 2.5,    # %2.5 trailing stop (sıkı takip)

            # Pozisyon Yönetimi
            'basePosition': 25,     # %25 temel pozisyon (daha agresif)
            'maxPosition': 40,      # %40 maksimum pozisyon
            'minPosition': 15,      # %15 minimum pozisyon

            # Teknik İndikatörler - ÇOK DAHA ESNEK FİLTRELER
            'rsiLower': 25,         # RSI alt sınır (çok geniş)
            'rsiUpper': 95,         # RSI üst sınır (çok geniş)
            'emaSpread': 0.05,      # EMA spread (çok dar)
            'momentumScore': 2,     # Momentum skoru (çok düşük)

            # Fiyat Değişim Filtreleri - Çok esnek
            'priceChange3': 0.1,    # %0.1 (çok düşük)
            'priceChange10': 0.3,   # %0.3 (çok düşük)
            'volatilityMax': 10,    # %10 maksimum volatilite (yüksek)

            # Gelişmiş İndikatörler - Esnek
            'adxMinimum': 10,       # ADX minimum (çok düşük)
            'useVWAP': True,        # VWAP kullan
            'useIchimoku': False,   # Ichimoku kullanma (karmaşık)
            'useParabolicSAR': False, # SAR kullanma (gürültülü)

            # Diğer
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

    def _apply_convergence_algorithm(self, base_params, optimization_history, iteration):
        """
        Gradient Descent benzeri convergence algoritması
        En iyi sonuçlara doğru yakınsama sağlar
        """
        # En iyi 3 iterasyonu bul
        best_iterations = sorted(
            optimization_history,
            key=lambda x: x['stats'].get('averageProfit', -999),
            reverse=True
        )[:3]

        if not best_iterations:
            return base_params

        # En iyi parametrelerin ağırlıklı ortalamasını al
        best_params = best_iterations[0].get('applied_params', {})
        if not best_params:
            return base_params

        # Convergence faktörü (iterasyon arttıkça daha küçük değişiklikler)
        convergence_factor = max(0.1, 1.0 / (iteration * 0.3))

        # Parametreleri optimize et
        optimized_params = base_params.copy()

        for key, best_value in best_params.items():
            if key in optimized_params and isinstance(best_value, (int, float)):
                current_value = optimized_params[key]

                # Gradient yönünde hareket et
                direction = (best_value - current_value) * convergence_factor

                if isinstance(best_value, int):
                    optimized_params[key] = max(1, int(current_value + direction))
                else:
                    optimized_params[key] = max(0.001, current_value + direction)

        print(f"İterasyon {iteration}: Convergence algoritması uygulandı (faktör: {convergence_factor:.3f})")
        return optimized_params

    def _get_data_for_simulation(self, symbol, timeframe, start_date):
        """
        Simülasyon için veri al (önce yerel veri seti, sonra online)

        Args:
            symbol: Para birimi
            timeframe: Zaman dilimi
            start_date: Başlangıç tarihi

        Returns:
            DataFrame: OHLCV verileri
        """
        # Aktif veri setini kontrol et
        active_dataset = self._get_active_dataset()

        if active_dataset and symbol in active_dataset and timeframe in active_dataset[symbol]:
            # Yerel veri setinden al
            df = active_dataset[symbol][timeframe].copy()

            # Tarih filtreleme
            if start_date:
                try:
                    start_datetime = pd.to_datetime(start_date)
                    df = df[df.index >= start_datetime]
                except:
                    pass

            return df
        else:
            # Online'dan al
            return self.data_fetcher.fetch_ohlcv(symbol, timeframe, start_date)

    def _get_active_dataset(self):
        """
        Aktif veri setini yükle

        Returns:
            dict: Veri seti veya None
        """
        try:
            datasets_dir = 'data/datasets'

            # Aktif veri setini bul
            for filename in os.listdir(datasets_dir):
                if filename.endswith('.json'):
                    info_path = os.path.join(datasets_dir, filename)
                    with open(info_path, 'r', encoding='utf-8') as f:
                        info = json.load(f)

                    if info.get('is_active', False):
                        # Veri dosyasını yükle
                        dataset_name = filename[:-5]  # .json uzantısını kaldır
                        data_path = os.path.join(datasets_dir, f'{dataset_name}.pkl')

                        if os.path.exists(data_path):
                            with open(data_path, 'rb') as f:
                                return pickle.load(f)

            return None

        except Exception as e:
            print(f"Aktif veri seti yükleme hatası: {e}")
            return None

    def _detect_market_regime(self):
        """
        ML tabanlı piyasa rejimi tespiti
        """
        try:
            from app.models.ml_regime_detection import MLRegimeDetector
            ml_detector = MLRegimeDetector()

            # Basit rejim tespiti (gerçek implementasyon için veri gerekli)
            return {
                'regime': 'Sideways_Market',  # Varsayılan
                'confidence': 0.7
            }
        except Exception as e:
            print(f"ML Regime Detection hatası: {str(e)}")
            return {'regime': 'Unknown', 'confidence': 0.5}

    def _analyze_multi_timeframe(self):
        """
        Multi-timeframe analiz
        """
        try:
            from app.models.multi_timeframe import MultiTimeframeAnalyzer
            # Basit multi-timeframe analizi
            return {
                'signal': 1,  # Varsayılan pozitif sinyal
                'confidence': 80
            }
        except Exception as e:
            print(f"Multi-timeframe analiz hatası: {str(e)}")
            return {'signal': 0, 'confidence': 50}

    def _determine_strategy_type(self, current_stats, iteration, optimization_history):
        """
        Performansa göre strateji tipi belirleme - GERÇEK FARKLILIK
        """
        import random
        random.seed(iteration * 123)  # Her iterasyonda farklı

        avg_profit = current_stats.get('avg_profit', 0)
        avg_trades = current_stats.get('avg_trades', 0)

        # İlk 5 iterasyon: Sırayla farklı stratejileri dene
        if iteration <= 5:
            strategies = ["ULTIMATE_HYBRID", "ML_ADAPTIVE", "MULTI_TIMEFRAME", "AGGRESSIVE_SCALP", "CONSERVATIVE_SWING"]
            selected = strategies[(iteration - 1) % len(strategies)]
            print(f"   🎯 İlk 5 iterasyon - Sıralı strateji: {selected}")
            return selected

        # 6-10 iterasyon: Rastgele strateji seçimi
        if iteration <= 10:
            strategies = ["ULTIMATE_HYBRID", "ML_ADAPTIVE", "MULTI_TIMEFRAME"]
            selected = random.choice(strategies)
            print(f"   🎲 6-10 iterasyon - Rastgele strateji: {selected}")
            return selected

        # 11-15 iterasyon: Performansa göre ağırlıklı seçim
        if avg_trades == 0:
            # Hiç işlem yoksa en agresif strateji
            selected = "AGGRESSIVE_SCALP"
            print(f"   🚨 Hiç işlem yok - Agresif strateji: {selected}")
        elif avg_profit < 0:
            # Zarar varsa konservatif strateji
            selected = "CONSERVATIVE_SWING"
            print(f"   🛡️ Zarar var - Konservatif strateji: {selected}")
        elif avg_profit > 2:
            # Kar varsa hibrit strateji
            selected = "ULTIMATE_HYBRID"
            print(f"   🎯 Kar var - Hibrit strateji: {selected}")
        else:
            # Orta performans - ML adaptif
            selected = "ML_ADAPTIVE"
            print(f"   🧠 Orta performans - ML strateji: {selected}")

        return selected

    def _get_ultimate_hybrid_parameters(self, regime_info, mtf_info, current_stats):
        """
        Ultimate Hybrid strateji parametreleri
        """
        base_params = {
            'stopLoss': 2,
            'takeProfit': 40,
            'trailingStop': 1,
            'basePosition': 50,
            'maxPosition': 80,
            'minPosition': 30,
            'rsiLower': 10,
            'rsiUpper': 99,
            'emaSpread': 0.001,
            'momentumScore': 0,
            'priceChange3': 0.01,
            'priceChange10': 0.01,
            'volatilityMax': 50,
            'adxMinimum': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

        # Rejim bazlı ayarlamalar
        regime = regime_info.get('regime', 'Unknown')
        confidence = regime_info.get('confidence', 0.5)

        if regime == "Bull_Market":
            base_params.update({
                'takeProfit': 50,
                'basePosition': 60,
                'stopLoss': 1.5
            })
        elif regime == "Bear_Market":
            base_params.update({
                'takeProfit': 25,
                'basePosition': 40,
                'stopLoss': 3,
                'enableShort': True
            })

        # Multi-timeframe bazlı ayarlamalar
        if mtf_info.get('confidence', 0) > 80:
            base_params['basePosition'] = min(90, base_params['basePosition'] + 20)
            base_params['takeProfit'] = base_params['takeProfit'] + 10

        return base_params

    def _get_ml_adaptive_parameters(self, regime_info, current_stats):
        """
        ML Adaptive strateji parametreleri
        """
        regime = regime_info.get('regime', 'Sideways_Market')
        confidence = regime_info.get('confidence', 0.5)

        if regime == "Sideways_Market":
            return {
                'stopLoss': 5,
                'takeProfit': 15,
                'basePosition': int(20 * confidence),
                'maxPosition': int(27 * confidence),
                'rsiLower': 25,
                'rsiUpper': 95,
                'momentumScore': 2,
                'commission': 0.01,
                'enableShort': False,
                'dynamicTrailing': True
            }
        else:
            return self._get_btc_optimized_base_parameters()

    def _get_multi_timeframe_parameters(self, mtf_info, current_stats):
        """
        Multi-timeframe strateji parametreleri
        """
        confidence = mtf_info.get('confidence', 50)

        return {
            'stopLoss': 3,
            'takeProfit': 25,
            'trailingStop': 2,
            'basePosition': min(50, int(30 * confidence / 100)),
            'maxPosition': min(70, int(50 * confidence / 100)),
            'rsiLower': 20,
            'rsiUpper': 95,
            'momentumScore': 1,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }

    def _get_aggressive_scalp_parameters(self, iteration):
        """
        Agresif scalping strateji parametreleri - ÇOK FAZLA İŞLEM
        """
        import random
        random.seed(iteration * 456)

        return {
            'stopLoss': random.uniform(0.3, 1.0),      # Çok düşük stop
            'takeProfit': random.uniform(3, 8),        # Düşük take profit
            'trailingStop': random.uniform(0.2, 0.5),  # Çok düşük trailing
            'basePosition': random.randint(60, 90),    # Yüksek pozisyon
            'maxPosition': 95,
            'minPosition': 50,
            'rsiLower': random.randint(1, 15),         # Çok geniş RSI
            'rsiUpper': random.randint(85, 99),
            'emaSpread': random.uniform(0.001, 0.02),  # Çok düşük spread
            'momentumScore': 0,                        # Momentum yok
            'priceChange3': random.uniform(0.001, 0.05),  # Çok düşük değişim
            'priceChange10': random.uniform(0.001, 0.1),
            'volatilityMax': random.randint(30, 80),   # Yüksek volatilite
            'adxMinimum': random.randint(1, 5),        # Çok düşük ADX
            'commission': 0.01,
            'enableShort': True,                       # Short pozisyon aktif
            'dynamicTrailing': True,
            'useVWAP': False,                          # Tüm filtreler kapalı
            'useIchimoku': False,
            'useParabolicSAR': False,
            'williamsRThreshold': random.randint(-99, -50),
            'cciRange': random.randint(100, 300),
            'stochasticThreshold': f"{random.randint(1, 20)},{random.randint(80, 99)}"
        }

    def _get_conservative_swing_parameters(self, iteration):
        """
        Konservatif swing trading parametreleri - AZ AMA KALİTELİ İŞLEM
        """
        import random
        random.seed(iteration * 789)

        return {
            'stopLoss': random.uniform(3, 8),          # Yüksek stop
            'takeProfit': random.uniform(15, 40),      # Yüksek take profit
            'trailingStop': random.uniform(2, 5),      # Yüksek trailing
            'basePosition': random.randint(15, 35),    # Düşük pozisyon
            'maxPosition': 50,
            'minPosition': 10,
            'rsiLower': random.randint(25, 35),        # Dar RSI aralığı
            'rsiUpper': random.randint(65, 75),
            'emaSpread': random.uniform(0.1, 0.5),     # Yüksek spread
            'momentumScore': random.randint(3, 8),     # Yüksek momentum
            'priceChange3': random.uniform(0.3, 1.0),  # Yüksek değişim
            'priceChange10': random.uniform(0.5, 2.0),
            'volatilityMax': random.randint(5, 20),    # Düşük volatilite
            'adxMinimum': random.randint(15, 30),      # Yüksek ADX
            'commission': 0.01,
            'enableShort': False,                      # Sadece long
            'dynamicTrailing': True,
            'useVWAP': True,                           # Tüm filtreler aktif
            'useIchimoku': True,
            'useParabolicSAR': True,
            'williamsRThreshold': random.randint(-30, -20),
            'cciRange': random.randint(200, 500),
            'stochasticThreshold': f"{random.randint(20, 30)},{random.randint(70, 80)}"
        }

    def _apply_iterative_improvements(self, params, current_stats, iteration, failed_strategies):
        """
        İteratif iyileştirmeler uygula
        """
        avg_profit = current_stats.get('avg_profit', 0)
        avg_trades = current_stats.get('avg_trades', 0)

        # Çok az işlem varsa filtreleri gevşet
        if avg_trades < 3:
            params.update({
                'momentumScore': max(0, params.get('momentumScore', 2) - 1),
                'adxMinimum': max(5, params.get('adxMinimum', 15) - 5),
                'rsiLower': max(5, params.get('rsiLower', 25) - 10),
                'rsiUpper': min(99, params.get('rsiUpper', 85) + 10)
            })
            print(f"   🔧 Az işlem nedeniyle filtreler gevşetildi")

        # Zararlı işlemler varsa risk yönetimini sıkılaştır
        if avg_profit < 0:
            params.update({
                'stopLoss': max(1, params.get('stopLoss', 3) - 0.5),
                'basePosition': max(10, params.get('basePosition', 25) - 5)
            })
            print(f"   🛡️ Zarar nedeniyle risk yönetimi sıkılaştırıldı")

        # Başarılı iterasyonlarda agresifleştir
        if avg_profit > 2:
            params.update({
                'takeProfit': min(50, params.get('takeProfit', 20) + 5),
                'basePosition': min(80, params.get('basePosition', 25) + 10)
            })
            print(f"   🚀 Başarı nedeniyle strateji agresifleştirildi")

        return params

    def ai_optimization(self):
        """
        Ana sayfa için AI optimizasyon
        """
        try:
            data = request.get_json()

            # Form verilerini al
            symbol = data.get('symbol', 'EUR/USD')
            timeframe = data.get('timeframe', '1d')
            start_date = data.get('start_date', '2025-01-01')
            initial_balance = float(data.get('initial_balance', 10000))
            leverage = int(data.get('leverage', 100))
            lot_size = float(data.get('lot_size', 0.1))

            print(f"🤖 Ana sayfa AI optimizasyon başlatılıyor...")
            print(f"   Sembol: {symbol}")
            print(f"   Zaman dilimi: {timeframe}")
            print(f"   Başlangıç tarihi: {start_date}")
            print(f"   Kaldıraç: {leverage}")
            print(f"   Lot büyüklüğü: {lot_size}")

            # Veri al
            df = self._get_data_for_simulation(symbol, timeframe, start_date)

            if df.empty:
                return jsonify({
                    'success': False,
                    'error': f'Veri seti bulunamadı: {symbol} - {timeframe}'
                }), 400

            # Hızlı AI optimizasyon (5 iterasyon)
            best_result = None
            best_profit = -float('inf')
            best_params = None

            # Basit parametreler test et
            test_params = [
                {'stopLoss': 2, 'takeProfit': 10, 'basePosition': 25, 'rsiLower': 20, 'rsiUpper': 80},
                {'stopLoss': 3, 'takeProfit': 15, 'basePosition': 30, 'rsiLower': 25, 'rsiUpper': 75},
                {'stopLoss': 4, 'takeProfit': 20, 'basePosition': 35, 'rsiLower': 30, 'rsiUpper': 70},
                {'stopLoss': 5, 'takeProfit': 25, 'basePosition': 40, 'rsiLower': 35, 'rsiUpper': 65},
                {'stopLoss': 3, 'takeProfit': 12, 'basePosition': 20, 'rsiLower': 15, 'rsiUpper': 85}
            ]

            for i, params in enumerate(test_params, 1):
                print(f"🔄 AI Optimizasyon İterasyon {i}/5")

                # Varsayılan parametrelerle birleştir
                full_params = {
                    'trailingStop': 3,
                    'maxPosition': params['basePosition'] + 10,
                    'minPosition': max(5, params['basePosition'] - 10),
                    'emaSpread': 0.2,
                    'momentumScore': 5,
                    'priceChange3': 0.5,
                    'priceChange10': 1.0,
                    'volatilityMax': 4,
                    'commission': 0.01,
                    'enableShort': False,
                    'dynamicTrailing': True,
                    **params
                }

                # Simülasyon çalıştır
                simulation_engine = SimulationEngine(self.config, full_params)
                result = simulation_engine.run_simulation(df, initial_balance, leverage, lot_size)

                profit_pct = result.get('profit_percentage', 0)

                print(f"   Parametreler: SL={params['stopLoss']}%, TP={params['takeProfit']}%, Pos={params['basePosition']}%")
                print(f"   Kar: {profit_pct:.2f}%")
                print(f"   İşlem: {result.get('total_trades', 0)}")

                # En iyi sonucu kaydet
                if profit_pct > best_profit:
                    best_profit = profit_pct
                    best_result = result
                    best_params = full_params

                    print(f"   ✅ Yeni en iyi sonuç!")

            # En iyi parametreleri kaydet
            if best_params:
                session['strategy_settings'] = best_params
                print(f"🎯 En iyi parametreler kaydedildi")
                print(f"   En iyi kar: {best_profit:.2f}%")
                print(f"   En iyi kazanma oranı: {best_result.get('win_rate', 0):.2f}%")

            return jsonify({
                'success': True,
                'best_profit': best_profit,
                'best_win_rate': best_result.get('win_rate', 0) if best_result else 0,
                'total_iterations': 5,
                'message': f'AI optimizasyon tamamlandı! En iyi kar: {best_profit:.2f}%'
            })

        except Exception as e:
            print(f"❌ AI optimizasyon hatası: {str(e)}")
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500

    def get_live_signal(self):
        """
        Canlı trading sinyali hesaplar

        Returns:
            JSON: Sinyal bilgileri
        """
        try:
            data = request.get_json()
            symbol = data.get('symbol', 'EUR/USD')
            timeframe = data.get('timeframe', '1h')

            print(f"🎯 Canlı sinyal hesaplanıyor: {symbol} ({timeframe})")

            # Veri al (offline veri kullan)
            df = self.data_manager.get_forex_data(symbol, timeframe, limit=100)

            if df is None or df.empty:
                return jsonify({
                    'signal': 'HOLD',
                    'strength': 50,
                    'reason': 'Veri alınamadı',
                    'target': 0,
                    'stopLoss': 0
                }), 200

            # Strateji ile sinyal hesapla
            strategy = self.simulation_engine.strategy
            signals = strategy.generate_signals(df)

            # Son sinyal
            last_signal = signals['signal'].iloc[-1] if not signals.empty else 0
            current_price = df['close'].iloc[-1]

            # Sinyal yorumla
            if last_signal == 1:
                signal_type = 'BUY'
                strength = 75
                reason = 'Teknik analiz pozitif - Alış sinyali'
                target = current_price * 1.01  # %1 hedef
                stop_loss = current_price * 0.995  # %0.5 stop
            elif last_signal == -1:
                signal_type = 'SELL'
                strength = 75
                reason = 'Teknik analiz negatif - Satış sinyali'
                target = current_price * 0.99  # %1 hedef
                stop_loss = current_price * 1.005  # %0.5 stop
            else:
                signal_type = 'HOLD'
                strength = 50
                reason = 'Belirsiz trend - Bekleme önerisi'
                target = current_price
                stop_loss = current_price

            return jsonify({
                'signal': signal_type,
                'strength': strength,
                'reason': reason,
                'target': target,
                'stopLoss': stop_loss,
                'currentPrice': current_price,
                'timestamp': df.index[-1].isoformat() if not df.empty else None
            })

        except Exception as e:
            print(f"Canlı sinyal hatası: {str(e)}")
            return jsonify({
                'signal': 'HOLD',
                'strength': 50,
                'reason': f'Hata: {str(e)}',
                'target': 0,
                'stopLoss': 0
            }), 500