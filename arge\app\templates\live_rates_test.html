<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live-Rates.com Test - Forex Trading Bot</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-dark.min.css" rel="stylesheet">
    <style>
        .test-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .status-error {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
        }
        .status-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }
        .json-viewer {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .proxy-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .proxy-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }
        .proxy-success {
            border-left: 5px solid #28a745;
        }
        .proxy-error {
            border-left: 5px solid #dc3545;
        }
        .proxy-testing {
            border-left: 5px solid #ffc107;
        }
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .metric-box {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chart-line"></i> Forex Trading Bot
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Ana Sayfa</a>
                <a class="nav-link" href="/live-trading">Canlı Trading</a>
                <a class="nav-link active" href="/live-rates-test">Live Rates Test</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Başlık -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="mb-3">
                    <i class="fas fa-flask text-primary"></i>
                    Live-Rates.com Test Sayfası
                    <small class="text-muted">Proxy sistemi ve ham veri testi</small>
                </h1>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Test Amacı:</strong> live-rates.com API'sinden proxy sistemi ile ham veri çekme testi
                </div>
            </div>
        </div>

        <!-- Test Kontrolleri -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="test-card p-4">
                    <h4><i class="fas fa-play-circle text-success"></i> Test Kontrolleri</h4>
                    <div class="mb-3">
                        <button class="btn btn-primary me-2" onclick="testDirectConnection()">
                            <i class="fas fa-globe"></i> Direkt Bağlantı Test
                        </button>
                        <button class="btn btn-success me-2" onclick="testWithProxy()">
                            <i class="fas fa-shield-alt"></i> Proxy ile Test
                        </button>
                        <button class="btn btn-warning me-2" onclick="testAllProxies()">
                            <i class="fas fa-list"></i> Tüm Proxy'leri Test
                        </button>
                        <button class="btn btn-info" onclick="clearResults()">
                            <i class="fas fa-trash"></i> Temizle
                        </button>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Test Modu:</label>
                        <select class="form-select" id="testMode">
                            <option value="single">Tek İstek</option>
                            <option value="multiple">Çoklu İstek (5x)</option>
                            <option value="stress">Stress Test (10x)</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="showRawData" checked>
                        <label class="form-check-label" for="showRawData">
                            Ham veriyi göster
                        </label>
                    </div>
                </div>
            </div>

            <!-- Test İstatistikleri -->
            <div class="col-md-6">
                <div class="test-card p-4">
                    <h4><i class="fas fa-chart-bar text-info"></i> Test İstatistikleri</h4>
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value text-success" id="successCount">0</div>
                                <div class="metric-label">Başarılı</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value text-danger" id="errorCount">0</div>
                                <div class="metric-label">Hatalı</div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value text-primary" id="totalRequests">0</div>
                                <div class="metric-label">Toplam İstek</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value text-warning" id="avgResponseTime">0ms</div>
                                <div class="metric-label">Ort. Süre</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Proxy Durumu -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="test-card p-4">
                    <h4><i class="fas fa-server text-warning"></i> Proxy Durumu</h4>
                    <div id="proxyStatus" class="row">
                        <!-- Proxy kartları buraya dinamik olarak eklenecek -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Sonuçları -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="test-card p-4">
                    <h4><i class="fas fa-clipboard-list text-primary"></i> Test Sonuçları</h4>
                    <div id="testResults">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-play-circle fa-3x mb-3"></i>
                            <p>Test başlatmak için yukarıdaki butonları kullanın</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ham Veri Görüntüleyici -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="test-card p-4">
                    <h4><i class="fas fa-code text-success"></i> Ham Veri Görüntüleyici</h4>
                    <div class="mb-3">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="formatJson()">
                            <i class="fas fa-magic"></i> JSON Formatla
                        </button>
                        <button class="btn btn-sm btn-outline-secondary me-2" onclick="copyToClipboard()">
                            <i class="fas fa-copy"></i> Kopyala
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="clearRawData()">
                            <i class="fas fa-eraser"></i> Temizle
                        </button>
                    </div>
                    <div id="rawDataViewer" class="json-viewer">
                        Ham veri burada görüntülenecek...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="/static/js/live_rates_test.js"></script>
</body>
</html>
