#!/usr/bin/env python3
"""
Multi-Timeframe Analiz <PERSON>ü
BTC/USDT için 1d trend + 4h entry kombinasyonu
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple, Optional
from .data_fetcher import DataFetcher
from .indicators import TechnicalIndicators

class MultiTimeframeAnalyzer:
    """
    Multi-timeframe analiz sınıfı
    Uzun vadeli trend (1d) + Kısa vadeli entry (4h) kombinasyonu
    """
    
    def __init__(self, config):
        self.config = config
        self.data_fetcher = DataFetcher(config)
        self.indicators = TechnicalIndicators()
        
    def analyze_trend_timeframe(self, symbol: str, start_date: str) -> Dict:
        """
        Trend timeframe (1d) analizi
        Genel trend yönünü belirler
        """
        try:
            # 1d verilerini çek
            df_1d = self.data_fetcher.fetch_ohlcv(symbol, "1d", start_date)
            if df_1d is None or len(df_1d) < 50:
                return {"trend": "neutral", "strength": 0}
            
            # Teknik indikatörleri ekle
            df_1d = self.indicators.add_all_indicators(df_1d)
            
            # Son veriyi al
            last_row = df_1d.iloc[-1]
            
            # Trend analizi
            trend_score = 0
            
            # EMA trend (50 puan)
            if last_row['ema_20'] > last_row['ema_50']:
                trend_score += 25
            if last_row['ema_50'] > last_row['ema_200']:
                trend_score += 25
                
            # MACD trend (30 puan)
            if last_row['macd'] > last_row['macd_signal']:
                trend_score += 15
            if last_row['macd_hist'] > 0:
                trend_score += 15
                
            # ADX trend gücü (20 puan)
            if last_row['adx'] > 25:
                trend_score += 20
            elif last_row['adx'] > 20:
                trend_score += 10
                
            # Trend yönü belirleme
            if trend_score >= 70:
                trend = "strong_bullish"
            elif trend_score >= 50:
                trend = "bullish"
            elif trend_score >= 30:
                trend = "weak_bullish"
            elif trend_score >= 20:
                trend = "neutral"
            else:
                trend = "bearish"
                
            return {
                "trend": trend,
                "strength": trend_score,
                "ema_20": last_row['ema_20'],
                "ema_50": last_row['ema_50'],
                "ema_200": last_row['ema_200'],
                "macd": last_row['macd'],
                "adx": last_row['adx'],
                "price": last_row['close']
            }
            
        except Exception as e:
            print(f"Trend analizi hatası: {str(e)}")
            return {"trend": "neutral", "strength": 0}
    
    def analyze_entry_timeframe(self, symbol: str, start_date: str, trend_info: Dict) -> Dict:
        """
        Entry timeframe (4h) analizi
        Giriş sinyallerini belirler
        """
        try:
            # 4h verilerini çek
            df_4h = self.data_fetcher.fetch_ohlcv(symbol, "4h", start_date)
            if df_4h is None or len(df_4h) < 50:
                return {"signal": 0, "strength": 0}
            
            # Teknik indikatörleri ekle
            df_4h = self.indicators.add_all_indicators(df_4h)
            
            # Son veriyi al
            last_row = df_4h.iloc[-1]
            
            # Entry sinyali analizi
            entry_score = 0
            
            # Trend konfirmasyonu (sadece bullish trendde long)
            if trend_info["trend"] in ["strong_bullish", "bullish", "weak_bullish"]:
                
                # RSI momentum (25 puan)
                if 30 < last_row['rsi'] < 70:
                    entry_score += 15
                if 40 < last_row['rsi'] < 60:
                    entry_score += 10
                    
                # MACD momentum (25 puan)
                if last_row['macd'] > last_row['macd_signal']:
                    entry_score += 15
                if last_row['macd_hist'] > last_row.get('macd_hist_prev', 0):
                    entry_score += 10
                    
                # EMA pozisyon (25 puan)
                if last_row['close'] > last_row['ema_20']:
                    entry_score += 15
                if last_row['ema_20'] > last_row['ema_50']:
                    entry_score += 10
                    
                # Volume konfirmasyonu (25 puan)
                if 'volume' in last_row and last_row['volume'] > df_4h['volume'].rolling(20).mean().iloc[-1]:
                    entry_score += 25
                    
            # Sinyal gücü belirleme
            if entry_score >= 75:
                signal = 1  # Güçlü alış
            elif entry_score >= 50:
                signal = 1  # Orta alış
            elif entry_score >= 25:
                signal = 0  # Zayıf sinyal
            else:
                signal = 0  # Sinyal yok
                
            return {
                "signal": signal,
                "strength": entry_score,
                "rsi": last_row['rsi'],
                "macd": last_row['macd'],
                "price": last_row['close'],
                "volume": last_row.get('volume', 0)
            }
            
        except Exception as e:
            print(f"Entry analizi hatası: {str(e)}")
            return {"signal": 0, "strength": 0}
    
    def get_multi_timeframe_signal(self, symbol: str, start_date: str) -> Dict:
        """
        Multi-timeframe sinyal kombinasyonu
        """
        # Trend analizi (1d)
        trend_info = self.analyze_trend_timeframe(symbol, start_date)
        
        # Entry analizi (4h)
        entry_info = self.analyze_entry_timeframe(symbol, start_date, trend_info)
        
        # Kombinasyon sinyali
        final_signal = 0
        confidence = 0
        
        if trend_info["trend"] in ["strong_bullish", "bullish"]:
            if entry_info["signal"] == 1:
                final_signal = 1
                confidence = min(100, trend_info["strength"] + entry_info["strength"])
        
        return {
            "signal": final_signal,
            "confidence": confidence,
            "trend_info": trend_info,
            "entry_info": entry_info,
            "recommendation": self._get_recommendation(final_signal, confidence)
        }
    
    def _get_recommendation(self, signal: int, confidence: int) -> str:
        """Sinyal önerisi"""
        if signal == 1:
            if confidence >= 120:
                return "STRONG_BUY"
            elif confidence >= 80:
                return "BUY"
            else:
                return "WEAK_BUY"
        else:
            return "HOLD"
    
    def get_support_resistance_levels(self, symbol: str, start_date: str) -> Dict:
        """
        Destek ve direnç seviyelerini hesapla
        """
        try:
            # 1d verilerini çek
            df_1d = self.data_fetcher.fetch_ohlcv(symbol, "1d", start_date)
            if df_1d is None or len(df_1d) < 50:
                return {}
            
            # Son 50 günün high/low değerleri
            recent_data = df_1d.tail(50)
            
            # Pivot noktaları bul
            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # Resistance seviyeleri (son 50 günün en yüksek değerleri)
            resistance_levels = []
            for i in range(2, len(highs)-2):
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    resistance_levels.append(highs[i])
            
            # Support seviyeleri (son 50 günün en düşük değerleri)
            support_levels = []
            for i in range(2, len(lows)-2):
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    support_levels.append(lows[i])
            
            # En yakın seviyeleri al
            current_price = df_1d['close'].iloc[-1]
            
            nearest_resistance = None
            nearest_support = None
            
            if resistance_levels:
                resistance_above = [r for r in resistance_levels if r > current_price]
                if resistance_above:
                    nearest_resistance = min(resistance_above)
            
            if support_levels:
                support_below = [s for s in support_levels if s < current_price]
                if support_below:
                    nearest_support = max(support_below)
            
            return {
                "current_price": current_price,
                "nearest_resistance": nearest_resistance,
                "nearest_support": nearest_support,
                "all_resistance": sorted(resistance_levels, reverse=True)[:5],
                "all_support": sorted(support_levels, reverse=True)[:5]
            }
            
        except Exception as e:
            print(f"Destek/direnç analizi hatası: {str(e)}")
            return {}
