body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    color: #333;
}

.navbar {
    margin-bottom: 20px;
}

.card {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #e9ecef;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: bold;
}

.form-label {
    font-weight: bold;
}

.form-select, .form-control {
    border-radius: 0.25rem;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
}

.table th, .table td {
    vertical-align: middle;
}

.text-success {
    color: #28a745 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-3, .col-md-9 {
        margin-bottom: 20px;
    }
} 