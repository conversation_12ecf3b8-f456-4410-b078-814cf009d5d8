"""
Flask uygulaması başlatıcı
"""
from flask import Flask
from app.controllers.main_controller import MainController
from app.models.config import Config

def create_app():
    """
    Flask uygulamasını oluşturur
    
    Returns:
        Flask: Flask uygulaması
    """
    app = Flask(__name__,
                template_folder='templates',
                static_folder='static')
    app.config.from_object(Config)
    
    controller = MainController()
    
    # Route'ları tanımla
    app.add_url_rule('/', 'index', controller.index)
    app.add_url_rule('/strategy', 'strategy', controller.strategy_page)
    app.add_url_rule('/bulk-analysis', 'bulk_analysis', controller.bulk_analysis_page)
    app.add_url_rule('/data-manager', 'data_manager', controller.data_manager_page)
    app.add_url_rule('/api/market-data', 'get_market_data', controller.get_market_data)
    app.add_url_rule('/api/exchange-info', 'get_exchange_info', controller.get_exchange_info)
    app.add_url_rule('/api/run-simulation', 'run_simulation', controller.run_simulation, methods=['POST'])
    app.add_url_rule('/api/get-performance', 'get_performance', controller.get_performance)
    app.add_url_rule('/api/get-trade-history', 'get_trade_history', controller.get_trade_history)
    app.add_url_rule('/api/save-strategy', 'save_strategy', controller.save_strategy, methods=['POST'])
    app.add_url_rule('/api/get-strategy', 'get_strategy', controller.get_strategy)
    app.add_url_rule('/api/generate-ai-strategy', 'generate_ai_strategy', controller.generate_ai_strategy, methods=['POST'])
    app.add_url_rule('/api/download-dataset', 'download_dataset', controller.download_dataset, methods=['POST'])
    app.add_url_rule('/api/list-datasets', 'list_datasets', controller.list_datasets)
    app.add_url_rule('/api/dataset-details/<dataset_name>', 'dataset_details', controller.dataset_details)
    app.add_url_rule('/api/set-active-dataset', 'set_active_dataset', controller.set_active_dataset, methods=['POST'])
    app.add_url_rule('/api/delete-dataset/<dataset_name>', 'delete_dataset', controller.delete_dataset, methods=['DELETE'])
    app.add_url_rule('/api/run-ai-optimization', 'run_ai_optimization', controller.run_ai_optimization, methods=['POST'])
    app.add_url_rule('/api/ai-optimization', 'ai_optimization', controller.ai_optimization, methods=['POST'])

    return app