// Grafik nesnesi
let priceChart = null;

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tooltip'lerini başlat
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Borsa bilgilerini yükle
    loadExchangeInfo();

    // Form submit olayını dinle
    document.getElementById('simulationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        runSimulation();
    });

    // AI Optimizasyon butonunu dinle
    document.getElementById('aiOptimizationBtn').addEventListener('click', function(e) {
        e.preventDefault();
        runAIOptimization();
    });
});

// Borsa bilgilerini yükle
async function loadExchangeInfo() {
    try {
        const response = await fetch('/api/exchange-info');
        const data = await response.json();
        
        // Sembolleri doldur
        const symbolSelect = document.getElementById('symbol');
        data.symbols.forEach(symbol => {
            const option = document.createElement('option');
            option.value = symbol;
            option.textContent = symbol;
            symbolSelect.appendChild(option);
        });
        
        // Zaman dilimlerini doldur
        const timeframeSelect = document.getElementById('timeframe');
        data.timeframes.forEach(timeframe => {
            const option = document.createElement('option');
            option.value = timeframe;
            option.textContent = timeframe;
            timeframeSelect.appendChild(option);
        });
        
        // Varsayılan değerleri ayarla
        symbolSelect.value = 'EUR/USD';
        timeframeSelect.value = '1d';  // 1d önerilen zaman aralığı (forex için)
        
        // Başlangıç tarihini ayarla
        const startDate = document.getElementById('startDate');
        const today = new Date();
        const oneYearAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate()-7);
        startDate.value = oneYearAgo.toISOString().split('T')[0];
        
    } catch (error) {
        console.error('Borsa bilgileri yüklenirken hata oluştu:', error);
    }
}

// Simülasyonu çalıştır
async function runSimulation() {
    try {
        console.log('Simülasyon başlatılıyor...');
        // Form verilerini al
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;
        const startDate = document.getElementById('startDate').value;
        const initialBalance = parseFloat(document.getElementById('initialBalance').value);
        const leverage = parseInt(document.getElementById('leverage').value);
        const lotSize = parseFloat(document.getElementById('lotSize').value);

        // Form verilerini doğrula
        if (!symbol || !timeframe || !startDate || isNaN(initialBalance) || isNaN(leverage) || isNaN(lotSize)) {
            alert('Lütfen tüm alanları doldurun!');
            return;
        }

        const formData = {
            symbol: symbol,
            timeframe: timeframe,
            start_date: startDate,
            initial_balance: initialBalance,
            leverage: leverage,
            lot_size: lotSize
        };
        
        // Simülasyonu başlat
        const response = await fetch('/api/run-simulation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            // Hata detaylarını al
            const errorData = await response.json();
            const errorMessage = errorData.error || 'Bilinmeyen bir hata oluştu.';
            
            if (response.status === 404) {
                alert(`Hata: Seçilen parametreler için veri bulunamadı. Lütfen tarih aralığını veya sembolü değiştirin. (Detay: ${errorMessage})`);
            } else {
                throw new Error(`HTTP error! status: ${response.status}. Detay: ${errorMessage}`);
            }
        }
        
        console.log('Yanıt başarıyla alındı, JSON çözümleniyor...');
        const results = await response.json();
        console.log('JSON başarıyla çözümlendi. Sonuçlar:', results);
        
        // Grafikleri güncelle
        updatePriceChart(results);
        updatePerformanceChart(results);
        updateTradeHistory(results);
        updatePerformanceMetrics(results);
        updateSimulationLog(results);
        
    } catch (error) {
        console.error('Simülasyon çalıştırılırken hata oluştu:', error);
        alert('Simülasyon çalıştırılırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
}

// Fiyat grafiğini güncelle
function updatePriceChart(results) {
    const dates = results.dates;
    const prices = results.prices || results.data?.map(d => d.close) || [];
    const trades = results.trades || [];

    // Ana fiyat çizgisi
    const priceTrace = {
        x: dates,
        y: prices,
        type: 'scatter',
        mode: 'lines',
        name: 'Fiyat',
        line: {
            color: '#17a2b8',
            width: 2
        },
        hovertemplate: '<b>%{x}</b><br>Fiyat: %{y:.5f}<extra></extra>'
    };

    const traces = [priceTrace];

    // İşlem noktalarını ekle
    if (trades && trades.length > 0) {
        // LONG açılış işlemleri (yeşil üçgen yukarı)
        const longOpenTrades = trades.filter(t => t.type === 'open_long');
        if (longOpenTrades.length > 0) {
            const longOpenTrace = {
                x: longOpenTrades.map(t => t.timestamp),
                y: longOpenTrades.map(t => t.price),
                mode: 'markers',
                name: 'LONG Açılış',
                marker: {
                    symbol: 'triangle-up',
                    size: 14,
                    color: '#28a745',
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                hovertemplate: longOpenTrades.map(t =>
                    '<b>🟢 LONG AÇILIŞ</b><br>' +
                    'Tarih: %{x}<br>' +
                    'Fiyat: %{y:.5f}<br>' +
                    (t.lot_size ? 'Lot: ' + t.lot_size + '<br>' : '') +
                    (t.leverage ? 'Kaldıraç: 1:' + t.leverage + '<br>' : '') +
                    (t.margin ? 'Margin: ' + t.margin.toFixed(2) + ' USD' : '') +
                    '<extra></extra>'
                )
            };
            traces.push(longOpenTrace);
        }

        // SHORT açılış işlemleri (kırmızı üçgen aşağı)
        const shortOpenTrades = trades.filter(t => t.type === 'open_short');
        if (shortOpenTrades.length > 0) {
            const shortOpenTrace = {
                x: shortOpenTrades.map(t => t.timestamp),
                y: shortOpenTrades.map(t => t.price),
                mode: 'markers',
                name: 'SHORT Açılış',
                marker: {
                    symbol: 'triangle-down',
                    size: 14,
                    color: '#dc3545',
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                hovertemplate: shortOpenTrades.map(t =>
                    '<b>🔴 SHORT AÇILIŞ</b><br>' +
                    'Tarih: %{x}<br>' +
                    'Fiyat: %{y:.5f}<br>' +
                    (t.lot_size ? 'Lot: ' + t.lot_size + '<br>' : '') +
                    (t.leverage ? 'Kaldıraç: 1:' + t.leverage + '<br>' : '') +
                    (t.margin ? 'Margin: ' + t.margin.toFixed(2) + ' USD' : '') +
                    '<extra></extra>'
                )
            };
            traces.push(shortOpenTrace);
        }

        // Kapanış işlemleri (daire - renk kar/zarar durumuna göre)
        const closeTrades = trades.filter(t =>
            t.type.includes('close_') || t.type === 'close_position' || t.type === 'margin_call'
        );
        if (closeTrades.length > 0) {
            const closeTrace = {
                x: closeTrades.map(t => t.timestamp),
                y: closeTrades.map(t => t.price),
                mode: 'markers',
                name: 'Pozisyon Kapanış',
                marker: {
                    symbol: 'circle',
                    size: 12,
                    color: closeTrades.map(t => {
                        if (t.type === 'margin_call') return '#ff6b35'; // Turuncu - margin call
                        if (t.profit && t.profit > 0) return '#28a745'; // Yeşil - kar
                        return '#dc3545'; // Kırmızı - zarar
                    }),
                    line: {
                        color: '#ffffff',
                        width: 2
                    }
                },
                hovertemplate: closeTrades.map(t => {
                    let typeText = '';
                    let emoji = '';
                    if (t.type === 'margin_call') {
                        typeText = 'MARGIN CALL';
                        emoji = '⚠️';
                    } else if (t.type.includes('stop')) {
                        typeText = 'STOP LOSS';
                        emoji = '🛑';
                    } else if (t.type.includes('profit')) {
                        typeText = 'TAKE PROFIT';
                        emoji = '🎯';
                    } else if (t.type.includes('trailing')) {
                        typeText = 'TRAILING STOP';
                        emoji = '📈';
                    } else {
                        typeText = 'KAPANIŞ';
                        emoji = '🔄';
                    }

                    return '<b>' + emoji + ' ' + typeText + '</b><br>' +
                           'Tarih: %{x}<br>' +
                           'Fiyat: %{y:.5f}<br>' +
                           'Kar/Zarar: ' + (t.profit ? t.profit.toFixed(2) : '0.00') + ' USD<br>' +
                           (t.pips ? 'Pip: ' + t.pips.toFixed(1) + '<br>' : '') +
                           (t.balance ? 'Yeni Bakiye: ' + t.balance.toFixed(2) + ' USD' : '') +
                           '<extra></extra>';
                }).join('')
            };
            traces.push(closeTrace);
        }
    }

    const layout = {
        title: 'Forex Fiyat Grafiği ve İşlemler',
        xaxis: {
            title: 'Tarih',
            type: 'date'
        },
        yaxis: {
            title: 'Fiyat (USD)'
        },
        hovermode: 'closest',
        showlegend: true,
        legend: {
            x: 0,
            y: 1,
            bgcolor: 'rgba(255,255,255,0.9)',
            bordercolor: 'rgba(0,0,0,0.1)',
            borderwidth: 1
        },
        plot_bgcolor: 'rgba(0,0,0,0)',
        paper_bgcolor: 'rgba(0,0,0,0)'
    };

    Plotly.newPlot('priceChart', traces, layout);
}

// Performans grafiğini güncelle
function updatePerformanceChart(results) {
    const dates = results.dates;
    const equity = results.equity_curve;
    const drawdown = results.drawdown_curve;
    
    const equityTrace = {
        x: dates,
        y: equity,
        type: 'scatter',
        mode: 'lines',
        name: 'Equity',
        line: {
            color: '#28a745'
        }
    };
    
    const drawdownTrace = {
        x: dates,
        y: drawdown,
        type: 'scatter',
        mode: 'lines',
        name: 'Drawdown',
        line: {
            color: '#dc3545'
        }
    };
    
    const layout = {
        title: 'Performans Grafiği',
        xaxis: {
            title: 'Tarih'
        },
        yaxis: {
            title: 'Değer'
        }
    };
    
    Plotly.newPlot('performanceChart', [equityTrace, drawdownTrace], layout);
}

// İşlem geçmişini güncelle
function updateTradeHistory(results) {
    const tbody = document.querySelector('#tradeHistory tbody');
    tbody.innerHTML = '';

    if (!results.trades || results.trades.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="7" class="text-center text-muted">Henüz işlem yapılmadı</td>';
        tbody.appendChild(row);
        return;
    }

    results.trades.forEach(trade => {
        const row = document.createElement('tr');

        // İşlem tipine göre renk ve metin belirle
        let typeClass = '';
        let typeText = '';
        let emoji = '';

        switch (trade.type) {
            case 'open_long':
                typeClass = 'text-success fw-bold';
                typeText = 'LONG AÇ';
                emoji = '🟢';
                break;
            case 'open_short':
                typeClass = 'text-danger fw-bold';
                typeText = 'SHORT AÇ';
                emoji = '🔴';
                break;
            case 'close_long_profit':
                typeClass = 'text-success';
                typeText = 'LONG KAPAT (TP)';
                emoji = '🎯';
                break;
            case 'close_long_stop':
                typeClass = 'text-danger';
                typeText = 'LONG KAPAT (SL)';
                emoji = '🛑';
                break;
            case 'close_long_trailing':
                typeClass = 'text-warning';
                typeText = 'LONG KAPAT (TS)';
                emoji = '📈';
                break;
            case 'close_short_profit':
                typeClass = 'text-success';
                typeText = 'SHORT KAPAT (TP)';
                emoji = '🎯';
                break;
            case 'close_short_stop':
                typeClass = 'text-danger';
                typeText = 'SHORT KAPAT (SL)';
                emoji = '🛑';
                break;
            case 'close_short_trailing':
                typeClass = 'text-warning';
                typeText = 'SHORT KAPAT (TS)';
                emoji = '📈';
                break;
            case 'margin_call':
                typeClass = 'text-warning fw-bold';
                typeText = 'MARGIN CALL';
                emoji = '⚠️';
                break;
            case 'close_position':
                typeClass = 'text-info';
                typeText = 'KAPANIŞ';
                emoji = '🔄';
                break;
            default:
                typeClass = 'text-muted';
                typeText = trade.type.toUpperCase();
                emoji = '📊';
        }

        // Kar/zarar rengi
        const profitClass = trade.profit > 0 ? 'text-success fw-bold' :
                           trade.profit < 0 ? 'text-danger fw-bold' : 'text-muted';

        // Pip hesaplama (varsa)
        const pips = trade.pips ? `${trade.pips > 0 ? '+' : ''}${trade.pips.toFixed(1)} pip` : '-';

        row.innerHTML = `
            <td>${new Date(trade.timestamp).toLocaleString('tr-TR')}</td>
            <td class="${typeClass}">${emoji} ${typeText}</td>
            <td>${trade.price.toFixed(5)}</td>
            <td>${trade.lot_size || '-'}</td>
            <td>${pips}</td>
            <td class="${profitClass}">
                ${trade.profit ? (trade.profit > 0 ? '+' : '') + trade.profit.toFixed(2) + ' USD' : '-'}
            </td>
            <td>${trade.balance ? trade.balance.toFixed(2) + ' USD' : '-'}</td>
        `;

        tbody.appendChild(row);
    });
}

// Performans metriklerini güncelle
function updatePerformanceMetrics(results) {
    const metrics = document.getElementById('performanceMetrics');
    
    metrics.innerHTML = `
        <div class="row">
            <div class="col-6">
                <p><strong>Sembol:</strong> ${results.symbol}</p>
                <p><strong>Zaman Aralığı:</strong> ${results.timeframe}</p>
                <p><strong>Başlangıç Tarihi:</strong> ${results.start_date}</p>
                <p><strong>Başlangıç Bakiyesi:</strong> ${results.initial_balance.toFixed(2)} USD</p>
                <p><strong>Son Bakiye:</strong> ${results.final_balance.toFixed(2)} USD</p>
                <p><strong>Kaldıraç:</strong> 1:${results.leverage || 100}</p>
                <p><strong>Lot Büyüklüğü:</strong> ${results.lot_size || 0.1}</p>
                <p><strong>Toplam Kar/Zarar:</strong>
                    <span class="${results.total_profit >= 0 ? 'text-success' : 'text-danger'}">
                        ${results.total_profit.toFixed(2)} USD (${results.profit_percentage.toFixed(2)}%)
                    </span>
                </p>
            </div>
            <div class="col-6">
                <p><strong>Toplam İşlem:</strong> ${results.total_trades}</p>
                <p><strong>Kazanan İşlem:</strong> ${results.winning_trades}</p>
                <p><strong>Kaybeden İşlem:</strong> ${results.losing_trades}</p>
                <p><strong>Kazanma Oranı:</strong> ${results.win_rate.toFixed(2)}%</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-6">
                <p><strong>Yıllık Getiri:</strong> ${(results.annual_return * 100).toFixed(2)}%</p>
                <p><strong>Volatilite:</strong> ${(results.volatility * 100).toFixed(2)}%</p>
            </div>
            <div class="col-6">
                <p><strong>Sharpe Oranı:</strong> ${results.sharpe_ratio.toFixed(2)}</p>
                <p><strong>Maximum Drawdown:</strong> ${(results.max_drawdown * 100).toFixed(2)}%</p>
            </div>
        </div>

        <!-- Strateji İyileştirme Önerileri -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb"></i> Strateji İyileştirme Önerileri:</h6>
                    ${generateStrategyRecommendations(results)}
                </div>
            </div>
        </div>
    `;
}

// Strateji önerileri üret
function generateStrategyRecommendations(results) {
    let recommendations = [];

    // İşlem sayısı analizi
    if (results.total_trades < 10) {
        recommendations.push("🔄 <strong>Daha fazla işlem fırsatı:</strong> RSI aralığını genişletin (20-80) veya take profit'i düşürün (%10-15)");
    }

    // Kazanma oranı analizi
    if (results.win_rate < 60) {
        recommendations.push("📈 <strong>Kazanma oranını artırın:</strong> Stop loss'u sıkılaştırın (%2-3) veya sinyal filtrelerini güçlendirin");
    }

    // Kar analizi
    if (results.profit_percentage > 50) {
        recommendations.push("💰 <strong>Mükemmel performans!</strong> Lot büyüklüğünü artırarak karı maksimize edebilirsiniz");
    } else if (results.profit_percentage < 10) {
        recommendations.push("⚠️ <strong>Düşük karlılık:</strong> Strateji parametrelerini gözden geçirin veya AI optimizasyon kullanın");
    }

    // Risk analizi
    if (Math.abs(results.max_drawdown) > 0.1) { // %10'dan fazla düşüş
        recommendations.push("🛡️ <strong>Risk yönetimi:</strong> Stop loss'u sıkılaştırın veya pozisyon büyüklüğünü azaltın");
    }

    // Sharpe oranı analizi
    if (results.sharpe_ratio < 0.5) {
        recommendations.push("📊 <strong>Risk/getiri oranı:</strong> Volatiliteyi azaltmak için daha uzun zaman dilimi deneyin");
    }

    // Genel öneriler
    recommendations.push("🤖 <strong>AI Optimizasyon:</strong> Otomatik parametre optimizasyonu için 'Temiz AI Optimizasyon' butonunu kullanın");
    recommendations.push("📋 <strong>Toplu analiz:</strong> Farklı forex çiftlerini karşılaştırmak için 'Toplu Analiz' sayfasını ziyaret edin");

    return recommendations.slice(0, 4).map(rec => `<small>• ${rec}</small>`).join('<br>');
}

// Simülasyon loglarını güncelle
function updateSimulationLog(results) {
    const logElement = document.getElementById('simulationLog');
    logElement.innerHTML = ''; // Mevcut logları temizle

    if (results.log && Array.isArray(results.log)) {
        results.log.forEach(logMessage => {
            const p = document.createElement('p');
            p.textContent = logMessage;
            logElement.appendChild(p);
        });
    } else {
        logElement.innerHTML = '<p>Log bulunamadı veya formatı hatalı.</p>';
    }
}

// AI Optimizasyonu çalıştır
async function runAIOptimization() {
    try {
        console.log('AI Optimizasyonu başlatılıyor...');

        // Form verilerini al
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;
        const startDate = document.getElementById('startDate').value;
        const initialBalance = parseFloat(document.getElementById('initialBalance').value);
        const leverage = parseInt(document.getElementById('leverage').value);
        const lotSize = parseFloat(document.getElementById('lotSize').value);

        // Form verilerini doğrula
        if (!symbol || !timeframe || !startDate || isNaN(initialBalance) || isNaN(leverage) || isNaN(lotSize)) {
            alert('Lütfen tüm alanları doldurun!');
            return;
        }

        // Butonu devre dışı bırak
        const btn = document.getElementById('aiOptimizationBtn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> AI Optimizasyonu Çalışıyor...';

        const formData = {
            symbol: symbol,
            timeframe: timeframe,
            start_date: startDate,
            initial_balance: initialBalance,
            leverage: leverage,
            lot_size: lotSize
        };

        // AI Optimizasyonu başlat
        const response = await fetch('/api/ai-optimization', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const errorData = await response.json();
            const errorMessage = errorData.error || 'Bilinmeyen bir hata oluştu.';
            throw new Error(`HTTP error! status: ${response.status}. Detay: ${errorMessage}`);
        }

        console.log('AI Optimizasyonu tamamlandı, sonuçlar alınıyor...');
        const results = await response.json();
        console.log('AI Optimizasyon sonuçları:', results);

        // Sonuçları göster
        alert(`AI Optimizasyonu tamamlandı!\n\nEn iyi sonuç:\n- Kar: ${results.best_profit?.toFixed(2) || 'N/A'}%\n- Kazanma Oranı: ${results.best_win_rate?.toFixed(2) || 'N/A'}%\n\nParametreler otomatik olarak güncellendi.`);

        // En iyi sonuçla simülasyon çalıştır
        if (results.success) {
            await runSimulation();
        }

    } catch (error) {
        console.error('AI Optimizasyonu çalıştırılırken hata oluştu:', error);
        alert('AI Optimizasyonu çalıştırılırken bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
        // Butonu tekrar aktif et
        const btn = document.getElementById('aiOptimizationBtn');
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-robot"></i> Temiz AI Optimizasyon';
    }
}