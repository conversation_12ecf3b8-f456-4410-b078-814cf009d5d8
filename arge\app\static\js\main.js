// Grafik nesnesi
let priceChart = null;

// Sayfa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    // Bootstrap tooltip'lerini başlat
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Borsa bilgilerini yükle
    loadExchangeInfo();

    // Form submit olayını dinle
    document.getElementById('simulationForm').addEventListener('submit', function(e) {
        e.preventDefault();
        runSimulation();
    });
});

// Borsa bilgilerini yükle
async function loadExchangeInfo() {
    try {
        const response = await fetch('/api/exchange-info');
        const data = await response.json();
        
        // Sembolleri doldur
        const symbolSelect = document.getElementById('symbol');
        data.symbols.forEach(symbol => {
            const option = document.createElement('option');
            option.value = symbol;
            option.textContent = symbol;
            symbolSelect.appendChild(option);
        });
        
        // Zaman dilimlerini doldur
        const timeframeSelect = document.getElementById('timeframe');
        data.timeframes.forEach(timeframe => {
            const option = document.createElement('option');
            option.value = timeframe;
            option.textContent = timeframe;
            timeframeSelect.appendChild(option);
        });
        
        // Varsayılan değerleri ayarla
        symbolSelect.value = 'BTC/USDT';
        timeframeSelect.value = '15m';  // 15m önerilen zaman aralığı
        
        // Başlangıç tarihini ayarla
        const startDate = document.getElementById('startDate');
        const today = new Date();
        const oneYearAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate()-7);
        startDate.value = oneYearAgo.toISOString().split('T')[0];
        
    } catch (error) {
        console.error('Borsa bilgileri yüklenirken hata oluştu:', error);
    }
}

// Simülasyonu çalıştır
async function runSimulation() {
    try {
        console.log('Simülasyon başlatılıyor...');
        // Form verilerini al
        const symbol = document.getElementById('symbol').value;
        const timeframe = document.getElementById('timeframe').value;
        const startDate = document.getElementById('startDate').value;
        const initialBalance = parseFloat(document.getElementById('initialBalance').value);
        
        // Form verilerini doğrula
        if (!symbol || !timeframe || !startDate || isNaN(initialBalance)) {
            alert('Lütfen tüm alanları doldurun!');
            return;
        }
        
        const formData = {
            symbol: symbol,
            timeframe: timeframe,
            start_date: startDate,
            initial_balance: initialBalance
        };
        
        // Simülasyonu başlat
        const response = await fetch('/api/run-simulation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        if (!response.ok) {
            // Hata detaylarını al
            const errorData = await response.json();
            const errorMessage = errorData.error || 'Bilinmeyen bir hata oluştu.';
            
            if (response.status === 404) {
                alert(`Hata: Seçilen parametreler için veri bulunamadı. Lütfen tarih aralığını veya sembolü değiştirin. (Detay: ${errorMessage})`);
            } else {
                throw new Error(`HTTP error! status: ${response.status}. Detay: ${errorMessage}`);
            }
        }
        
        console.log('Yanıt başarıyla alındı, JSON çözümleniyor...');
        const results = await response.json();
        console.log('JSON başarıyla çözümlendi. Sonuçlar:', results);
        
        // Grafikleri güncelle
        updatePriceChart(results);
        updatePerformanceChart(results);
        updateTradeHistory(results);
        updatePerformanceMetrics(results);
        updateSimulationLog(results);
        
    } catch (error) {
        console.error('Simülasyon çalıştırılırken hata oluştu:', error);
        alert('Simülasyon çalıştırılırken bir hata oluştu. Lütfen tekrar deneyin.');
    }
}

// Fiyat grafiğini güncelle
function updatePriceChart(results) {
    const dates = results.dates;
    const prices = results.prices || results.data?.map(d => d.close) || [];
    
    const trace = {
        x: dates,
        y: prices,
        type: 'scatter',
        mode: 'lines',
        name: 'Fiyat',
        line: {
            color: '#17a2b8'
        }
    };
    
    const layout = {
        title: 'Fiyat Grafiği',
        xaxis: {
            title: 'Tarih'
        },
        yaxis: {
            title: 'Fiyat (USDT)'
        }
    };
    
    Plotly.newPlot('priceChart', [trace], layout);
}

// Performans grafiğini güncelle
function updatePerformanceChart(results) {
    const dates = results.dates;
    const equity = results.equity_curve;
    const drawdown = results.drawdown_curve;
    
    const equityTrace = {
        x: dates,
        y: equity,
        type: 'scatter',
        mode: 'lines',
        name: 'Equity',
        line: {
            color: '#28a745'
        }
    };
    
    const drawdownTrace = {
        x: dates,
        y: drawdown,
        type: 'scatter',
        mode: 'lines',
        name: 'Drawdown',
        line: {
            color: '#dc3545'
        }
    };
    
    const layout = {
        title: 'Performans Grafiği',
        xaxis: {
            title: 'Tarih'
        },
        yaxis: {
            title: 'Değer'
        }
    };
    
    Plotly.newPlot('performanceChart', [equityTrace, drawdownTrace], layout);
}

// İşlem geçmişini güncelle
function updateTradeHistory(results) {
    const tbody = document.querySelector('#tradeHistory tbody');
    tbody.innerHTML = '';
    
    results.trades.forEach(trade => {
        const row = document.createElement('tr');
        
        // İşlem tipine göre renk belirle
        let typeClass = '';
        let typeText = '';
        
        switch (trade.type) {
            case 'open_long':
                typeClass = 'text-success';
                typeText = 'Long Aç';
                break;
            case 'close_long':
                typeClass = 'text-danger';
                typeText = 'Long Kapat';
                break;
            case 'open_short':
                typeClass = 'text-danger';
                typeText = 'Short Aç';
                break;
            case 'close_short':
                typeClass = 'text-success';
                typeText = 'Short Kapat';
                break;
        }
        
        row.innerHTML = `
            <td>${new Date(trade.timestamp).toLocaleString()}</td>
            <td class="${typeClass}">${typeText}</td>
            <td>${trade.price.toFixed(2)}</td>
            <td>${trade.size ? trade.size.toFixed(4) : '-'}</td>
            <td class="${trade.profit >= 0 ? 'text-success' : 'text-danger'}">
                ${trade.profit ? trade.profit.toFixed(2) : '-'}
            </td>
            <td>${trade.balance.toFixed(2)}</td>
        `;
        
        tbody.appendChild(row);
    });
}

// Performans metriklerini güncelle
function updatePerformanceMetrics(results) {
    const metrics = document.getElementById('performanceMetrics');
    
    metrics.innerHTML = `
        <div class="row">
            <div class="col-6">
                <p><strong>Sembol:</strong> ${results.symbol}</p>
                <p><strong>Zaman Aralığı:</strong> ${results.timeframe}</p>
                <p><strong>Başlangıç Tarihi:</strong> ${results.start_date}</p>
                <p><strong>Başlangıç Bakiyesi:</strong> ${results.initial_balance.toFixed(2)} USDT</p>
                <p><strong>Son Bakiye:</strong> ${results.final_balance.toFixed(2)} USDT</p>
                <p><strong>Toplam Kar/Zarar:</strong> 
                    <span class="${results.total_profit >= 0 ? 'text-success' : 'text-danger'}">
                        ${results.total_profit.toFixed(2)} USDT (${results.profit_percentage.toFixed(2)}%)
                    </span>
                </p>
            </div>
            <div class="col-6">
                <p><strong>Toplam İşlem:</strong> ${results.total_trades}</p>
                <p><strong>Kazanan İşlem:</strong> ${results.winning_trades}</p>
                <p><strong>Kaybeden İşlem:</strong> ${results.losing_trades}</p>
                <p><strong>Kazanma Oranı:</strong> ${(results.win_rate * 100).toFixed(2)}%</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-6">
                <p><strong>Yıllık Getiri:</strong> ${(results.annual_return * 100).toFixed(2)}%</p>
                <p><strong>Volatilite:</strong> ${(results.volatility * 100).toFixed(2)}%</p>
            </div>
            <div class="col-6">
                <p><strong>Sharpe Oranı:</strong> ${results.sharpe_ratio.toFixed(2)}</p>
                <p><strong>Maximum Drawdown:</strong> ${(results.max_drawdown * 100).toFixed(2)}%</p>
            </div>
        </div>
    `;
}

// Simülasyon loglarını güncelle
function updateSimulationLog(results) {
    const logElement = document.getElementById('simulationLog');
    logElement.innerHTML = ''; // Mevcut logları temizle

    if (results.log && Array.isArray(results.log)) {
        results.log.forEach(logMessage => {
            const p = document.createElement('p');
            p.textContent = logMessage;
            logElement.appendChild(p);
        });
    } else {
        logElement.innerHTML = '<p>Log bulunamadı veya formatı hatalı.</p>';
    }
} 