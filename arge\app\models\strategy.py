"""
Simülasyon stratejilerini içeren sınıf
"""
import pandas as pd
import numpy as np
from typing import Dict
from .indicators import TechnicalIndicators

class TradingStrategy:
    def __init__(self, config, strategy_settings=None):
        """
        TradingStrategy sınıfının başlatıcı metodu

        Args:
            config: Konfigürasyon nesnesi
            strategy_settings: Kullanıcı strateji ayarları (opsiyonel)
        """
        self.config = config
        self.indicators = TechnicalIndicators()

        # Kullanıc<PERSON> ayarları varsa kullan, yoksa varsay<PERSON><PERSON> değ<PERSON>leri kullan
        if strategy_settings:
            self.stop_loss_pct = strategy_settings.get('stopLoss', 4) / 100
            self.take_profit_pct = strategy_settings.get('takeProfit', 8) / 100
            self.max_position_size = strategy_settings.get('maxPosition', 25) / 100
            self.trailing_stop_pct = strategy_settings.get('trailingStop', 3) / 100
            self.base_position_pct = strategy_settings.get('basePosition', 15) / 100
            self.min_position_pct = strategy_settings.get('minPosition', 8) / 100
            self.rsi_lower = strategy_settings.get('rsiLower', 55)
            self.rsi_upper = strategy_settings.get('rsiUpper', 75)
            self.ema_spread = strategy_settings.get('emaSpread', 0.2) / 100
            self.momentum_score_min = strategy_settings.get('momentumScore', 8)  # 12 puan sisteminde 8
            self.price_change_3 = strategy_settings.get('priceChange3', 0.5) / 100
            self.price_change_10 = strategy_settings.get('priceChange10', 1.0) / 100
            self.volatility_max = strategy_settings.get('volatilityMax', 4) / 100
            self.enable_short = strategy_settings.get('enableShort', False)
            self.dynamic_trailing = strategy_settings.get('dynamicTrailing', True)
        else:
            # Varsayılan değerler
            self.stop_loss_pct = 0.04  # %4 stop loss
            self.take_profit_pct = 0.08  # %8 take profit
            self.max_position_size = 0.25  # %25 maksimum pozisyon
            self.trailing_stop_pct = 0.03  # %3 trailing stop
            self.base_position_pct = 0.15  # %15 temel pozisyon
            self.min_position_pct = 0.08  # %8 minimum pozisyon
            self.rsi_lower = 55
            self.rsi_upper = 75
            self.ema_spread = 0.002  # %0.2
            self.momentum_score_min = 8  # 12 puan sisteminde 8
            self.price_change_3 = 0.005  # %0.5
            self.price_change_10 = 0.01  # %1.0
            self.volatility_max = 0.04  # %4
            self.enable_short = False
            self.dynamic_trailing = True
        
    def calculate_position_size(self, balance: float, current_price: float, signal_strength: float = 1.0) -> float:
        """
        Pozisyon büyüklüğünü hesaplar - Konservatif yaklaşım

        Args:
            balance (float): Mevcut bakiye
            current_price (float): Mevcut fiyat
            risk_per_trade (float): İşlem başına risk yüzdesi (düşürüldü %1.5)

        Returns:
            float: Pozisyon büyüklüğü
        """
        # Sinyal gücüne göre pozisyon büyüklüğü
        base_percentage = self.base_position_pct

        # Sinyal gücüne göre çarpan (0.8 - 1.5 arası)
        multiplier = 0.8 + (signal_strength * 0.7)

        position_percentage = base_percentage * multiplier
        position_value = balance * position_percentage
        position_size = position_value / current_price

        # Minimum ve maksimum kontrolleri
        min_value = balance * self.min_position_pct
        max_value = balance * self.max_position_size

        min_size = min_value / current_price
        max_size = max_value / current_price

        return max(min_size, min(position_size, max_size))
        
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Teknik indikatörlere göre alım/satım sinyalleri üretir
        
        Args:
            df (pd.DataFrame): Fiyat ve indikatör verileri
            
        Returns:
            pd.DataFrame: Sinyaller eklenmiş DataFrame
        """
        print("generate_signals fonksiyonu çalışıyor") # Test logu
        # İndikatörleri hesapla
        df = self.indicators.add_all_indicators(df)
        
        # Sinyal sütununu oluştur (0: nötr, 1: al, -1: sat)
        df['signal'] = 0
        
        # Trend gücü hesapla
        df['trend_strength'] = abs(df['ema_20'] - df['ema_50']) / df['ema_50']
        
        # Volatilite hesapla
        df['volatility'] = df['bb_upper'] - df['bb_lower']
        df['volatility_ratio'] = df['volatility'] / df['close']
        
        # EMA Crossover Stratejisi - Optimize edildi
        df['ema_cross'] = np.where(
            (df['ema_20'] > df['ema_50']) & (df['ema_20'].shift(1) <= df['ema_50'].shift(1)) & (df['trend_strength'] > 0.005),
            1,  # Alış sinyali
            np.where(
                (df['ema_20'] < df['ema_50']) & (df['ema_20'].shift(1) >= df['ema_50'].shift(1)) & (df['trend_strength'] > 0.005),
                -1,  # Satış sinyali
                0  # Nötr
            )
        )

        # RSI Stratejisi - Daha esnek eşikler
        df['rsi_signal'] = np.where(
            (df['rsi'] < 40) & (df['rsi'].shift(1) >= 40) & (df['volatility_ratio'] < 0.03),
            1,  # Aşırı satım - Alış sinyali
            np.where(
                (df['rsi'] > 60) & (df['rsi'].shift(1) <= 60) & (df['volatility_ratio'] < 0.03),
                -1,  # Aşırı alım - Satış sinyali
                0  # Nötr
            )
        )

        # MACD Stratejisi - Histogram momentum eklendi
        df['macd_signal'] = np.where(
            (df['macd'] > df['macd_signal']) & (df['macd'].shift(1) <= df['macd_signal'].shift(1)) &
            (df['macd_hist'] > df['macd_hist'].shift(1)),  # Histogram artıyor
            1,  # MACD sinyal çizgisini yukarı kesiyor - Alış sinyali
            np.where(
                (df['macd'] < df['macd_signal']) & (df['macd'].shift(1) >= df['macd_signal'].shift(1)) &
                (df['macd_hist'] < df['macd_hist'].shift(1)),  # Histogram azalıyor
                -1,  # MACD sinyal çizgisini aşağı kesiyor - Satış sinyali
                0  # Nötr
            )
        )

        # Bollinger Bantları Stratejisi - Orta bant desteği eklendi
        df['bb_signal'] = np.where(
            ((df['close'] < df['bb_lower']) |
             ((df['close'] < df['bb_middle']) & (df['close'].shift(1) >= df['bb_middle']))) &
            (df['volatility_ratio'] < 0.03),
            1,  # Fiyat alt bandın altında veya orta bandı aşağı kesiyor - Alış sinyali
            np.where(
                ((df['close'] > df['bb_upper']) |
                 ((df['close'] > df['bb_middle']) & (df['close'].shift(1) <= df['bb_middle']))) &
                (df['volatility_ratio'] < 0.03),
                -1,  # Fiyat üst bandın üstünde veya orta bandı yukarı kesiyor - Satış sinyali
                0  # Nötr
            )
        )
        
        # Basit ve etkili strateji - Sadece trend takibi
        # EMA trend + RSI konfirmasyonu
        df['trend_signal'] = np.where(
            (df['ema_20'] > df['ema_50']) & (df['close'] > df['ema_20']),  # Güçlü yükseliş trendi
            1,  # Alış sinyali
            np.where(
                (df['ema_20'] < df['ema_50']) & (df['close'] < df['ema_20']),  # Güçlü düşüş trendi
                -1,  # Satış sinyali
                0   # Nötr
            )
        )

        # RSI konfirmasyonu (aşırı alım/satım bölgelerinden kaçın)
        df['rsi_filter'] = np.where(
            (df['rsi'] > 20) & (df['rsi'] < 80),  # RSI aşırı bölgelerde değil
            1,  # Güvenli
            0   # Riskli
        )

        # MACD momentum konfirmasyonu
        df['macd_momentum'] = np.where(
            df['macd_hist'] > df['macd_hist'].shift(1),  # MACD histogramı artıyor
            1,  # Pozitif momentum
            -1  # Negatif momentum
        )

        # Yüksek kaliteli sinyal stratejisi - Az işlem, yüksek kar
        df['price_change_3'] = df['close'].pct_change(3)   # 3 periyotluk değişim
        df['price_change_10'] = df['close'].pct_change(10) # 10 periyotluk değişim
        df['volume_sma'] = df['volume'].rolling(20).mean() if 'volume' in df.columns else 1

        # BTC/USDT için optimize edilmiş momentum skoru (15 puan sistemi)
        df['momentum_score'] = (
            # Trend indikatörleri (7 puan)
            (df['ema_20'] > df['ema_50']).astype(int) * 2 +  # EMA trend (2 puan)
            (df['close'] > df['ema_20']).astype(int) * 1 +   # Fiyat pozisyonu (1 puan)
            (df['adx'] > 20).astype(int) * 2 +               # Güçlü trend (düşürüldü)
            (df['di_plus'] > df['di_minus']).astype(int) * 1 + # Pozitif yön (1 puan)
            ((df['close'] > df['vwap']) if 'vwap' in df.columns else 0).astype(int) * 1 + # VWAP üstü (1 puan)

            # Momentum indikatörleri (5 puan)
            (df['macd'] > df['macd_signal']).astype(int) * 1 + # MACD (1 puan)
            (df['rsi'] > 45).astype(int) * 1 +                # RSI (düşürüldü)
            (df['williams_r'] > -60).astype(int) * 1 +        # Williams %R (esnek)
            (df['cci'] > 0).astype(int) * 1 +                 # CCI (1 puan)
            (df['mfi'] > 50).astype(int) * 1 if 'mfi' in df.columns else 0 + # MFI (1 puan)

            # Volatilite ve hacim indikatörleri (3 puan)
            (df['roc'] > 0).astype(int) * 1 if 'roc' in df.columns else 0 + # ROC pozitif (1 puan)
            (df['close'] > df['bb_middle']).astype(int) * 1 + # Bollinger orta üstü (1 puan)
            (df['stoch_k'] > df['stoch_d']).astype(int) * 1   # Stochastic momentum (1 puan)

            # Hacim ve fiyat indikatörleri (2 puan)
            (df['close'] > df['vwap']).astype(int) * 1 +      # VWAP üstünde (1 puan)
            (df['close'] > df['parabolic_sar']).astype(int) * 1 # SAR üstünde (1 puan)
        )  # Toplam 12 puan

        # Ichimoku Cloud sinyalleri
        df['ichimoku_signal'] = np.where(
            (df['close'] > df['ichimoku_senkou_a']) &
            (df['close'] > df['ichimoku_senkou_b']) &
            (df['ichimoku_tenkan'] > df['ichimoku_kijun']),
            1,  # Bulut üstünde ve tenkan > kijun
            np.where(
                (df['close'] < df['ichimoku_senkou_a']) &
                (df['close'] < df['ichimoku_senkou_b']) &
                (df['ichimoku_tenkan'] < df['ichimoku_kijun']),
                -1,  # Bulut altında ve tenkan < kijun
                0
            )
        )

        # BTC/USDT için basitleştirilmiş ve etkili sinyal sistemi
        df['signal'] = np.where(
            # BASİT VE ETKİLİ ALIŞ KOŞULLARİ
            (df['momentum_score'] >= self.momentum_score_min) &              # Momentum skoru (düşük eşik)
            (df['ema_20'] > df['ema_50']) &                                  # EMA trend pozitif
            (df['close'] > df['ema_20']) &                                   # Fiyat EMA20 üstünde
            (df['rsi'] > self.rsi_lower) & (df['rsi'] < self.rsi_upper) &    # RSI aralığı (geniş)
            (df['macd_hist'] > 0),                                           # MACD histogram pozitif
            1,  # Alış sinyali
            np.where(
                # BASİT ÇIKIŞ KOŞULLARİ
                (df['ema_20'] < df['ema_50']) |                              # Trend bozuldu
                (df['close'] < df['ema_20'] * 0.995) |                       # Fiyat EMA20'den %0.5 aşağı
                (df['rsi'] > self.rsi_upper) |                               # RSI aşırı alım
                (df['macd_hist'] < 0),                                       # MACD histogram negatif
                -1,  # Satış sinyali
                0    # Nötr
            )
        )
        
        # Detaylı loglama
        for i in range(1, len(df)):
            if pd.notna(df['close'].iloc[i]): # Geçerli veri kontrolü
                timestamp = df.index[i].strftime('%Y-%m-%d %H:%M:%S')
                close_price = df['close'].iloc[i]

                ema20 = df['ema_20'].iloc[i]
                ema50 = df['ema_50'].iloc[i]
                rsi_val = df['rsi'].iloc[i]
                macd_val = df['macd'].iloc[i]
                macd_signal_val = df['macd_signal'].iloc[i]
                macd_hist_val = df['macd_hist'].iloc[i]
                bb_upper_val = df['bb_upper'].iloc[i]
                bb_lower_val = df['bb_lower'].iloc[i]

                ema_cross_sig = df['ema_cross'].iloc[i]
                rsi_sig = df['rsi_signal'].iloc[i]
                macd_sig = df['macd_signal'].iloc[i]
                bb_sig = df['bb_signal'].iloc[i]
                final_signal = df['signal'].iloc[i]

                # Loglama - Değerlerin sayı olup olmadığını kontrol et ve formatla
                ema20_str = f"{ema20:.2f}" if pd.notna(ema20) else "N/A"
                ema50_str = f"{ema50:.2f}" if pd.notna(ema50) else "N/A"
                rsi_val_str = f"{rsi_val:.2f}" if pd.notna(rsi_val) else "N/A"
                macd_val_str = f"{macd_val:.2f}" if pd.notna(macd_val) else "N/A"
                macd_signal_val_str = f"{macd_signal_val:.2f}" if pd.notna(macd_signal_val) else "N/A"
                macd_hist_val_str = f"{macd_hist_val:.2f}" if pd.notna(macd_hist_val) else "N/A"
                bb_upper_val_str = f"{bb_upper_val:.2f}" if pd.notna(bb_upper_val) else "N/A"
                bb_lower_val_str = f"{bb_lower_val:.2f}" if pd.notna(bb_lower_val) else "N/A"
                ema_cross_sig_str = f"{ema_cross_sig:.0f}" if pd.notna(ema_cross_sig) else "N/A"
                rsi_sig_str = f"{rsi_sig:.0f}" if pd.notna(rsi_sig) else "N/A"
                macd_sig_str = f"{macd_sig:.0f}" if pd.notna(macd_sig) else "N/A"
                bb_sig_str = f"{bb_sig:.0f}" if pd.notna(bb_sig) else "N/A"
                final_signal_str = f"{final_signal:.0f}" if pd.notna(final_signal) else "N/A"

                print(f"\n--- Zaman: {timestamp}, Fiyat: {close_price:.2f} ---")
                print(f"EMA(20): {ema20_str}, EMA(50): {ema50_str} -> EMA Sinyal: {ema_cross_sig_str}")
                print(f"RSI: {rsi_val_str} -> RSI Sinyal: {rsi_sig_str}")
                print(f"MACD: {macd_val_str}, MACD Sinyal Hattı: {macd_signal_val_str}, Histogram: {macd_hist_val_str} -> MACD Sinyal: {macd_sig_str}")
                print(f"BB (Üst: {bb_upper_val_str}, Alt: {bb_lower_val_str}) -> BB Sinyal: {bb_sig_str}")
                print(f"Nihai Sinyal: {final_signal_str}")
        
        return df
    
    def backtest(self, df: pd.DataFrame, initial_balance: float = 10000) -> Dict:
        """
        Stratejiyi geçmiş veriler üzerinde test eder
        
        Args:
            df (pd.DataFrame): Fiyat ve sinyal verileri
            initial_balance (float): Başlangıç bakiyesi
            
        Returns:
            Dict: Backtest sonuçları
        """
        balance = initial_balance
        position = 0  # 0: pozisyon yok, 1: long (short kaldırıldı)
        position_size = 0
        entry_price = 0
        highest_price = 0
        trades = []
        log = []
        
        # Komisyon oranı
        commission_rate = self.config.get('COMMISSION_RATE', 0.0002) # Varsayılan %0.02
        
        for i in range(1, len(df)):
            current_price = df['close'].iloc[i]
            signal = df['signal'].iloc[i]
            
            # Gelişmiş trailing stop kontrolü
            if position == 1:  # Long pozisyon
                highest_price = max(highest_price, current_price)

                # Dinamik trailing stop - kar arttıkça daha sıkı takip
                current_profit_pct = (current_price - entry_price) / entry_price

                if current_profit_pct > 0.05:  # %5+ kar varsa
                    trailing_pct = 0.02  # %2 trailing stop
                elif current_profit_pct > 0.03:  # %3+ kar varsa
                    trailing_pct = 0.025  # %2.5 trailing stop
                else:
                    trailing_pct = self.trailing_stop_pct  # Normal %3 trailing stop

                if current_price < highest_price * (1 - trailing_pct):
                    # Trailing stop tetiklendi
                    profit = (current_price - entry_price) * position_size
                    commission = current_price * position_size * commission_rate
                    balance += profit - commission # Komisyonu düş
                    trades.append({
                        'type': 'close_long_trailing',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'commission': commission # Komisyon bilgisini ekle
                    })
                    net_profit = profit - commission
                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon trailing stop ile kapatıldı. Kar: {current_profit_pct*100:.1f}%, Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                    position = 0
                    position_size = 0
                    entry_price = 0
                    highest_price = 0
                    
            # Short pozisyon mantığı kaldırıldı - sadece long trading
            
            # Stop loss ve take profit kontrolü
            if position != 0:
                if position == 1:  # Long pozisyon
                    if current_price <= entry_price * (1 - self.stop_loss_pct):
                        # Stop loss tetiklendi
                        loss = (current_price - entry_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += loss - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_long_stop',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': loss,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = loss - commission # loss zaten negatif
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon stop loss ile kapatıldı. Brüt Kar/Zarar: {loss:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        highest_price = 0
                    elif current_price >= entry_price * (1 + self.take_profit_pct):
                        # Take profit tetiklendi
                        profit = (current_price - entry_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += profit - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_long_profit',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = profit - commission
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon take profit ile kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        highest_price = 0
                        
                # Short pozisyon mantığı kaldırıldı - sadece long trading
            
            # Yeni pozisyon açma - Sadece long pozisyonlar
            if signal == 1 and position == 0:  # Alış sinyali ve pozisyon yok
                # Sinyal gücünü hesapla (0-1 arası)
                momentum_score = df['momentum_score'].iloc[i]
                signal_strength = min(momentum_score / 6.0, 1.0)  # 6 maksimum puan

                # Long pozisyon aç
                position = 1
                position_size = self.calculate_position_size(balance, current_price, signal_strength)
                entry_price = current_price
                highest_price = current_price

                # Alış işlemi komisyonu
                commission = current_price * position_size * commission_rate
                balance -= commission # Alış komisyonu düş

                trades.append({
                    'type': 'open_long',
                    'price': current_price,
                    'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                    'size': position_size,
                    'balance': balance, # Komisyon düşülmüş bakiye
                    'commission': commission # Komisyon bilgisini ekle
                })
                log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon açıldı. Sinyal Gücü: {signal_strength:.2f}, Açılış Fiyatı: {entry_price:.2f}, Pozisyon Büyüklüğü: {position_size:.4f}, Komisyon: {commission:.2f}, Kalan Bakiye: {balance:.2f}") # Loga komisyonu ve kalan bakiyeyi ekle
                
            elif signal == -1 and position == 1:  # Satış sinyali - sadece long pozisyonu kapat
                # Long pozisyonu kapat
                profit = (current_price - entry_price) * position_size
                commission = current_price * position_size * commission_rate
                balance += profit - commission # Komisyonu düş
                trades.append({
                    'type': 'close_long',
                    'price': current_price,
                    'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                    'profit': profit,
                    'balance': balance,
                    'commission': commission # Komisyon bilgisini ekle
                })
                net_profit = profit - commission
                log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle

                # Pozisyonu sıfırla
                position = 0
                position_size = 0
                entry_price = 0
                highest_price = 0

                # Short pozisyon açmıyoruz - sadece long trading
        
        # Son pozisyonu kapat (sadece long pozisyonlar)
        if position == 1:
            current_price = df['close'].iloc[-1]
            profit = (current_price - entry_price) * position_size

            # Kapatma işlemi komisyonu
            commission = current_price * position_size * commission_rate
            balance += profit - commission # Komisyonu düş

            trades.append({
                'type': 'close_position',
                'price': current_price,
                'timestamp': df.index[-1].strftime('%Y-%m-%d %H:%M:%S'),
                'profit': profit,
                'balance': balance,
                'commission': commission # Komisyon bilgisini ekle
            })
            net_profit = profit - commission
            log.append(f"{df.index[-1].strftime('%Y-%m-%d %H:%M:%S')} - Kalan LONG Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
        
        # Performans metriklerini hesapla
        total_trades = len([t for t in trades if t['type'] in ['open_long', 'open_short']])
        # Tüm kapatma işlemlerinin net karını/zararını hesaplayarak kazanan ve kaybeden işlemleri say
        winning_trades = 0
        losing_trades = 0
        
        for trade in trades:
            # Kapatma işlemlerini kontrol et
            if trade['type'].startswith('close'):
                # Net kar/zarar = Brüt Kar/Zarar - Komisyon (komisyon yoksa 0 kabul et)
                net_result = trade['profit'] - trade.get('commission', 0)
                
                if net_result > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_profit = balance - initial_balance
        profit_percentage = (total_profit / initial_balance) * 100
        
        # Backtest başlangıç tarihini DataFrame'in ilk indeksinden al
        start_date = df.index[0].strftime('%Y-%m-%d %H:%M:%S') if not df.empty else 'N/A'
        
        return {
            'initial_balance': initial_balance,
            'final_balance': balance,
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'trades': trades,
            'log': log,
            'backtest_start_date': start_date # Hesaplanan başlangıç tarihini ekle
        } 