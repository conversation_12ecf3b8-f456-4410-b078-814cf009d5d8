"""
Simülasyon stratejilerini içeren sınıf
"""
import pandas as pd
import numpy as np
from typing import Dict
from .indicators import TechnicalIndicators
from .multi_timeframe import MultiTimeframeAnalyzer

class TradingStrategy:
    def __init__(self, config, strategy_settings=None):
        """
        TradingStrategy sınıfının başlatıcı metodu

        Args:
            config: Konfigürasyon nesnesi
            strategy_settings: Kullanıcı strateji ayarları (opsiyonel)
        """
        self.config = config
        self.indicators = TechnicalIndicators()
        self.multi_timeframe = MultiTimeframeAnalyzer(config)

        # <PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON> kullan, yoksa var<PERSON><PERSON><PERSON> de<PERSON> kull<PERSON>
        if strategy_settings:
            self.stop_loss_pct = strategy_settings.get('stopLoss', 4) / 100
            self.take_profit_pct = strategy_settings.get('takeProfit', 8) / 100
            self.max_position_size = strategy_settings.get('maxPosition', 25) / 100
            self.trailing_stop_pct = strategy_settings.get('trailingStop', 3) / 100
            self.base_position_pct = strategy_settings.get('basePosition', 15) / 100
            self.min_position_pct = strategy_settings.get('minPosition', 8) / 100
            self.rsi_lower = strategy_settings.get('rsiLower', 55)
            self.rsi_upper = strategy_settings.get('rsiUpper', 75)
            self.ema_spread = strategy_settings.get('emaSpread', 0.2) / 100
            self.momentum_score_min = strategy_settings.get('momentumScore', 8)  # 12 puan sisteminde 8
            self.price_change_3 = strategy_settings.get('priceChange3', 0.5) / 100
            self.price_change_10 = strategy_settings.get('priceChange10', 1.0) / 100
            self.volatility_max = strategy_settings.get('volatilityMax', 4) / 100
            self.enable_short = strategy_settings.get('enableShort', False)
            self.dynamic_trailing = strategy_settings.get('dynamicTrailing', True)
        else:
            # Forex için optimize edilmiş varsayılan değerler
            self.stop_loss_pct = 0.02  # %2 stop loss (forex için daha dar)
            self.take_profit_pct = 0.04  # %4 take profit (forex için daha dar)
            self.max_position_size = 0.25  # %25 maksimum pozisyon
            self.trailing_stop_pct = 0.015  # %1.5 trailing stop (forex için daha dar)
            self.base_position_pct = 0.15  # %15 temel pozisyon
            self.min_position_pct = 0.08  # %8 minimum pozisyon
            self.rsi_lower = 30  # Forex için daha geniş aralık
            self.rsi_upper = 70  # Forex için daha geniş aralık
            self.ema_spread = 0.001  # %0.1 (forex için daha hassas)
            self.momentum_score_min = 6  # Daha düşük eşik (daha fazla sinyal)
            self.price_change_3 = 0.002  # %0.2 (forex için daha küçük)
            self.price_change_10 = 0.005  # %0.5 (forex için daha küçük)
            self.volatility_max = 0.02  # %2 (forex için daha düşük)
            self.enable_short = True  # Forex'te short pozisyon etkin
            self.dynamic_trailing = True
        
    def calculate_position_size(self, balance: float, current_price: float, signal_strength: float = 1.0) -> float:
        """
        Pozisyon büyüklüğünü hesaplar - Konservatif yaklaşım

        Args:
            balance (float): Mevcut bakiye
            current_price (float): Mevcut fiyat
            risk_per_trade (float): İşlem başına risk yüzdesi (düşürüldü %1.5)

        Returns:
            float: Pozisyon büyüklüğü
        """
        # Sinyal gücüne göre pozisyon büyüklüğü
        base_percentage = self.base_position_pct

        # Sinyal gücüne göre çarpan (0.8 - 1.5 arası)
        multiplier = 0.8 + (signal_strength * 0.7)

        position_percentage = base_percentage * multiplier
        position_value = balance * position_percentage
        position_size = position_value / current_price

        # Minimum ve maksimum kontrolleri
        min_value = balance * self.min_position_pct
        max_value = balance * self.max_position_size

        min_size = min_value / current_price
        max_size = max_value / current_price

        return max(min_size, min(position_size, max_size))
        
    def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Teknik indikatörlere göre alım/satım sinyalleri üretir
        
        Args:
            df (pd.DataFrame): Fiyat ve indikatör verileri
            
        Returns:
            pd.DataFrame: Sinyaller eklenmiş DataFrame
        """
        print("generate_signals fonksiyonu çalışıyor") # Test logu
        print(f"🎯 Kullanılan parametreler:")
        print(f"   RSI Aralığı: {self.rsi_lower} - {self.rsi_upper}")
        print(f"   Momentum Min: {self.momentum_score_min}")
        print(f"   Stop Loss: {self.stop_loss_pct*100:.1f}%")
        print(f"   Take Profit: {self.take_profit_pct*100:.1f}%")
        print(f"   Base Position: {self.base_position_pct*100:.1f}%")

        # İndikatörleri hesapla
        df = self.indicators.add_all_indicators(df)
        
        # Sinyal sütununu oluştur (0: nötr, 1: al, -1: sat)
        df['signal'] = 0
        
        # Trend gücü hesapla
        df['trend_strength'] = abs(df['ema_20'] - df['ema_50']) / df['ema_50']
        
        # Volatilite hesapla
        df['volatility'] = df['bb_upper'] - df['bb_lower']
        df['volatility_ratio'] = df['volatility'] / df['close']
        
        # EMA Crossover Stratejisi - Optimize edildi
        df['ema_cross'] = np.where(
            (df['ema_20'] > df['ema_50']) & (df['ema_20'].shift(1) <= df['ema_50'].shift(1)) & (df['trend_strength'] > 0.005),
            1,  # Alış sinyali
            np.where(
                (df['ema_20'] < df['ema_50']) & (df['ema_20'].shift(1) >= df['ema_50'].shift(1)) & (df['trend_strength'] > 0.005),
                -1,  # Satış sinyali
                0  # Nötr
            )
        )

        # RSI Stratejisi - Daha esnek eşikler
        df['rsi_signal'] = np.where(
            (df['rsi'] < 40) & (df['rsi'].shift(1) >= 40) & (df['volatility_ratio'] < 0.03),
            1,  # Aşırı satım - Alış sinyali
            np.where(
                (df['rsi'] > 60) & (df['rsi'].shift(1) <= 60) & (df['volatility_ratio'] < 0.03),
                -1,  # Aşırı alım - Satış sinyali
                0  # Nötr
            )
        )

        # MACD Stratejisi - Histogram momentum eklendi
        df['macd_signal'] = np.where(
            (df['macd'] > df['macd_signal']) & (df['macd'].shift(1) <= df['macd_signal'].shift(1)) &
            (df['macd_hist'] > df['macd_hist'].shift(1)),  # Histogram artıyor
            1,  # MACD sinyal çizgisini yukarı kesiyor - Alış sinyali
            np.where(
                (df['macd'] < df['macd_signal']) & (df['macd'].shift(1) >= df['macd_signal'].shift(1)) &
                (df['macd_hist'] < df['macd_hist'].shift(1)),  # Histogram azalıyor
                -1,  # MACD sinyal çizgisini aşağı kesiyor - Satış sinyali
                0  # Nötr
            )
        )

        # Bollinger Bantları Stratejisi - Orta bant desteği eklendi
        df['bb_signal'] = np.where(
            ((df['close'] < df['bb_lower']) |
             ((df['close'] < df['bb_middle']) & (df['close'].shift(1) >= df['bb_middle']))) &
            (df['volatility_ratio'] < 0.03),
            1,  # Fiyat alt bandın altında veya orta bandı aşağı kesiyor - Alış sinyali
            np.where(
                ((df['close'] > df['bb_upper']) |
                 ((df['close'] > df['bb_middle']) & (df['close'].shift(1) <= df['bb_middle']))) &
                (df['volatility_ratio'] < 0.03),
                -1,  # Fiyat üst bandın üstünde veya orta bandı yukarı kesiyor - Satış sinyali
                0  # Nötr
            )
        )
        
        # Basit ve etkili strateji - Sadece trend takibi
        # EMA trend + RSI konfirmasyonu
        df['trend_signal'] = np.where(
            (df['ema_20'] > df['ema_50']) & (df['close'] > df['ema_20']),  # Güçlü yükseliş trendi
            1,  # Alış sinyali
            np.where(
                (df['ema_20'] < df['ema_50']) & (df['close'] < df['ema_20']),  # Güçlü düşüş trendi
                -1,  # Satış sinyali
                0   # Nötr
            )
        )

        # RSI konfirmasyonu (aşırı alım/satım bölgelerinden kaçın)
        df['rsi_filter'] = np.where(
            (df['rsi'] > 20) & (df['rsi'] < 80),  # RSI aşırı bölgelerde değil
            1,  # Güvenli
            0   # Riskli
        )

        # MACD momentum konfirmasyonu
        df['macd_momentum'] = np.where(
            df['macd_hist'] > df['macd_hist'].shift(1),  # MACD histogramı artıyor
            1,  # Pozitif momentum
            -1  # Negatif momentum
        )

        # Yüksek kaliteli sinyal stratejisi - Az işlem, yüksek kar
        df['price_change_3'] = df['close'].pct_change(3)   # 3 periyotluk değişim
        df['price_change_10'] = df['close'].pct_change(10) # 10 periyotluk değişim
        df['volume_sma'] = df['volume'].rolling(20).mean() if 'volume' in df.columns else 1

        # BTC/USDT için optimize edilmiş momentum skoru (15 puan sistemi)
        df['momentum_score'] = (
            # Trend indikatörleri (7 puan)
            (df['ema_20'] > df['ema_50']).astype(int) * 2 +  # EMA trend (2 puan)
            (df['close'] > df['ema_20']).astype(int) * 1 +   # Fiyat pozisyonu (1 puan)
            (df['adx'] > 20).astype(int) * 2 +               # Güçlü trend (düşürüldü)
            (df['di_plus'] > df['di_minus']).astype(int) * 1 + # Pozitif yön (1 puan)
            ((df['close'] > df['vwap']) if 'vwap' in df.columns else 0).astype(int) * 1 + # VWAP üstü (1 puan)

            # Momentum indikatörleri (5 puan)
            (df['macd'] > df['macd_signal']).astype(int) * 1 + # MACD (1 puan)
            (df['rsi'] > 45).astype(int) * 1 +                # RSI (düşürüldü)
            (df['williams_r'] > -60).astype(int) * 1 +        # Williams %R (esnek)
            (df['cci'] > 0).astype(int) * 1 +                 # CCI (1 puan)
            (df['mfi'] > 50).astype(int) * 1 if 'mfi' in df.columns else 0 + # MFI (1 puan)

            # Volatilite ve hacim indikatörleri (3 puan)
            (df['roc'] > 0).astype(int) * 1 if 'roc' in df.columns else 0 + # ROC pozitif (1 puan)
            (df['close'] > df['bb_middle']).astype(int) * 1 + # Bollinger orta üstü (1 puan)
            (df['stoch_k'] > df['stoch_d']).astype(int) * 1   # Stochastic momentum (1 puan)

            # Hacim ve fiyat indikatörleri (2 puan)
            (df['close'] > df['vwap']).astype(int) * 1 +      # VWAP üstünde (1 puan)
            (df['close'] > df['parabolic_sar']).astype(int) * 1 # SAR üstünde (1 puan)
        )  # Toplam 12 puan

        # Ichimoku Cloud sinyalleri
        df['ichimoku_signal'] = np.where(
            (df['close'] > df['ichimoku_senkou_a']) &
            (df['close'] > df['ichimoku_senkou_b']) &
            (df['ichimoku_tenkan'] > df['ichimoku_kijun']),
            1,  # Bulut üstünde ve tenkan > kijun
            np.where(
                (df['close'] < df['ichimoku_senkou_a']) &
                (df['close'] < df['ichimoku_senkou_b']) &
                (df['ichimoku_tenkan'] < df['ichimoku_kijun']),
                -1,  # Bulut altında ve tenkan < kijun
                0
            )
        )

        # Multi-timeframe analiz (1d trend + 4h entry)
        try:
            # Symbol'ü belirle (varsayılan BTC/USDT)
            symbol = "BTC/USDT"
            start_date = df.index[0].strftime('%Y-%m-%d') if not df.empty else "2025-01-01"

            # Multi-timeframe sinyali al
            mtf_signal = self.multi_timeframe.get_multi_timeframe_signal(symbol, start_date)
            # mtf_strength = mtf_signal.get('confidence', 0) / 100.0  # 0-1 arası normalize et

            print(f"Multi-timeframe analizi: Sinyal={mtf_signal.get('signal', 0)}, Güven={mtf_signal.get('confidence', 0)}")

        except Exception as e:
            print(f"Multi-timeframe analizi hatası: {str(e)}")
            mtf_signal = {'signal': 0}
            # mtf_strength = 0

        # FOREX için basit ve etkili sinyal sistemi
        # RSI kontrolü - eğer RSI hesaplanamadıysa (N/A) RSI koşulunu atla
        rsi_valid = df['rsi'].notna()

        df['signal'] = np.where(
            # FOREX ALIŞ KOŞULLARİ (Basit ve etkili)
            (df['ema_20'] > df['ema_50']) &                                  # EMA trend pozitif
            (df['close'] > df['ema_20']) &                                   # Fiyat EMA20 üstünde
            (df['macd_hist'] > df['macd_hist'].shift(1)) &                   # MACD momentum artıyor
            (
                (~rsi_valid) |                                               # RSI yok ise koşulu atla
                ((df['rsi'] > 30) & (df['rsi'] < 70))                       # RSI var ise kontrol et
            ),
            1,  # Alış sinyali
            np.where(
                # FOREX SATIŞ KOŞULLARİ (Short pozisyon için)
                (df['ema_20'] < df['ema_50']) &                              # EMA trend negatif
                (df['close'] < df['ema_20']) &                               # Fiyat EMA20 altında
                (df['macd_hist'] < df['macd_hist'].shift(1)) &               # MACD momentum azalıyor
                (
                    (~rsi_valid) |                                           # RSI yok ise koşulu atla
                    ((df['rsi'] > 30) & (df['rsi'] < 70))                   # RSI var ise kontrol et
                ) &
                self.enable_short,                                           # Short pozisyon etkin
                -1,  # Satış sinyali
                0    # Nötr
            )
        )

        # Sinyal istatistikleri
        total_signals = len(df)
        buy_signals = len(df[df['signal'] == 1])
        sell_signals = len(df[df['signal'] == -1])
        neutral_signals = len(df[df['signal'] == 0])

        print(f"📊 Sinyal İstatistikleri:")
        print(f"   Toplam Veri: {total_signals}")
        print(f"   Alış Sinyali: {buy_signals} ({buy_signals/total_signals*100:.1f}%)")
        print(f"   Satış Sinyali: {sell_signals} ({sell_signals/total_signals*100:.1f}%)")
        print(f"   Nötr: {neutral_signals} ({neutral_signals/total_signals*100:.1f}%)")
        print(f"   Short Pozisyon Etkin: {self.enable_short}")

        # Son 10 sinyali göster
        if len(df) >= 10:
            print(f"📈 Son 10 Sinyal:")
            for i in range(-10, 0):
                date = df.index[i].strftime('%Y-%m-%d')
                signal = df['signal'].iloc[i]
                rsi = df['rsi'].iloc[i]
                ema20 = df['ema_20'].iloc[i]
                ema50 = df['ema_50'].iloc[i]
                close = df['close'].iloc[i]
                signal_text = "ALIŞ" if signal == 1 else "SATIŞ" if signal == -1 else "NÖTR"
                print(f"   {date}: {signal_text} (RSI:{rsi:.1f}, Close:{close:.5f}, EMA20:{ema20:.5f}, EMA50:{ema50:.5f})")

        return df
    
    def backtest(self, df: pd.DataFrame, initial_balance: float = 10000) -> Dict:
        """
        Stratejiyi geçmiş veriler üzerinde test eder
        
        Args:
            df (pd.DataFrame): Fiyat ve sinyal verileri
            initial_balance (float): Başlangıç bakiyesi
            
        Returns:
            Dict: Backtest sonuçları
        """
        balance = initial_balance
        position = 0  # 0: pozisyon yok, 1: long (short kaldırıldı)
        position_size = 0
        entry_price = 0
        highest_price = 0
        trades = []
        log = []
        
        # Komisyon oranı
        commission_rate = self.config.get('COMMISSION_RATE', 0.0002) # Varsayılan %0.02
        
        for i in range(1, len(df)):
            current_price = df['close'].iloc[i]
            signal = df['signal'].iloc[i]
            
            # Gelişmiş trailing stop kontrolü
            if position == 1:  # Long pozisyon
                highest_price = max(highest_price, current_price)

                # Dinamik trailing stop - kar arttıkça daha sıkı takip
                current_profit_pct = (current_price - entry_price) / entry_price

                if current_profit_pct > 0.05:  # %5+ kar varsa
                    trailing_pct = 0.02  # %2 trailing stop
                elif current_profit_pct > 0.03:  # %3+ kar varsa
                    trailing_pct = 0.025  # %2.5 trailing stop
                else:
                    trailing_pct = self.trailing_stop_pct  # Normal %3 trailing stop

                if current_price < highest_price * (1 - trailing_pct):
                    # Trailing stop tetiklendi
                    profit = (current_price - entry_price) * position_size
                    commission = current_price * position_size * commission_rate
                    balance += profit - commission # Komisyonu düş
                    trades.append({
                        'type': 'close_long_trailing',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'commission': commission # Komisyon bilgisini ekle
                    })
                    net_profit = profit - commission
                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon trailing stop ile kapatıldı. Kar: {current_profit_pct*100:.1f}%, Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                    position = 0
                    position_size = 0
                    entry_price = 0
                    highest_price = 0
                    
            # Short pozisyon mantığı kaldırıldı - sadece long trading
            
            # Stop loss ve take profit kontrolü
            if position != 0:
                if position == 1:  # Long pozisyon
                    if current_price <= entry_price * (1 - self.stop_loss_pct):
                        # Stop loss tetiklendi
                        loss = (current_price - entry_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += loss - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_long_stop',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': loss,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = loss - commission # loss zaten negatif
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon stop loss ile kapatıldı. Brüt Kar/Zarar: {loss:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        highest_price = 0
                    elif current_price >= entry_price * (1 + self.take_profit_pct):
                        # Take profit tetiklendi
                        profit = (current_price - entry_price) * position_size
                        commission = current_price * position_size * commission_rate
                        balance += profit - commission # Komisyonu düş
                        trades.append({
                            'type': 'close_long_profit',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'commission': commission # Komisyon bilgisini ekle
                        })
                        net_profit = profit - commission
                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon take profit ile kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
                        position = 0
                        position_size = 0
                        entry_price = 0
                        highest_price = 0
                        
                # Short pozisyon mantığı kaldırıldı - sadece long trading
            
            # Yeni pozisyon açma - Sadece long pozisyonlar
            if signal == 1 and position == 0:  # Alış sinyali ve pozisyon yok
                # Sinyal gücünü hesapla (0-1 arası)
                momentum_score = df['momentum_score'].iloc[i]
                signal_strength = min(momentum_score / 6.0, 1.0)  # 6 maksimum puan

                # Long pozisyon aç
                position = 1
                position_size = self.calculate_position_size(balance, current_price, signal_strength)
                entry_price = current_price
                highest_price = current_price

                # Alış işlemi komisyonu
                commission = current_price * position_size * commission_rate
                balance -= commission # Alış komisyonu düş

                trades.append({
                    'type': 'open_long',
                    'price': current_price,
                    'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                    'size': position_size,
                    'balance': balance, # Komisyon düşülmüş bakiye
                    'commission': commission # Komisyon bilgisini ekle
                })
                log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon açıldı. Sinyal Gücü: {signal_strength:.2f}, Açılış Fiyatı: {entry_price:.2f}, Pozisyon Büyüklüğü: {position_size:.4f}, Komisyon: {commission:.2f}, Kalan Bakiye: {balance:.2f}") # Loga komisyonu ve kalan bakiyeyi ekle
                
            elif signal == -1 and position == 1:  # Satış sinyali - sadece long pozisyonu kapat
                # Long pozisyonu kapat
                profit = (current_price - entry_price) * position_size
                commission = current_price * position_size * commission_rate
                balance += profit - commission # Komisyonu düş
                trades.append({
                    'type': 'close_long',
                    'price': current_price,
                    'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                    'profit': profit,
                    'balance': balance,
                    'commission': commission # Komisyon bilgisini ekle
                })
                net_profit = profit - commission
                log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle

                # Pozisyonu sıfırla
                position = 0
                position_size = 0
                entry_price = 0
                highest_price = 0

                # Short pozisyon açmıyoruz - sadece long trading
        
        # Son pozisyonu kapat (sadece long pozisyonlar)
        if position == 1:
            current_price = df['close'].iloc[-1]
            profit = (current_price - entry_price) * position_size

            # Kapatma işlemi komisyonu
            commission = current_price * position_size * commission_rate
            balance += profit - commission # Komisyonu düş

            trades.append({
                'type': 'close_position',
                'price': current_price,
                'timestamp': df.index[-1].strftime('%Y-%m-%d %H:%M:%S'),
                'profit': profit,
                'balance': balance,
                'commission': commission # Komisyon bilgisini ekle
            })
            net_profit = profit - commission
            log.append(f"{df.index[-1].strftime('%Y-%m-%d %H:%M:%S')} - Kalan LONG Pozisyon kapatıldı. Brüt Kar/Zarar: {profit:.2f}, Komisyon: {commission:.2f}, Net Kar/Zarar: {net_profit:.2f}, Yeni Bakiye: {balance:.2f}") # Loga net karı ekle
        
        # Performans metriklerini hesapla
        total_trades = len([t for t in trades if t['type'] in ['open_long', 'open_short']])
        # Tüm kapatma işlemlerinin net karını/zararını hesaplayarak kazanan ve kaybeden işlemleri say
        winning_trades = 0
        losing_trades = 0
        
        for trade in trades:
            # Kapatma işlemlerini kontrol et
            if trade['type'].startswith('close'):
                # Net kar/zarar = Brüt Kar/Zarar - Komisyon (komisyon yoksa 0 kabul et)
                net_result = trade['profit'] - trade.get('commission', 0)
                
                if net_result > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        total_profit = balance - initial_balance
        profit_percentage = (total_profit / initial_balance) * 100
        
        # Backtest başlangıç tarihini DataFrame'in ilk indeksinden al
        start_date = df.index[0].strftime('%Y-%m-%d %H:%M:%S') if not df.empty else 'N/A'
        
        return {
            'initial_balance': initial_balance,
            'final_balance': balance,
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'trades': trades,
            'log': log,
            'backtest_start_date': start_date # Hesaplanan başlangıç tarihini ekle
        }

    def backtest_forex(self, df: pd.DataFrame, initial_balance: float = 10000, leverage: int = 100, lot_size: float = 0.1) -> Dict:
        """
        Forex stratejisini kaldıraç ile test eder

        Args:
            df (pd.DataFrame): Fiyat ve sinyal verileri
            initial_balance (float): Başlangıç bakiyesi
            leverage (int): Kaldıraç oranı (1-500)
            lot_size (float): Lot büyüklüğü (0.01-10.0)

        Returns:
            Dict: Backtest sonuçları
        """
        balance = initial_balance
        equity = initial_balance
        position = 0  # 0: pozisyon yok, 1: long, -1: short
        entry_price = 0
        highest_price = 0
        lowest_price = 0
        trades = []
        log = []

        # Forex parametreleri
        pip_value = 0.0001  # EUR/USD için pip değeri
        spread = 0.0001  # Spread (1 pip)
        margin_call_level = 0.2  # %20 margin seviyesi

        # Lot büyüklüğü hesaplama (1 lot = 100,000 birim)
        contract_size = 100000
        position_value = lot_size * contract_size

        for i in range(1, len(df)):
            current_price = df['close'].iloc[i]
            signal = df['signal'].iloc[i]

            # Margin hesaplama
            if position != 0:
                # Açık pozisyon değeri
                required_margin = position_value / leverage

                # Unrealized P&L hesaplama
                if position == 1:  # Long pozisyon
                    unrealized_pnl = (current_price - entry_price) * position_value
                elif position == -1:  # Short pozisyon
                    unrealized_pnl = (entry_price - current_price) * position_value
                else:
                    unrealized_pnl = 0

                # Equity hesaplama
                equity = balance + unrealized_pnl

                # Margin seviyesi kontrolü
                margin_level = equity / required_margin if required_margin > 0 else float('inf')

                # Margin call kontrolü
                if margin_level < margin_call_level:
                    # Margin call - pozisyonu zorla kapat
                    if position == 1:  # Long pozisyon
                        profit = (current_price - spread - entry_price) * position_value
                    else:  # Short pozisyon
                        profit = (entry_price - current_price - spread) * position_value

                    balance += profit
                    equity = balance

                    trades.append({
                        'type': 'margin_call',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'margin_level': margin_level
                    })

                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - MARGIN CALL! Pozisyon zorla kapatıldı. Margin Seviyesi: {margin_level:.1%}, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                    position = 0
                    entry_price = 0
                    highest_price = 0
                    lowest_price = 0
                    continue

            # Trailing stop kontrolü
            if position == 1:  # Long pozisyon
                highest_price = max(highest_price, current_price)

                # Dinamik trailing stop
                current_profit_pips = (current_price - entry_price) / pip_value

                if current_profit_pips > 50:  # 50+ pip kar varsa
                    trailing_pips = 20  # 20 pip trailing stop
                elif current_profit_pips > 30:  # 30+ pip kar varsa
                    trailing_pips = 15  # 15 pip trailing stop
                else:
                    trailing_pips = int(self.trailing_stop_pct * 10000 / pip_value)  # Normal trailing stop

                if current_price < highest_price - (trailing_pips * pip_value):
                    # Trailing stop tetiklendi
                    profit = (current_price - spread - entry_price) * position_value
                    balance += profit
                    equity = balance

                    trades.append({
                        'type': 'close_long_trailing',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'pips': current_profit_pips
                    })

                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon trailing stop ile kapatıldı. Kar: {current_profit_pips:.1f} pip, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                    position = 0
                    entry_price = 0
                    highest_price = 0

            elif position == -1:  # Short pozisyon
                lowest_price = min(lowest_price, current_price)

                # Dinamik trailing stop
                current_profit_pips = (entry_price - current_price) / pip_value

                if current_profit_pips > 50:  # 50+ pip kar varsa
                    trailing_pips = 20  # 20 pip trailing stop
                elif current_profit_pips > 30:  # 30+ pip kar varsa
                    trailing_pips = 15  # 15 pip trailing stop
                else:
                    trailing_pips = int(self.trailing_stop_pct * 10000 / pip_value)  # Normal trailing stop

                if current_price > lowest_price + (trailing_pips * pip_value):
                    # Trailing stop tetiklendi
                    profit = (entry_price - current_price - spread) * position_value
                    balance += profit
                    equity = balance

                    trades.append({
                        'type': 'close_short_trailing',
                        'price': current_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'profit': profit,
                        'balance': balance,
                        'pips': current_profit_pips
                    })

                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon trailing stop ile kapatıldı. Kar: {current_profit_pips:.1f} pip, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                    position = 0
                    entry_price = 0
                    lowest_price = 0

            # Stop loss ve take profit kontrolü
            if position != 0:
                stop_loss_pips = int(self.stop_loss_pct * 10000 / pip_value)
                take_profit_pips = int(self.take_profit_pct * 10000 / pip_value)

                if position == 1:  # Long pozisyon
                    if current_price <= entry_price - (stop_loss_pips * pip_value):
                        # Stop loss tetiklendi
                        profit = (current_price - spread - entry_price) * position_value
                        balance += profit
                        equity = balance

                        trades.append({
                            'type': 'close_long_stop',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'pips': -stop_loss_pips
                        })

                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon stop loss ile kapatıldı. Zarar: {-stop_loss_pips} pip, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                        position = 0
                        entry_price = 0
                        highest_price = 0

                    elif current_price >= entry_price + (take_profit_pips * pip_value):
                        # Take profit tetiklendi
                        profit = (current_price - spread - entry_price) * position_value
                        balance += profit
                        equity = balance

                        trades.append({
                            'type': 'close_long_profit',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'pips': take_profit_pips
                        })

                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon take profit ile kapatıldı. Kar: {take_profit_pips} pip, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                        position = 0
                        entry_price = 0
                        highest_price = 0

                elif position == -1:  # Short pozisyon
                    if current_price >= entry_price + (stop_loss_pips * pip_value):
                        # Stop loss tetiklendi
                        profit = (entry_price - current_price - spread) * position_value
                        balance += profit
                        equity = balance

                        trades.append({
                            'type': 'close_short_stop',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'pips': -stop_loss_pips
                        })

                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon stop loss ile kapatıldı. Zarar: {-stop_loss_pips} pip, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                        position = 0
                        entry_price = 0
                        lowest_price = 0

                    elif current_price <= entry_price - (take_profit_pips * pip_value):
                        # Take profit tetiklendi
                        profit = (entry_price - current_price - spread) * position_value
                        balance += profit
                        equity = balance

                        trades.append({
                            'type': 'close_short_profit',
                            'price': current_price,
                            'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                            'profit': profit,
                            'balance': balance,
                            'pips': take_profit_pips
                        })

                        log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon take profit ile kapatıldı. Kar: {take_profit_pips} pip, Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

                        position = 0
                        entry_price = 0
                        lowest_price = 0

            # Yeni pozisyon açma
            if position == 0:  # Pozisyon yok
                required_margin = position_value / leverage



                if signal == 1 and balance >= required_margin:  # Long pozisyon aç
                    position = 1
                    entry_price = current_price + spread  # Spread dahil
                    highest_price = current_price

                    trades.append({
                        'type': 'open_long',
                        'price': entry_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'lot_size': lot_size,
                        'leverage': leverage,
                        'margin': required_margin,
                        'balance': balance
                    })

                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - LONG Pozisyon açıldı. Lot: {lot_size}, Kaldıraç: {leverage}:1, Açılış: {entry_price:.5f}, Margin: {required_margin:.2f}, Bakiye: {balance:.2f}")

                elif signal == -1 and balance >= required_margin and self.enable_short:  # Short pozisyon aç
                    position = -1
                    entry_price = current_price - spread  # Spread dahil
                    lowest_price = current_price

                    trades.append({
                        'type': 'open_short',
                        'price': entry_price,
                        'timestamp': df.index[i].strftime('%Y-%m-%d %H:%M:%S'),
                        'lot_size': lot_size,
                        'leverage': leverage,
                        'margin': required_margin,
                        'balance': balance
                    })

                    log.append(f"{df.index[i].strftime('%Y-%m-%d %H:%M:%S')} - SHORT Pozisyon açıldı. Lot: {lot_size}, Kaldıraç: {leverage}:1, Açılış: {entry_price:.5f}, Margin: {required_margin:.2f}, Bakiye: {balance:.2f}")

        # Son pozisyonu kapat
        if position != 0:
            current_price = df['close'].iloc[-1]

            if position == 1:  # Long pozisyon
                profit = (current_price - spread - entry_price) * position_value
            else:  # Short pozisyon
                profit = (entry_price - current_price - spread) * position_value

            balance += profit
            equity = balance

            trades.append({
                'type': 'close_position',
                'price': current_price,
                'timestamp': df.index[-1].strftime('%Y-%m-%d %H:%M:%S'),
                'profit': profit,
                'balance': balance
            })

            log.append(f"{df.index[-1].strftime('%Y-%m-%d %H:%M:%S')} - Kalan pozisyon kapatıldı. Kar/Zarar: {profit:.2f}, Yeni Bakiye: {balance:.2f}")

        # Sonuçları hesapla
        total_profit = balance - initial_balance
        profit_percentage = (total_profit / initial_balance) * 100

        # İşlem istatistikleri
        winning_trades = len([t for t in trades if t.get('profit', 0) > 0])
        losing_trades = len([t for t in trades if t.get('profit', 0) < 0])
        total_trades = winning_trades + losing_trades
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0

        results = {
            'initial_balance': initial_balance,
            'final_balance': balance,
            'total_profit': total_profit,
            'profit_percentage': profit_percentage,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'leverage': leverage,
            'lot_size': lot_size,
            'trades': trades,
            'log': log
        }

        return results