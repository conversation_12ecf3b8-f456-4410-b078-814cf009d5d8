#!/usr/bin/env python3
"""
BTC/USDT Machine Learning Regime Detection Testi
Adaptif strateji ile %10+ kar hedefi
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.ml_regime_detection import MLRegimeDetector
from app.models.config import Config
import pandas as pd

def test_ml_regime_strategy():
    """ML Regime Detection ile adaptif BTC/USDT strateji testi"""
    
    print("🤖 BTC/USDT ML Regime Detection Strateji Testi Başlatılıyor...")
    
    # Test parametreleri
    symbol = "BTC/USDT"
    start_date = "2025-01-01"
    initial_balance = 10000
    
    try:
        # Config ve veri çekme
        config = Config()
        data_fetcher = DataFetcher(config)
        df = data_fetcher.fetch_ohlcv(symbol, "1d", start_date)
        
        if df is None or len(df) < 50:
            print("❌ Yeterli veri bulunamadı!")
            return None
            
        print(f"✅ {len(df)} adet veri noktası alındı")
        
        # ML Regime Detector
        print("🤖 Machine Learning regime detection başlatılıyor...")
        ml_detector = MLRegimeDetector()
        
        # Rejimleri tespit et
        df_with_regimes = ml_detector.detect_regimes(df)
        
        print("\n" + "="*60)
        print("🤖 MACHINE LEARNING REGIME ANALİZİ")
        print("="*60)
        
        # Rejim dağılımı
        regime_counts = df_with_regimes['regime_name'].value_counts()
        print("📊 Tespit Edilen Rejimler:")
        for regime, count in regime_counts.items():
            percentage = (count / len(df_with_regimes)) * 100
            print(f"   • {regime}: {count} gün (%{percentage:.1f})")
        
        # Son rejim bilgisi
        last_regime = df_with_regimes['regime_name'].iloc[-1]
        last_confidence = df_with_regimes['regime_confidence'].iloc[-1]
        print(f"\n🎯 Mevcut Rejim: {last_regime}")
        print(f"🔥 Güven Seviyesi: %{last_confidence*100:.1f}")
        
        # Rejim bazlı performans analizi
        regime_performance = ml_detector.analyze_regime_performance(df_with_regimes)
        print(f"\n📈 Rejim Bazlı Performans:")
        for regime, perf in regime_performance.items():
            print(f"   • {regime}:")
            print(f"     - Ortalama Getiri: %{perf['avg_return']*100:.2f}")
            print(f"     - Volatilite: %{perf['volatility']*100:.2f}")
            print(f"     - Sharpe Oranı: {perf['sharpe']:.2f}")
            print(f"     - Max Drawdown: %{perf['max_drawdown']*100:.2f}")
        
        # Adaptif strateji parametreleri
        adaptive_params = ml_detector.get_regime_strategy_params(last_regime, last_confidence)
        
        print(f"\n🎯 {last_regime} İçin Adaptif Parametreler:")
        print(f"   • Take Profit: %{adaptive_params['takeProfit']}")
        print(f"   • Stop Loss: %{adaptive_params['stopLoss']}")
        print(f"   • Base Position: %{adaptive_params['basePosition']}")
        print(f"   • Max Position: %{adaptive_params['maxPosition']}")
        print(f"   • Momentum Score: {adaptive_params['momentumScore']}")
        
        # Simülasyon testi
        print("\n🔄 ML Adaptif strateji ile simülasyon çalıştırılıyor...")
        simulation_engine = SimulationEngine(config, adaptive_params)
        
        # Simülasyonu çalıştır
        results = simulation_engine.run_simulation(df, initial_balance)
        
        # Sonuçları analiz et
        print("\n" + "="*60)
        print("🤖 ML ADAPTIF STRATEJİ SONUÇLARI")
        print("="*60)
        
        print(f"💰 Başlangıç Bakiye: ${initial_balance:,.2f}")
        print(f"💰 Son Bakiye: ${results['final_balance']:,.2f}")
        print(f"📊 Toplam Kar: ${results['total_profit']:,.2f}")
        print(f"📈 Kar Oranı: %{results['profit_percentage']:.2f}")
        print(f"🔢 Toplam İşlem: {results['total_trades']}")
        print(f"✅ Kazanan İşlem: {results['winning_trades']}")
        print(f"❌ Kaybeden İşlem: {results['losing_trades']}")
        print(f"🎯 Kazanma Oranı: %{results['win_rate']:.2f}")
        print(f"📉 Maksimum Düşüş: %{results['max_drawdown']:.2f}")
        print(f"⚡ Sharpe Oranı: {results['sharpe_ratio']:.3f}")
        
        # Hedef analizi
        target_profit_pct = 10.0  # %10 hedef
        if results['profit_percentage'] >= target_profit_pct:
            print(f"\n🎉 BAŞARILI! %{target_profit_pct} hedefine ulaşıldı!")
        else:
            needed_improvement = target_profit_pct - results['profit_percentage']
            print(f"\n⚠️  Hedef için %{needed_improvement:.2f} daha kar gerekli")
        
        # ML Avantajları
        print("\n" + "="*60)
        print("🤖 MACHINE LEARNING AVANTAJLARI")
        print("="*60)
        
        if last_confidence > 0.8:
            print("✅ Yüksek güven seviyesi - Rejim tespiti güçlü")
        elif last_confidence > 0.6:
            print("⚠️  Orta güven seviyesi - Rejim belirsiz")
        else:
            print("❌ Düşük güven seviyesi - Rejim değişim döneminde")
            
        # Rejim bazlı öneriler
        if last_regime == "Bull_Market":
            print("🚀 Boğa piyasası tespit edildi - Agresif strateji öneriliyor")
        elif last_regime == "Bear_Market":
            print("🐻 Ayı piyasası tespit edildi - Konservatif strateji öneriliyor")
        elif last_regime == "High_Volatility":
            print("⚡ Yüksek volatilite tespit edildi - Hızlı giriş/çıkış öneriliyor")
        else:
            print("📊 Yatay piyasa tespit edildi - Range trading öneriliyor")
        
        # Gelişmiş öneriler
        print("\n" + "="*60)
        print("💡 ML ADAPTIF STRATEJİ ÖNERİLERİ")
        print("="*60)
        
        if results['profit_percentage'] < target_profit_pct:
            print("🔧 Önerilen iyileştirmeler:")
            
            if results['total_trades'] < 5:
                print("   • Rejim değişim sinyallerini kullan")
                print("   • Daha kısa timeframe'lerde test et")
                print("   • Feature engineering'i iyileştir")
                
            if results['win_rate'] < 50:
                print("   • Rejim geçiş dönemlerinde işlem yapma")
                print("   • Güven seviyesi düşük dönemlerde bekle")
                print("   • Ensemble model kullan")
                
            if results['max_drawdown'] > 15:
                print("   • Rejim değişiminde pozisyon küçült")
                print("   • Dynamic stop loss kullan")
                print("   • Portfolio diversification ekle")
        
        # Rejim geçiş analizi
        print("\n📊 Rejim Geçiş Analizi:")
        regime_changes = (df_with_regimes['regime_name'] != df_with_regimes['regime_name'].shift(1)).sum()
        print(f"   • Toplam rejim değişimi: {regime_changes}")
        print(f"   • Ortalama rejim süresi: {len(df_with_regimes) / (regime_changes + 1):.1f} gün")
        
        return results
        
    except Exception as e:
        print(f"❌ Hata oluştu: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def compare_all_strategies():
    """Tüm stratejilerin karşılaştırması"""
    print("\n" + "="*60)
    print("⚔️  TÜM STRATEJİLER KARŞILAŞTIRMASI")
    print("="*60)
    
    print("🔍 Normal Strateji:")
    print("   • Sabit parametreler")
    print("   • Tek timeframe")
    print("   • Basit teknik indikatörler")
    print("   • Sonuç: ~%3 kar")
    
    print("\n🚀 Multi-timeframe Strateji:")
    print("   • 1d trend + 4h entry")
    print("   • Çoklu konfirmasyon")
    print("   • Destek/Direnç analizi")
    print("   • Sonuç: ~%3.5 kar")
    
    print("\n🤖 ML Adaptif Strateji:")
    print("   • Piyasa rejimi tespiti")
    print("   • Adaptif parametreler")
    print("   • Machine learning")
    print("   • Hedef: %10+ kar")
    
    print("\n💡 Önerilen Hibrit Yaklaşım:")
    print("   • ML Regime Detection")
    print("   • + Multi-timeframe analiz")
    print("   • + Dynamic position sizing")
    print("   • + Risk management")

if __name__ == "__main__":
    # ML Regime test
    results = test_ml_regime_strategy()
    
    # Tüm stratejiler karşılaştırması
    compare_all_strategies()
    
    print("\n🏁 ML Regime Detection test tamamlandı!")
