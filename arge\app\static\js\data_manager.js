// Veri yöneticisi JavaScript dosyası

let isDownloading = false;

// <PERSON>fa yüklendiğinde çalışacak fonksiyonlar
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    loadDatasetsList();
});

// Sayfayı başlat
function initializePage() {
    // Tarih alanlarını ayarla
    const today = new Date();
    const oneMonthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    document.getElementById('downloadStartDate').value = oneMonthAgo.toISOString().split('T')[0];
    document.getElementById('downloadEndDate').value = today.toISOString().split('T')[0];
    
    // Varsayılan veri seti adı
    const defaultName = `dataset_${today.getFullYear()}_${(today.getMonth() + 1).toString().padStart(2, '0')}_${today.getDate().toString().padStart(2, '0')}`;
    document.getElementById('datasetName').value = defaultName;
}

// Event listener'ları ayarla
function setupEventListeners() {
    // Form submit
    document.getElementById('downloadDataForm').addEventListener('submit', function(e) {
        e.preventDefault();
        startDataDownload();
    });
    
    // Symbol filter değişikliği
    document.getElementById('downloadSymbols').addEventListener('change', function() {
        const customDiv = document.getElementById('customSymbolsDiv');
        if (this.value === 'custom') {
            customDiv.style.display = 'block';
        } else {
            customDiv.style.display = 'none';
        }
    });
    
    // Veri seti işlemleri
    document.getElementById('setActiveDataset').addEventListener('click', setActiveDataset);
    document.getElementById('deleteDataset').addEventListener('click', deleteDataset);
    
    // Veri seti seçimi
    document.getElementById('activeDataset').addEventListener('change', function() {
        if (this.value) {
            loadDatasetDetails(this.value);
        } else {
            document.getElementById('datasetDetailsCard').style.display = 'none';
        }
    });
}

// Veri indirmeyi başlat
async function startDataDownload() {
    if (isDownloading) {
        alert('Veri indirme zaten devam ediyor!');
        return;
    }
    
    isDownloading = true;
    
    try {
        // Form verilerini al
        const symbols = getSelectedSymbols();
        const timeframes = getSelectedTimeframes();
        const startDate = document.getElementById('downloadStartDate').value;
        const endDate = document.getElementById('downloadEndDate').value;
        const datasetName = document.getElementById('datasetName').value;
        
        if (symbols.length === 0) {
            alert('En az bir para birimi seçin!');
            return;
        }
        
        if (timeframes.length === 0) {
            alert('En az bir zaman dilimi seçin!');
            return;
        }
        
        if (!datasetName.trim()) {
            alert('Veri seti adı girin!');
            return;
        }
        
        // Progress göster
        showDownloadProgress();
        
        // Veri indirme isteği gönder
        const response = await fetch('/api/download-dataset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                symbols: symbols,
                timeframes: timeframes,
                start_date: startDate,
                end_date: endDate,
                dataset_name: datasetName
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
            showNotification('Veri seti başarıyla indirildi!', 'success');
            loadDatasetsList(); // Listeyi yenile
        } else {
            throw new Error(result.error || 'Veri indirme başarısız');
        }
        
    } catch (error) {
        console.error('Veri indirme hatası:', error);
        showNotification('Veri indirme sırasında hata oluştu: ' + error.message, 'error');
    } finally {
        isDownloading = false;
        hideDownloadProgress();
    }
}

// Seçili sembolleri al
function getSelectedSymbols() {
    const symbolFilter = document.getElementById('downloadSymbols').value;
    
    const predefinedSymbols = {
        'major': ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT'],
        'top10': [
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT',
            'XRP/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'SHIB/USDT'
        ],
        'top20': [
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT',
            'XRP/USDT', 'DOT/USDT', 'DOGE/USDT', 'AVAX/USDT', 'SHIB/USDT',
            'MATIC/USDT', 'LTC/USDT', 'UNI/USDT', 'LINK/USDT', 'ATOM/USDT',
            'ETC/USDT', 'XLM/USDT', 'BCH/USDT', 'ALGO/USDT', 'VET/USDT'
        ]
    };
    
    if (symbolFilter === 'custom') {
        const customSymbols = document.getElementById('customSymbolsList').value;
        return customSymbols.split(',').map(s => s.trim()).filter(s => s.length > 0);
    } else if (predefinedSymbols[symbolFilter]) {
        return predefinedSymbols[symbolFilter];
    }
    
    return [];
}

// Seçili zaman dilimlerini al
function getSelectedTimeframes() {
    const timeframes = [];
    const checkboxes = document.querySelectorAll('input[type="checkbox"][id^="tf_"]');
    
    checkboxes.forEach(checkbox => {
        if (checkbox.checked) {
            timeframes.push(checkbox.value);
        }
    });
    
    return timeframes;
}

// İndirme progress'ini göster
function showDownloadProgress() {
    document.getElementById('downloadProgressCard').style.display = 'block';
    document.getElementById('startDownload').disabled = true;
    document.getElementById('startDownload').innerHTML = '<i class="fas fa-spinner fa-spin"></i> İndiriliyor...';
}

// İndirme progress'ini gizle
function hideDownloadProgress() {
    document.getElementById('downloadProgressCard').style.display = 'none';
    document.getElementById('startDownload').disabled = false;
    document.getElementById('startDownload').innerHTML = '<i class="fas fa-download"></i> Veri İndirmeyi Başlat';
}

// Veri setleri listesini yükle
async function loadDatasetsList() {
    try {
        const response = await fetch('/api/list-datasets');
        const datasets = await response.json();
        
        updateDatasetsDisplay(datasets);
        updateActiveDatasetSelect(datasets);
        
    } catch (error) {
        console.error('Veri setleri listesi yüklenemedi:', error);
    }
}

// Veri setleri görünümünü güncelle
function updateDatasetsDisplay(datasets) {
    const container = document.getElementById('datasetsList');
    
    if (datasets.length === 0) {
        container.innerHTML = `
            <div class="text-center text-muted py-3">
                <i class="fas fa-folder-open fa-2x mb-2"></i>
                <p>Henüz veri seti yok</p>
                <small>Veri indirerek başlayın</small>
            </div>
        `;
        return;
    }
    
    let html = '';
    datasets.forEach(dataset => {
        const isActive = dataset.is_active ? '<span class="badge bg-success">Aktif</span>' : '';
        const createdDate = new Date(dataset.created_at).toLocaleDateString('tr-TR');
        
        html += `
            <div class="card mb-2">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${dataset.name} ${isActive}</h6>
                            <small class="text-muted">
                                ${dataset.symbols_count} para birimi, ${dataset.timeframes_count} zaman dilimi
                                <br>Oluşturulma: ${createdDate}
                            </small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary" onclick="selectDataset('${dataset.name}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

// Aktif veri seti select'ini güncelle
function updateActiveDatasetSelect(datasets) {
    const select = document.getElementById('activeDataset');
    select.innerHTML = '<option value="">Veri seti seçin</option>';
    
    datasets.forEach(dataset => {
        const option = document.createElement('option');
        option.value = dataset.name;
        option.textContent = dataset.name;
        if (dataset.is_active) {
            option.selected = true;
        }
        select.appendChild(option);
    });
}

// Veri seti seç
function selectDataset(datasetName) {
    document.getElementById('activeDataset').value = datasetName;
    loadDatasetDetails(datasetName);
}

// Veri seti detaylarını yükle
async function loadDatasetDetails(datasetName) {
    try {
        const response = await fetch(`/api/dataset-details/${datasetName}`);
        const details = await response.json();
        
        displayDatasetDetails(details);
        
    } catch (error) {
        console.error('Veri seti detayları yüklenemedi:', error);
    }
}

// Veri seti detaylarını göster
function displayDatasetDetails(details) {
    const container = document.getElementById('datasetDetails');
    
    const html = `
        <h6>${details.name}</h6>
        <div class="row">
            <div class="col-md-6">
                <strong>Para Birimleri (${details.symbols.length}):</strong>
                <div class="mt-1">
                    ${details.symbols.map(s => `<span class="badge bg-secondary me-1">${s}</span>`).join('')}
                </div>
            </div>
            <div class="col-md-6">
                <strong>Zaman Dilimleri (${details.timeframes.length}):</strong>
                <div class="mt-1">
                    ${details.timeframes.map(t => `<span class="badge bg-info me-1">${t}</span>`).join('')}
                </div>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-6">
                <small><strong>Başlangıç:</strong> ${details.start_date}</small>
            </div>
            <div class="col-md-6">
                <small><strong>Bitiş:</strong> ${details.end_date}</small>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-md-6">
                <small><strong>Toplam Kayıt:</strong> ${details.total_records.toLocaleString()}</small>
            </div>
            <div class="col-md-6">
                <small><strong>Dosya Boyutu:</strong> ${details.file_size}</small>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
    document.getElementById('datasetDetailsCard').style.display = 'block';
}

// Aktif veri setini ayarla
async function setActiveDataset() {
    const datasetName = document.getElementById('activeDataset').value;
    
    if (!datasetName) {
        alert('Veri seti seçin!');
        return;
    }
    
    try {
        const response = await fetch('/api/set-active-dataset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ dataset_name: datasetName })
        });
        
        if (response.ok) {
            showNotification('Aktif veri seti ayarlandı!', 'success');
            loadDatasetsList(); // Listeyi yenile
        } else {
            throw new Error('Veri seti aktif yapılamadı');
        }
        
    } catch (error) {
        console.error('Aktif veri seti ayarlama hatası:', error);
        showNotification('Hata: ' + error.message, 'error');
    }
}

// Veri setini sil
async function deleteDataset() {
    const datasetName = document.getElementById('activeDataset').value;
    
    if (!datasetName) {
        alert('Veri seti seçin!');
        return;
    }
    
    if (!confirm(`"${datasetName}" veri setini silmek istediğinizden emin misiniz?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/delete-dataset/${datasetName}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            showNotification('Veri seti silindi!', 'success');
            loadDatasetsList(); // Listeyi yenile
            document.getElementById('datasetDetailsCard').style.display = 'none';
        } else {
            throw new Error('Veri seti silinemedi');
        }
        
    } catch (error) {
        console.error('Veri seti silme hatası:', error);
        showNotification('Hata: ' + error.message, 'error');
    }
}

// Bildirim göster
function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 'alert-info';
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 5 saniye sonra otomatik kapat
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
