{% extends "base.html" %}

{% block title %}Veri Yöneticisi - Kripto Simülasyon{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-database"></i> Veri Yöneticisi
                <small class="text-muted">Test veri setlerini yönetin</small>
            </h2>
        </div>
    </div>

    <!-- Veri İndirme -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-download"></i> Veri İndirme</h5>
                </div>
                <div class="card-body">
                    <form id="downloadDataForm">
                        <div class="mb-3">
                            <label for="downloadSymbols" class="form-label">Para Birimleri</label>
                            <select class="form-select" id="downloadSymbols" name="symbols">
                                <option value="major">Ana Para Birimleri (BTC, ETH, BNB, ADA, SOL)</option>
                                <option value="top10">Top 10 Para Birimi</option>
                                <option value="top20">Top 20 Para Birimi</option>
                                <option value="custom">Özel Seçim</option>
                            </select>
                        </div>

                        <div class="mb-3" id="customSymbolsDiv" style="display: none;">
                            <label for="customSymbolsList" class="form-label">Özel Para Birimleri</label>
                            <textarea class="form-control" id="customSymbolsList" rows="3" placeholder="BTC/USDT, ETH/USDT, ADA/USDT"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="downloadTimeframes" class="form-label">Zaman Dilimleri</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="tf_1m" value="1m">
                                <label class="form-check-label" for="tf_1m">1 Dakika</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="tf_5m" value="5m">
                                <label class="form-check-label" for="tf_5m">5 Dakika</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="tf_15m" value="15m" checked>
                                <label class="form-check-label" for="tf_15m">15 Dakika</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="tf_1h" value="1h" checked>
                                <label class="form-check-label" for="tf_1h">1 Saat</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="tf_4h" value="4h">
                                <label class="form-check-label" for="tf_4h">4 Saat</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="tf_1d" value="1d">
                                <label class="form-check-label" for="tf_1d">1 Gün</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="downloadStartDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="downloadStartDate" name="startDate">
                        </div>

                        <div class="mb-3">
                            <label for="downloadEndDate" class="form-label">Bitiş Tarihi</label>
                            <input type="date" class="form-control" id="downloadEndDate" name="endDate">
                        </div>

                        <div class="mb-3">
                            <label for="datasetName" class="form-label">Veri Seti Adı</label>
                            <input type="text" class="form-control" id="datasetName" placeholder="Örn: test_dataset_2024" required>
                            <small class="text-muted">Bu isimle kaydedilecek</small>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" id="startDownload">
                            <i class="fas fa-download"></i> Veri İndirmeyi Başlat
                        </button>
                    </form>
                </div>
            </div>

            <!-- İndirme İlerlemesi -->
            <div class="card mt-3" id="downloadProgressCard" style="display: none;">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-spinner fa-spin"></i> Veri İndiriliyor</h6>
                </div>
                <div class="card-body">
                    <div class="progress mb-2">
                        <div class="progress-bar" id="downloadProgressBar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="downloadProgressText">Hazırlanıyor...</div>
                    <div id="downloadCurrentItem" class="text-muted small"></div>
                </div>
            </div>
        </div>

        <!-- Mevcut Veri Setleri -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-folder"></i> Mevcut Veri Setleri</h5>
                </div>
                <div class="card-body">
                    <div id="datasetsList">
                        <div class="text-center text-muted py-3">
                            <i class="fas fa-folder-open fa-2x mb-2"></i>
                            <p>Henüz veri seti yok</p>
                            <small>Veri indirerek başlayın</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Veri Seti Detayları -->
            <div class="card mt-3" id="datasetDetailsCard" style="display: none;">
                <div class="card-header bg-dark text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> Veri Seti Detayları</h6>
                </div>
                <div class="card-body">
                    <div id="datasetDetails"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Veri Seti Kullanımı -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-play-circle"></i> Veri Seti Kullanımı</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Aktif Veri Seti</h6>
                            <select class="form-select" id="activeDataset">
                                <option value="">Veri seti seçin</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <h6>İşlemler</h6>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" id="setActiveDataset">
                                    <i class="fas fa-check"></i> Aktif Yap
                                </button>
                                <button type="button" class="btn btn-outline-danger" id="deleteDataset">
                                    <i class="fas fa-trash"></i> Sil
                                </button>
                                <a href="/bulk-analysis" class="btn btn-success">
                                    <i class="fas fa-chart-bar"></i> Analiz Et
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-lightbulb"></i> Kullanım Talimatları:</h6>
                        <ol class="mb-0">
                            <li><strong>Veri İndirin:</strong> İstediğiniz para birimleri ve zaman dilimlerini seçip indirin</li>
                            <li><strong>Aktif Yapın:</strong> Test etmek istediğiniz veri setini aktif yapın</li>
                            <li><strong>Analiz Edin:</strong> Toplu analiz sayfasında bu veri seti kullanılacak</li>
                            <li><strong>Optimizasyon:</strong> Döngüsel AI optimizasyonu aynı veri seti üzerinde çalışacak</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/data_manager.js') }}"></script>
{% endblock %}
