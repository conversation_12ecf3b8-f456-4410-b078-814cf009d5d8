"""
Teknik analiz indikatörlerini hesaplayan sınıf ve gelişmiş analiz fonksiyonları
"""
import pandas as pd
import numpy as np
from typing import Dict, Tuple

class TechnicalIndicators:
    def __init__(self):
        """
        TechnicalIndicators sınıfının başlatıcı metodu
        """
        pass

    def calculate_sma(self, data: pd.Series, period: int) -> pd.Series:
        """
        Basit Hareketli Ortalama (SMA) hesaplar

        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot

        Returns:
            pd.Series: Hesaplanan SMA değerleri
        """
        return data.rolling(window=period).mean()

    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """
        Üssel Hareketli Ortalama (EMA) hesaplar

        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot

        Returns:
            pd.Series: Hesaplanan EMA değerleri
        """
        return data.ewm(span=period, adjust=False).mean()

    def calculate_rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """
        Göreceli Güç Endeksi (RSI) hesaplar

        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot (varsayılan: 14)

        Returns:
            pd.Series: Hesaplanan RSI değerleri
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def calculate_rsi_advanced(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """
        Gelişmiş RSI hesaplama (numpy tabanlı, daha hızlı)

        Args:
            prices: Fiyat verileri
            period: RSI periyodu (varsayılan: 14)

        Returns:
            RSI değerleri
        """
        deltas = np.diff(prices)
        seed = deltas[:period+1]

        up = seed[seed >= 0].sum()/period
        down = -seed[seed < 0].sum()/period

        rs = up/down if down != 0 else 0
        rsi = np.zeros_like(prices)
        rsi[period] = 100. - 100./(1. + rs)

        for i in range(period+1, len(prices)):
            delta = deltas[i-1]
            if delta > 0:
                upval = delta
                downval = 0.
            else:
                upval = 0.
                downval = -delta

            up = (up*(period-1) + upval)/period
            down = (down*(period-1) + downval)/period

            rs = up/down if down != 0 else 0
            rsi[i] = 100. - 100./(1. + rs)

        return rsi

    def calculate_macd(self, data: pd.Series,
                      fast_period: int = 12,
                      slow_period: int = 26,
                      signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        MACD (Moving Average Convergence Divergence) hesaplar

        Args:
            data (pd.Series): Fiyat verileri
            fast_period (int): Hızlı EMA periyodu (varsayılan: 12)
            slow_period (int): Yavaş EMA periyodu (varsayılan: 26)
            signal_period (int): Sinyal çizgisi periyodu (varsayılan: 9)

        Returns:
            Tuple[pd.Series, pd.Series, pd.Series]: (MACD çizgisi, Sinyal çizgisi, Histogram)
        """
        fast_ema = self.calculate_ema(data, fast_period)
        slow_ema = self.calculate_ema(data, slow_period)

        macd_line = fast_ema - slow_ema
        signal_line = self.calculate_ema(macd_line, signal_period)
        histogram = macd_line - signal_line

        return macd_line, signal_line, histogram

    def calculate_macd_advanced(self, prices: np.ndarray, fast_period: int = 12,
                               slow_period: int = 26, signal_period: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Gelişmiş MACD hesaplama (numpy tabanlı)

        Args:
            prices: Fiyat verileri
            fast_period: Hızlı EMA periyodu
            slow_period: Yavaş EMA periyodu
            signal_period: Sinyal çizgisi periyodu

        Returns:
            MACD, Sinyal ve Histogram değerleri
        """
        ema_fast = pd.Series(prices).ewm(span=fast_period, adjust=False).mean()
        ema_slow = pd.Series(prices).ewm(span=slow_period, adjust=False).mean()

        macd_line = ema_fast - ema_slow
        signal_line = pd.Series(macd_line).ewm(span=signal_period, adjust=False).mean()
        histogram = macd_line - signal_line

        return macd_line.values, signal_line.values, histogram.values

    def calculate_bollinger_bands(self, data: pd.Series,
                                period: int = 20,
                                std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        Bollinger Bantları hesaplar

        Args:
            data (pd.Series): Fiyat verileri
            period (int): Periyot (varsayılan: 20)
            std_dev (float): Standart sapma çarpanı (varsayılan: 2.0)

        Returns:
            Tuple[pd.Series, pd.Series, pd.Series]: (Orta bant, Üst bant, Alt bant)
        """
        middle_band = self.calculate_sma(data, period)
        std = data.rolling(window=period).std()

        upper_band = middle_band + (std * std_dev)
        lower_band = middle_band - (std * std_dev)

        return middle_band, upper_band, lower_band

    def calculate_bollinger_bands_advanced(self, prices: np.ndarray, period: int = 20,
                                          num_std: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Gelişmiş Bollinger Bands hesaplama

        Args:
            prices: Fiyat verileri
            period: SMA periyodu
            num_std: Standart sapma çarpanı

        Returns:
            Orta bant, Üst bant ve Alt bant değerleri
        """
        middle_band = pd.Series(prices).rolling(window=period).mean()
        std = pd.Series(prices).rolling(window=period).std()

        upper_band = middle_band + (std * num_std)
        lower_band = middle_band - (std * num_std)

        return middle_band.values, upper_band.values, lower_band.values

    def calculate_stochastic_oscillator(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                                      k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """
        Stochastic Osilatör hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            close: Kapanış fiyatları
            k_period: %K periyodu
            d_period: %D periyodu

        Returns:
            %K ve %D değerleri
        """
        lowest_low = pd.Series(low).rolling(window=k_period).min()
        highest_high = pd.Series(high).rolling(window=k_period).max()

        k = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d = pd.Series(k).rolling(window=d_period).mean()

        return k.values, d.values

    def calculate_trend_strength(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """
        Trend gücünü hesapla

        Args:
            prices: Fiyat verileri
            period: Trend periyodu

        Returns:
            Trend gücü değerleri (-1 ile 1 arasında)
        """
        price_changes = np.diff(prices)
        trend_strength = np.zeros_like(prices)

        for i in range(period, len(prices)):
            window = price_changes[i-period:i]
            up_moves = np.sum(window[window > 0])
            down_moves = np.abs(np.sum(window[window < 0]))
            total_moves = up_moves + down_moves

            if total_moves > 0:
                trend_strength[i] = (up_moves - down_moves) / total_moves
            else:
                trend_strength[i] = 0

        return trend_strength

    def add_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Tüm teknik indikatörleri DataFrame'e ekler

        Args:
            df (pd.DataFrame): Fiyat verileri içeren DataFrame

        Returns:
            pd.DataFrame: İndikatörler eklenmiş DataFrame
        """
        # SMA hesaplamaları
        df['sma_20'] = self.calculate_sma(df['close'], 20)
        df['sma_50'] = self.calculate_sma(df['close'], 50)
        df['sma_200'] = self.calculate_sma(df['close'], 200)

        # EMA hesaplamaları
        df['ema_20'] = self.calculate_ema(df['close'], 20)
        df['ema_50'] = self.calculate_ema(df['close'], 50)
        df['ema_200'] = self.calculate_ema(df['close'], 200)

        # RSI hesaplaması
        df['rsi'] = self.calculate_rsi(df['close'])

        # MACD hesaplaması
        macd_line, signal_line, histogram = self.calculate_macd(df['close'])
        df['macd'] = macd_line
        df['macd_signal'] = signal_line
        df['macd_hist'] = histogram

        # Bollinger Bantları hesaplaması
        middle_band, upper_band, lower_band = self.calculate_bollinger_bands(df['close'])
        df['bb_middle'] = middle_band
        df['bb_upper'] = upper_band
        df['bb_lower'] = lower_band

        # Stochastic Oscillator
        if 'high' in df.columns and 'low' in df.columns:
            k_line, d_line = self.calculate_stochastic_oscillator(
                df['high'].values, df['low'].values, df['close'].values
            )
            df['stoch_k'] = k_line
            df['stoch_d'] = d_line

        # Trend Strength
        df['trend_strength'] = self.calculate_trend_strength(df['close'].values)

        # Williams %R
        if 'high' in df.columns and 'low' in df.columns:
            df['williams_r'] = self.calculate_williams_r(
                df['high'].values, df['low'].values, df['close'].values
            )

        # CCI (Commodity Channel Index)
        if 'high' in df.columns and 'low' in df.columns:
            df['cci'] = self.calculate_cci(
                df['high'].values, df['low'].values, df['close'].values
            )

        # ADX (Average Directional Index)
        if 'high' in df.columns and 'low' in df.columns:
            adx, di_plus, di_minus = self.calculate_adx(
                df['high'].values, df['low'].values, df['close'].values
            )
            df['adx'] = adx
            df['di_plus'] = di_plus
            df['di_minus'] = di_minus

        # VWAP (Volume Weighted Average Price)
        if 'volume' in df.columns and 'high' in df.columns and 'low' in df.columns:
            df['vwap'] = self.calculate_vwap(
                df['high'].values, df['low'].values, df['close'].values, df['volume'].values
            )

        # Parabolic SAR
        if 'high' in df.columns and 'low' in df.columns:
            df['parabolic_sar'] = self.calculate_parabolic_sar(
                df['high'].values, df['low'].values
            )

        # Ichimoku Cloud
        if 'high' in df.columns and 'low' in df.columns:
            ichimoku = self.calculate_ichimoku(
                df['high'].values, df['low'].values, df['close'].values
            )
            df['ichimoku_tenkan'] = ichimoku['tenkan_sen']
            df['ichimoku_kijun'] = ichimoku['kijun_sen']
            df['ichimoku_senkou_a'] = ichimoku['senkou_a']
            df['ichimoku_senkou_b'] = ichimoku['senkou_b']
            df['ichimoku_chikou'] = ichimoku['chikou_span']

        return df

    def analyze_market(self, data: pd.DataFrame) -> Dict:
        """
        Piyasa verilerini analiz et ve sinyaller üret

        Args:
            data: OHLCV verileri içeren DataFrame

        Returns:
            Analiz sonuçları ve sinyaller
        """
        # Fiyat verilerini al
        close_prices = data['close'].values
        high_prices = data['high'].values if 'high' in data.columns else close_prices
        low_prices = data['low'].values if 'low' in data.columns else close_prices

        # Teknik göstergeleri hesapla
        rsi = self.calculate_rsi_advanced(close_prices)
        macd_line, signal_line, histogram = self.calculate_macd_advanced(close_prices)
        middle_band, upper_band, lower_band = self.calculate_bollinger_bands_advanced(close_prices)
        k_line, d_line = self.calculate_stochastic_oscillator(high_prices, low_prices, close_prices)
        trend_strength = self.calculate_trend_strength(close_prices)

        # Sinyal üretimi için eşik değerleri
        rsi_oversold = 40
        rsi_overbought = 60
        stoch_oversold = 30
        stoch_overbought = 70

        # Sinyal üret
        signals = np.zeros(len(close_prices))
        signal_strength = np.zeros(len(close_prices))

        for i in range(2, len(close_prices)):
            # RSI sinyalleri
            rsi_signal = 0
            rsi_change = rsi[i] - rsi[i-1]
            if (rsi[i] < rsi_oversold and rsi_change > 0) or (rsi[i] < 50 and rsi_change > 2):
                rsi_signal = 1
            elif (rsi[i] > rsi_overbought and rsi_change < 0) or (rsi[i] > 50 and rsi_change < -2):
                rsi_signal = -1

            # MACD sinyalleri
            macd_signal = 0
            hist_change = histogram[i] - histogram[i-1]
            if (macd_line[i] > signal_line[i] and macd_line[i-1] <= signal_line[i-1]) or \
               (histogram[i] > 0 and hist_change > 0) or \
               (histogram[i-1] < 0 and histogram[i] > 0):
                macd_signal = 1
            elif (macd_line[i] < signal_line[i] and macd_line[i-1] >= signal_line[i-1]) or \
                 (histogram[i] < 0 and hist_change < 0) or \
                 (histogram[i-1] > 0 and histogram[i] < 0):
                macd_signal = -1

            # Bollinger Bands sinyalleri
            bb_signal = 0
            bb_range = upper_band[i] - lower_band[i]
            price_position = (close_prices[i] - lower_band[i]) / bb_range if bb_range != 0 else 0.5
            price_change = close_prices[i] - close_prices[i-1]

            if (close_prices[i] < lower_band[i] and price_change > 0) or \
               (price_position < 0.3 and price_change > 0):
                bb_signal = 1
            elif (close_prices[i] > upper_band[i] and price_change < 0) or \
                 (price_position > 0.7 and price_change < 0):
                bb_signal = -1

            # Stochastic sinyalleri
            stoch_signal = 0
            k_change = k_line[i] - k_line[i-1]

            if (k_line[i] < stoch_oversold and k_change > 0) or \
               (k_line[i] > d_line[i] and k_line[i-1] <= d_line[i-1] and k_line[i] < 50):
                stoch_signal = 1
            elif (k_line[i] > stoch_overbought and k_change < 0) or \
                 (k_line[i] < d_line[i] and k_line[i-1] >= d_line[i-1] and k_line[i] > 50):
                stoch_signal = -1

            # Trend gücü sinyalleri
            trend_signal = 0
            if trend_strength[i] > 0.3 and trend_strength[i-1] <= 0.3:
                trend_signal = 1
            elif trend_strength[i] < -0.3 and trend_strength[i-1] >= -0.3:
                trend_signal = -1

            # Toplam sinyal hesapla (ağırlıklı ortalama)
            total_signal = (rsi_signal * 0.25 +
                           macd_signal * 0.25 +
                           bb_signal * 0.2 +
                           stoch_signal * 0.2 +
                           trend_signal * 0.1)

            # Sinyal gücünü hesapla
            signal_strength[i] = abs(total_signal)

            # Sinyal eşiği kontrolü
            if signal_strength[i] >= 0.2:
                signals[i] = np.sign(total_signal)

        # Sonuçları hazırla
        results = {
            'signals': signals,
            'signal_strength': signal_strength,
            'indicators': {
                'rsi': rsi,
                'macd': macd_line,
                'macd_signal': signal_line,
                'macd_histogram': histogram,
                'bb_middle': middle_band,
                'bb_upper': upper_band,
                'bb_lower': lower_band,
                'stoch_k': k_line,
                'stoch_d': d_line,
                'trend_strength': trend_strength
            }
        }

        return results

    def calculate_williams_r(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """
        Williams %R hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            close: Kapanış fiyatları
            period: Periyot

        Returns:
            Williams %R değerleri (-100 ile 0 arası)
        """
        highest_high = pd.Series(high).rolling(window=period).max()
        lowest_low = pd.Series(low).rolling(window=period).min()

        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))

        return williams_r.values

    def calculate_cci(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> np.ndarray:
        """
        Commodity Channel Index (CCI) hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            close: Kapanış fiyatları
            period: Periyot

        Returns:
            CCI değerleri
        """
        typical_price = (high + low + close) / 3
        sma_tp = pd.Series(typical_price).rolling(window=period).mean()

        # Mean deviation hesaplama
        mad = pd.Series(typical_price).rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean()))
        )

        cci = (typical_price - sma_tp) / (0.015 * mad)

        return cci.values

    def calculate_adx(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Average Directional Index (ADX) hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            close: Kapanış fiyatları
            period: Periyot

        Returns:
            ADX, +DI, -DI değerleri
        """
        # True Range hesaplama
        tr1 = high - low
        tr2 = np.abs(high - np.roll(close, 1))
        tr3 = np.abs(low - np.roll(close, 1))
        tr = np.maximum(tr1, np.maximum(tr2, tr3))

        # Directional Movement hesaplama
        dm_plus = np.where((high - np.roll(high, 1)) > (np.roll(low, 1) - low),
                          np.maximum(high - np.roll(high, 1), 0), 0)
        dm_minus = np.where((np.roll(low, 1) - low) > (high - np.roll(high, 1)),
                           np.maximum(np.roll(low, 1) - low, 0), 0)

        # Smoothed values
        atr = pd.Series(tr).rolling(window=period).mean()
        adm_plus = pd.Series(dm_plus).rolling(window=period).mean()
        adm_minus = pd.Series(dm_minus).rolling(window=period).mean()

        # Directional Indicators
        di_plus = 100 * (adm_plus / atr)
        di_minus = 100 * (adm_minus / atr)

        # ADX hesaplama
        dx = 100 * np.abs(di_plus - di_minus) / (di_plus + di_minus)
        adx = pd.Series(dx).rolling(window=period).mean()

        return adx.values, di_plus.values, di_minus.values

    def calculate_vwap(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """
        Volume Weighted Average Price (VWAP) hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            close: Kapanış fiyatları
            volume: Hacim verileri

        Returns:
            VWAP değerleri
        """
        typical_price = (high + low + close) / 3
        vwap = np.cumsum(typical_price * volume) / np.cumsum(volume)

        return vwap

    def calculate_parabolic_sar(self, high: np.ndarray, low: np.ndarray, af_start: float = 0.02, af_max: float = 0.2) -> np.ndarray:
        """
        Parabolic SAR hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            af_start: Başlangıç acceleration factor
            af_max: Maksimum acceleration factor

        Returns:
            Parabolic SAR değerleri
        """
        length = len(high)
        sar = np.zeros(length)
        trend = np.zeros(length)
        af = np.zeros(length)
        ep = np.zeros(length)

        # İlk değerler
        sar[0] = low[0]
        trend[0] = 1  # 1: uptrend, -1: downtrend
        af[0] = af_start
        ep[0] = high[0]

        for i in range(1, length):
            if trend[i-1] == 1:  # Uptrend
                sar[i] = sar[i-1] + af[i-1] * (ep[i-1] - sar[i-1])

                if low[i] <= sar[i]:  # Trend değişimi
                    trend[i] = -1
                    sar[i] = ep[i-1]
                    af[i] = af_start
                    ep[i] = low[i]
                else:
                    trend[i] = 1
                    if high[i] > ep[i-1]:
                        ep[i] = high[i]
                        af[i] = min(af[i-1] + af_start, af_max)
                    else:
                        ep[i] = ep[i-1]
                        af[i] = af[i-1]
            else:  # Downtrend
                sar[i] = sar[i-1] + af[i-1] * (ep[i-1] - sar[i-1])

                if high[i] >= sar[i]:  # Trend değişimi
                    trend[i] = 1
                    sar[i] = ep[i-1]
                    af[i] = af_start
                    ep[i] = high[i]
                else:
                    trend[i] = -1
                    if low[i] < ep[i-1]:
                        ep[i] = low[i]
                        af[i] = min(af[i-1] + af_start, af_max)
                    else:
                        ep[i] = ep[i-1]
                        af[i] = af[i-1]

        return sar

    def calculate_ichimoku(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Dict[str, np.ndarray]:
        """
        Ichimoku Cloud hesaplama

        Args:
            high: Yüksek fiyatlar
            low: Düşük fiyatlar
            close: Kapanış fiyatları

        Returns:
            Ichimoku bileşenleri (tenkan, kijun, senkou_a, senkou_b, chikou)
        """
        # Tenkan-sen (9 periyot)
        tenkan_high = pd.Series(high).rolling(window=9).max()
        tenkan_low = pd.Series(low).rolling(window=9).min()
        tenkan_sen = (tenkan_high + tenkan_low) / 2

        # Kijun-sen (26 periyot)
        kijun_high = pd.Series(high).rolling(window=26).max()
        kijun_low = pd.Series(low).rolling(window=26).min()
        kijun_sen = (kijun_high + kijun_low) / 2

        # Senkou Span A
        senkou_a = (tenkan_sen + kijun_sen) / 2

        # Senkou Span B (52 periyot)
        senkou_high = pd.Series(high).rolling(window=52).max()
        senkou_low = pd.Series(low).rolling(window=52).min()
        senkou_b = (senkou_high + senkou_low) / 2

        # Chikou Span
        chikou_span = pd.Series(close).shift(-26)

        return {
            'tenkan_sen': tenkan_sen.values,
            'kijun_sen': kijun_sen.values,
            'senkou_a': senkou_a.values,
            'senkou_b': senkou_b.values,
            'chikou_span': chikou_span.values
        }