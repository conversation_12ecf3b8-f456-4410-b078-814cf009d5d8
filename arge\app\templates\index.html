{% extends "base.html" %}

{% block title %}Kripto Simülasyon{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sol Panel - Kontroller -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Simülasyon Ayarları</h5>
                </div>
                <div class="card-body">
                    <form id="simulationForm">
                        <div class="mb-3">
                            <label for="symbol" class="form-label">Forex Çifti</label>
                            <select class="form-select" id="symbol" name="symbol">
                                <!-- Semboller JavaScript ile doldurulacak -->
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="timeframe" class="form-label">
                                Zaman Dilimi
                                <i class="fas fa-info-circle" data-bs-toggle="tooltip"
                                   title="15m-1h önerilir. 1m çok riskli, 4h+ y<PERSON><PERSON><PERSON> kar potansiyeli"></i>
                            </label>
                            <select class="form-select" id="timeframe" name="timeframe">
                                <!-- Zaman dilimleri JavaScript ile doldurulacak -->
                            </select>
                            <div class="form-text">
                                <small>
                                    <span class="text-success">✅ Önerilen:</span> 15m, 1h &nbsp;
                                    <span class="text-warning">⚠️ Dikkat:</span> 1m, 5m &nbsp;
                                    <a href="/strategy" class="text-decoration-none">Strateji ayarları →</a>
                                </small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="startDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="startDate" name="startDate">
                        </div>
                        
                        <div class="mb-3">
                            <label for="initialBalance" class="form-label">Başlangıç Bakiyesi (USD)</label>
                            <input type="number" class="form-control" id="initialBalance" name="initialBalance" value="10000">
                        </div>

                        <div class="mb-3">
                            <label for="leverage" class="form-label">Kaldıraç Oranı</label>
                            <select class="form-select" id="leverage" name="leverage">
                                <option value="1">1:1 (Kaldıraçsız)</option>
                                <option value="5">1:5</option>
                                <option value="10">1:10</option>
                                <option value="20">1:20</option>
                                <option value="50">1:50</option>
                                <option value="100" selected>1:100 (Önerilen)</option>
                                <option value="200">1:200</option>
                                <option value="500">1:500 (Yüksek Risk)</option>
                            </select>
                            <div class="form-text">
                                <small class="text-warning">⚠️ Yüksek kaldıraç yüksek risk demektir!</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="lotSize" class="form-label">Lot Büyüklüğü</label>
                            <select class="form-select" id="lotSize" name="lotSize">
                                <option value="0.01">0.01 (Mikro Lot)</option>
                                <option value="0.1" selected>0.1 (Mini Lot)</option>
                                <option value="1.0">1.0 (Standart Lot)</option>
                                <option value="2.0">2.0 Lot</option>
                                <option value="5.0">5.0 Lot</option>
                                <option value="10.0">10.0 Lot (Yüksek Risk)</option>
                            </select>
                            <div class="form-text">
                                <small>1 Standart Lot = 100,000 birim</small>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-2">Simülasyonu Başlat</button>
                        <a href="/bulk-analysis" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-chart-bar"></i> Toplu Analiz
                        </a>
                        <a href="/smart-optimization" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-brain"></i> Akıllı Optimizasyon
                        </a>
                        <button type="button" class="btn btn-success w-100" id="aiOptimizationBtn">
                            <i class="fas fa-robot"></i> Temiz AI Optimizasyon
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Performans Metrikleri -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">Performans Metrikleri</h5>
                </div>
                <div class="card-body">
                    <div id="performanceMetrics">
                        <!-- Metrikler JavaScript ile doldurulacak -->
                    </div>
                </div>
            </div>
            
            <!-- Analiz ve Karar Logları -->
            <div class="card mt-3">
                <div class="card-header">Analiz ve Karar Logları</div>
                <div class="card-body" id="simulationLog" style="max-height: 300px; overflow-y: auto; font-size: 0.8em;">
                    <!-- Log mesajları buraya yüklenecek -->
                    <p>Simülasyon başlatılmadı.</p>
                </div>
            </div>
        </div>
        
        <!-- Sağ Panel - Grafikler -->
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Fiyat Grafiği</h5>
                </div>
                <div class="card-body">
                    <div id="priceChart" style="height: 400px;"></div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">Performans Grafiği</h5>
                </div>
                <div class="card-body">
                    <div id="performanceChart" style="height: 400px;"></div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">İşlem Geçmişi</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="tradeHistory">
                            <thead>
                                <tr>
                                    <th>Tarih</th>
                                    <th>İşlem</th>
                                    <th>Fiyat</th>
                                    <th>Lot</th>
                                    <th>Pip</th>
                                    <th>Kar/Zarar</th>
                                    <th>Bakiye</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- İşlem geçmişi JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdn.plot.ly/plotly-2.29.1.min.js"></script>
<script src="{{ url_for('static', filename='js/main.js') }}"></script>
{% endblock %} 