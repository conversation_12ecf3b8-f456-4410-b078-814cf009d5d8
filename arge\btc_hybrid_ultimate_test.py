#!/usr/bin/env python3
"""
BTC/USDT Ultimate Hybrid Strategy Test
ML Regime Detection + Multi-timeframe + Aggressive Parameters
%10+ kar hede<PERSON> i<PERSON>in son deneme
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.data_fetcher import DataFetcher
from app.models.simulation import SimulationEngine
from app.models.ml_regime_detection import MLRegimeDetector
from app.models.multi_timeframe import MultiTimeframeAnalyzer
from app.models.config import Config

def test_ultimate_hybrid_strategy():
    """Ultimate Hybrid Strategy - Tüm yaklaşımları birleştiren son test"""
    
    print("🚀 BTC/USDT ULTIMATE HYBRID STRATEGY TEST")
    print("="*60)
    print("🎯 Hedef: %10+ kar")
    print("🔧 Yaklaşım: ML + Multi-timeframe + Aggressive")
    print("="*60)
    
    # Test parametreleri
    symbol = "BTC/USDT"
    start_date = "2025-01-01"
    initial_balance = 10000
    
    try:
        # Config ve veri çekme
        config = Config()
        data_fetcher = DataFetcher(config)
        df = data_fetcher.fetch_ohlcv(symbol, "1d", start_date)
        
        if df is None or len(df) < 50:
            print("❌ Yeterli veri bulunamadı!")
            return None
            
        print(f"✅ {len(df)} adet veri noktası alındı")
        
        # 1. ML Regime Detection
        print("\n🤖 1. ML Regime Detection...")
        ml_detector = MLRegimeDetector()
        df_with_regimes = ml_detector.detect_regimes(df)
        
        last_regime = df_with_regimes['regime_name'].iloc[-1]
        last_confidence = df_with_regimes['regime_confidence'].iloc[-1]
        
        print(f"   • Mevcut Rejim: {last_regime}")
        print(f"   • Güven Seviyesi: %{last_confidence*100:.1f}")
        
        # 2. Multi-timeframe Analysis
        print("\n🚀 2. Multi-timeframe Analysis...")
        mtf_analyzer = MultiTimeframeAnalyzer(config)
        mtf_result = mtf_analyzer.get_multi_timeframe_signal(symbol, start_date)
        
        print(f"   • MTF Sinyal: {mtf_result.get('signal', 0)}")
        print(f"   • MTF Güven: {mtf_result.get('confidence', 0)}")
        print(f"   • Öneri: {mtf_result.get('recommendation', 'HOLD')}")
        
        # 3. Ultimate Hybrid Parameters
        print("\n⚡ 3. Ultimate Hybrid Parameters...")
        
        # Base aggressive parameters
        ultimate_params = {
            'stopLoss': 2,          # %2 çok sıkı stop loss
            'takeProfit': 40,       # %40 çok yüksek hedef
            'trailingStop': 1,      # %1 çok sıkı trailing
            'basePosition': 50,     # %50 çok agresif pozisyon
            'maxPosition': 80,      # %80 maksimum pozisyon
            'minPosition': 30,      # %30 minimum pozisyon
            'rsiLower': 10,         # RSI çok geniş (neredeyse her durumda işlem)
            'rsiUpper': 99,         # RSI çok geniş
            'emaSpread': 0.001,     # EMA spread çok dar (neredeyse her durumda)
            'momentumScore': 0,     # Momentum skoru sıfır (her durumda işlem)
            'priceChange3': 0.01,   # %0.01 çok düşük
            'priceChange10': 0.01,  # %0.01 çok düşük
            'volatilityMax': 50,    # %50 çok yüksek volatilite kabul
            'adxMinimum': 1,        # ADX minimum çok düşük
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }
        
        # Regime-based adjustments
        if last_regime == "Bull_Market":
            ultimate_params.update({
                'takeProfit': 50,       # Boğa piyasasında daha yüksek
                'basePosition': 60,     # Daha agresif
                'stopLoss': 1.5         # Daha sıkı
            })
        elif last_regime == "Bear_Market":
            ultimate_params.update({
                'takeProfit': 25,       # Ayı piyasasında daha düşük
                'basePosition': 40,     # Daha konservatif
                'stopLoss': 3,          # Daha geniş
                'enableShort': True     # Short pozisyon aç
            })
        
        # Multi-timeframe adjustments
        if mtf_result.get('confidence', 0) > 80:
            ultimate_params['basePosition'] = min(90, ultimate_params['basePosition'] + 20)
            ultimate_params['takeProfit'] = ultimate_params['takeProfit'] + 10
        
        print(f"   • Take Profit: %{ultimate_params['takeProfit']}")
        print(f"   • Stop Loss: %{ultimate_params['stopLoss']}")
        print(f"   • Base Position: %{ultimate_params['basePosition']}")
        print(f"   • Momentum Score: {ultimate_params['momentumScore']}")
        
        # 4. Ultimate Simulation
        print("\n🔥 4. Ultimate Simulation Running...")
        simulation_engine = SimulationEngine(config, ultimate_params)
        results = simulation_engine.run_simulation(df, initial_balance)
        
        # 5. Results Analysis
        print("\n" + "="*60)
        print("🏆 ULTIMATE HYBRID STRATEGY RESULTS")
        print("="*60)
        
        print(f"💰 Başlangıç Bakiye: ${initial_balance:,.2f}")
        print(f"💰 Son Bakiye: ${results['final_balance']:,.2f}")
        print(f"📊 Toplam Kar: ${results['total_profit']:,.2f}")
        print(f"📈 Kar Oranı: %{results['profit_percentage']:.2f}")
        print(f"🔢 Toplam İşlem: {results['total_trades']}")
        print(f"✅ Kazanan İşlem: {results['winning_trades']}")
        print(f"❌ Kaybeden İşlem: {results['losing_trades']}")
        print(f"🎯 Kazanma Oranı: %{results['win_rate']:.2f}")
        print(f"📉 Maksimum Düşüş: %{results['max_drawdown']:.2f}")
        print(f"⚡ Sharpe Oranı: {results['sharpe_ratio']:.3f}")
        
        # Target analysis
        target_profit_pct = 10.0
        if results['profit_percentage'] >= target_profit_pct:
            print(f"\n🎉🎉🎉 BAŞARILI! %{target_profit_pct} HEDEFİNE ULAŞILDI! 🎉🎉🎉")
        else:
            needed_improvement = target_profit_pct - results['profit_percentage']
            print(f"\n⚠️  Hedef için %{needed_improvement:.2f} daha kar gerekli")
        
        # Buy & Hold comparison
        start_price = df['close'].iloc[0]
        end_price = df['close'].iloc[-1]
        buy_hold_return = ((end_price - start_price) / start_price) * 100
        
        print(f"\n📊 Buy & Hold Karşılaştırması:")
        print(f"   • Buy & Hold: %{buy_hold_return:.2f}")
        print(f"   • Ultimate Strategy: %{results['profit_percentage']:.2f}")
        
        if results['profit_percentage'] > buy_hold_return:
            print("✅ Ultimate Strategy Buy & Hold'dan daha iyi!")
        else:
            print("❌ Buy & Hold hala daha iyi")
        
        # Final recommendations
        print("\n" + "="*60)
        print("💡 SON ÖNERİLER VE ÇÖZÜMLER")
        print("="*60)
        
        if results['profit_percentage'] < target_profit_pct:
            print("🔧 %10 kar için önerilen yaklaşımlar:")
            print("\n1. 📈 LEVERAGE KULLANIMI:")
            print("   • 2x leverage ile %5 kar → %10 kar")
            print("   • Risk yönetimi ile kontrollü leverage")
            
            print("\n2. 🔄 DAHA KISA TIMEFRAME:")
            print("   • 4h veya 1h timeframe test et")
            print("   • Daha fazla işlem fırsatı")
            
            print("\n3. 💎 PORTFOLIO YAKLAŞIMI:")
            print("   • BTC + ETH + Top altcoinler")
            print("   • Diversifikasyon ile risk azaltma")
            
            print("\n4. 🎯 BREAKOUT STRATEJİSİ:")
            print("   • Support/Resistance kırılımları")
            print("   • Volume konfirmasyonu")
            
            print("\n5. 🤖 GELIŞMIŞ ML:")
            print("   • LSTM/GRU neural networks")
            print("   • Ensemble models")
            print("   • Feature engineering")
        
        return results
        
    except Exception as e:
        print(f"❌ Hata oluştu: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def final_summary():
    """Final özet ve öneriler"""
    print("\n" + "="*60)
    print("🏁 FINAL ÖZET VE SONUÇ")
    print("="*60)
    
    print("📊 Test Edilen Stratejiler:")
    print("   1. Normal Strategy: %2.92 kar")
    print("   2. Multi-timeframe: %3.48 kar")
    print("   3. ML Adaptif: %2.28 kar")
    print("   4. Ultimate Hybrid: Test edildi")
    
    print("\n🎯 %10 Kar Hedefi İçin Gerçekçi Yaklaşımlar:")
    print("   • 2x Leverage + Mevcut strateji")
    print("   • Daha kısa timeframe (4h/1h)")
    print("   • Portfolio diversifikasyonu")
    print("   • Breakout trading")
    print("   • Gelişmiş ML modelleri")
    
    print("\n💡 Önemli Bulgular:")
    print("   • Mevcut piyasa %84 sideways")
    print("   • Buy & Hold: %10.71 (en iyi)")
    print("   • Filtreler çok katı, az işlem")
    print("   • Risk yönetimi iyi çalışıyor")
    
    print("\n🚀 Sonraki Adımlar:")
    print("   1. Leverage entegrasyonu")
    print("   2. Kısa timeframe testleri")
    print("   3. Portfolio stratejisi")
    print("   4. Breakout detection")
    print("   5. Neural network modelleri")

if __name__ == "__main__":
    # Ultimate hybrid test
    results = test_ultimate_hybrid_strategy()
    
    # Final summary
    final_summary()
    
    print("\n🏁 Ultimate Hybrid Strategy test tamamlandı!")
    print("🎯 BTC/USDT %10 kar analizi bitti!")
