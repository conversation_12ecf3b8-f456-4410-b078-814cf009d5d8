{% extends "base.html" %}

{% block title %}Toplu Analiz - Kripto Simülasyon{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="fas fa-chart-bar"></i> Toplu Para Birimi Analizi
                <small class="text-muted">Tüm para birimlerinde aynı anda test edin</small>
            </h2>
        </div>
    </div>

    <!-- Ana<PERSON>z Ayarları -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-cogs"></i> Analiz Ayarları</h5>
                </div>
                <div class="card-body">
                    <form id="bulkAnalysisForm">
                        <div class="mb-3">
                            <label for="bulkTimeframe" class="form-label"><PERSON>aman <PERSON></label>
                            <select class="form-select" id="bulkTimeframe" name="timeframe">
                                <option value="15m">15 Dakika</option>
                                <option value="1h" selected>1 Saat</option>
                                <option value="4h">4 Saat</option>
                                <option value="1d">1 Gün</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="bulkStartDate" class="form-label">Başlangıç Tarihi</label>
                            <input type="date" class="form-control" id="bulkStartDate" name="startDate">
                        </div>
                        
                        <div class="mb-3">
                            <label for="bulkInitialBalance" class="form-label">Başlangıç Bakiyesi (USDT)</label>
                            <input type="number" class="form-control" id="bulkInitialBalance" name="initialBalance" value="10000">
                        </div>

                        <div class="mb-3">
                            <label for="symbolFilter" class="form-label">Para Birimi Filtresi</label>
                            <select class="form-select" id="symbolFilter" name="symbolFilter">
                                <option value="all">Tüm Para Birimleri</option>
                                <option value="major">Ana Para Birimleri (BTC, ETH, BNB, ADA, SOL)</option>
                                <option value="top20">Top 20 Para Birimi</option>
                                <option value="custom">Özel Seçim</option>
                            </select>
                        </div>

                        <div class="mb-3" id="customSymbolsDiv" style="display: none;">
                            <label for="customSymbols" class="form-label">Özel Para Birimleri (virgülle ayırın)</label>
                            <textarea class="form-control" id="customSymbols" rows="3" placeholder="BTC/USDT, ETH/USDT, ADA/USDT"></textarea>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="useCurrentStrategy" checked>
                                <label class="form-check-label" for="useCurrentStrategy">
                                    Mevcut Strateji Ayarlarını Kullan
                                </label>
                            </div>
                        </div>

                        <!-- Döngüsel Optimizasyon Ayarları -->
                        <div class="card border-warning mb-3">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0"><i class="fas fa-sync-alt"></i> Döngüsel AI Optimizasyon</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableCyclicOptimization">
                                        <label class="form-check-label" for="enableCyclicOptimization">
                                            <strong>Döngüsel Optimizasyonu Etkinleştir</strong>
                                        </label>
                                    </div>
                                    <small class="text-muted">AI sürekli iyileştirme yapacak</small>
                                </div>

                                <div class="mb-3">
                                    <label for="targetProfit" class="form-label">Hedef Kar Oranı (%)</label>
                                    <input type="number" class="form-control" id="targetProfit" value="3" min="1" max="10" step="0.1">
                                    <small class="text-muted">Bu hedefe ulaşana kadar optimizasyon devam eder (1d için %3 gerçekçi)</small>
                                </div>

                                <div class="mb-3">
                                    <label for="maxIterations" class="form-label">Maksimum İterasyon</label>
                                    <input type="number" class="form-control" id="maxIterations" value="15" min="5" max="50">
                                    <small class="text-muted">En fazla kaç iterasyon çalışsın (15-20 önerilir)</small>
                                </div>

                                <div class="mb-3">
                                    <label for="minImprovement" class="form-label">Minimum İyileştirme (%)</label>
                                    <input type="number" class="form-control" id="minImprovement" value="0.01" min="0.001" max="1" step="0.001">
                                    <small class="text-muted">Çok düşük değer önerilir (0.01% = her 100 iterasyonda 1% iyileştirme)</small>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" id="startBulkAnalysis">
                            <i class="fas fa-play"></i> Analizi Başlat
                        </button>

                        <button type="button" class="btn btn-warning w-100 mt-2" id="startCyclicOptimization" style="display: none;">
                            <i class="fas fa-sync-alt"></i> Döngüsel Optimizasyonu Başlat
                        </button>

                        <button type="button" class="btn btn-success w-100 mt-2" id="startAIOptimization">
                            <i class="fas fa-robot"></i> Temiz AI Optimizasyon
                        </button>
                    </form>
                </div>
            </div>

            <!-- İlerleme Durumu -->
            <div class="card mt-3" id="progressCard" style="display: none;">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-spinner fa-spin"></i> Analiz İlerliyor</h6>
                </div>
                <div class="card-body">
                    <div class="progress mb-2">
                        <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="progressText">Hazırlanıyor...</div>
                    <div id="currentSymbol" class="text-muted small"></div>
                </div>
            </div>

            <!-- Döngüsel Optimizasyon İlerlemesi -->
            <div class="card mt-3" id="cyclicProgressCard" style="display: none;">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="fas fa-sync-alt fa-spin"></i> Döngüsel Optimizasyon İlerliyor</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 id="currentIterationDisplay" class="text-primary">İterasyon 1</h5>
                                <small class="text-muted">Mevcut Aşama</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 id="targetProfitDisplay" class="text-success">Hedef: %5</h5>
                                <small class="text-muted">Kar Hedefi</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5 id="currentProfitDisplay" class="text-info">Mevcut: %0</h5>
                                <small class="text-muted">Mevcut Kar</small>
                            </div>
                        </div>
                    </div>

                    <div class="progress mb-2">
                        <div class="progress-bar bg-warning" id="cyclicProgressBar" role="progressbar" style="width: 0%"></div>
                    </div>

                    <div id="cyclicProgressText" class="mb-2">Döngüsel optimizasyon başlatılıyor...</div>
                    <div id="cyclicCurrentStep" class="text-muted small"></div>

                    <!-- Debug Bilgileri -->
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#debugInfo">
                            <i class="fas fa-bug"></i> Debug Bilgileri
                        </button>
                        <div class="collapse mt-2" id="debugInfo">
                            <div class="card card-body bg-light">
                                <pre id="debugLog" style="font-size: 12px; max-height: 200px; overflow-y: auto;"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sonuçlar -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line"></i> Analiz Sonuçları</h5>
                    <div>
                        <button class="btn btn-info btn-sm" id="loadTestData">
                            <i class="fas fa-upload"></i> Test Verisi Yükle
                        </button>
                        <button class="btn btn-light btn-sm" id="exportResults" style="display: none;">
                            <i class="fas fa-download"></i> Sonuçları İndir
                        </button>
                        <button class="btn btn-warning btn-sm" id="generateAIStrategy" style="display: none;">
                            <i class="fas fa-robot"></i> AI Strateji Oluştur
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="analysisResults">
                        <div class="text-center text-muted py-5">
                            <i class="fas fa-chart-bar fa-3x mb-3"></i>
                            <h5>Analiz Sonuçları Burada Görünecek</h5>
                            <p>Toplu analizi başlatmak için sol paneldeki formu doldurun.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Döngüsel Optimizasyon Geçmişi -->
    <div class="row mt-4" id="optimizationHistoryRow" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-history"></i> Döngüsel Optimizasyon Geçmişi</h5>
                </div>
                <div class="card-body">
                    <div id="optimizationChart" style="height: 300px;"></div>
                    <div class="table-responsive mt-3">
                        <table class="table table-sm" id="optimizationHistoryTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>İterasyon</th>
                                    <th>Ortalama Kar %</th>
                                    <th>Karlı Para Birimi</th>
                                    <th>Kazanma Oranı</th>
                                    <th>İşlem Sayısı</th>
                                    <th>İyileştirme</th>
                                    <th>Durum</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Optimizasyon geçmişi JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detaylı Sonuçlar Tablosu -->
    <div class="row mt-4" id="detailedResultsRow" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0"><i class="fas fa-table"></i> Detaylı Sonuçlar</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="detailedResultsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Sıra</th>
                                    <th>Para Birimi</th>
                                    <th>Toplam Kar/Zarar</th>
                                    <th>Kar %</th>
                                    <th>İşlem Sayısı</th>
                                    <th>Kazanma Oranı</th>
                                    <th>Sharpe Oranı</th>
                                    <th>Max Drawdown</th>
                                    <th>Durum</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Sonuçlar JavaScript ile doldurulacak -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Özet İstatistikler -->
    <div class="row mt-4" id="summaryStatsRow" style="display: none;">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">En İyi Performans</h5>
                    <h3 id="bestPerformance">-</h3>
                    <p id="bestSymbol" class="text-muted">-</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-danger">En Kötü Performans</h5>
                    <h3 id="worstPerformance">-</h3>
                    <p id="worstSymbol" class="text-muted">-</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">Ortalama Kazanma Oranı</h5>
                    <h3 id="avgWinRate">-</h3>
                    <p class="text-muted">Tüm para birimleri</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">Karlı Para Birimi</h5>
                    <h3 id="profitableCount">-</h3>
                    <p id="profitablePercentage" class="text-muted">-</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Strateji Modal -->
<div class="modal fade" id="aiStrategyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-robot"></i> AI Strateji Önerisi</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="aiStrategyContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Yükleniyor...</span>
                        </div>
                        <p class="mt-2">AI analiz sonuçlarını değerlendiriyor...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-primary" id="applyAIStrategy" style="display: none;">
                    <i class="fas fa-magic"></i> Stratejiyi Uygula
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.plot.ly/plotly-2.29.1.min.js"></script>
<script src="{{ url_for('static', filename='js/bulk_analysis.js') }}"></script>
{% endblock %}
