# Kripto Trading Simülasyon Uygulaması

Bu uygulama, kripto para trading stratejilerini test etmek için geliştirilmiş bir simülasyon platformudur.

## Özellikler

- **Teknik Analiz**: RSI, MACD, Bollinger Bands, Stochastic Oscillator
- **Gelişmiş Sinyal Üretimi**: Çoklu indikatör tabanlı sinyal sistemi
- **Piyasa Verisi**: Binance API entegrasyonu
- **Simülasyon**: Gerçek zamanlı trading simülasyonu
- **Web Arayüzü**: Flask tabanlı kullanıcı dostu arayüz

## Kurulum

### Otomatik Kurulum (Windows)
```bash
setup.bat
```

### Manuel <PERSON>rulum
```bash
# Sanal ortam oluştur
python -m venv venv

# Sanal ortamı aktifleştir
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Paketleri kur
pip install -r requirements.txt
```

## Kullanım

```bash
python run.py
```

Uygulama http://localhost:5000 adresinde çalışacaktır.

## Proje Yap<PERSON>sı

```
arge/
├── app/
│   ├── __init__.py          # Flask uygulaması
│   ├── controllers/         # Controller sınıfları
│   ├── models/             # Model sınıfları
│   │   ├── config.py       # Konfigürasyon
│   │   ├── indicators.py   # Teknik analiz
│   │   ├── data_fetcher.py # Veri çekme
│   │   ├── simulation.py   # Trading simülasyonu
│   │   └── strategy.py     # Trading stratejileri
│   ├── static/            # CSS, JS dosyaları
│   └── templates/         # HTML şablonları
├── requirements.txt       # Python bağımlılıkları
├── run.py                # Ana uygulama dosyası
├── setup.bat             # Kurulum scripti
└── README.md             # Bu dosya
```

## Teknik Detaylar

### Kullanılan Teknolojiler
- **Backend**: Flask (Python)
- **Frontend**: HTML, CSS, JavaScript
- **Veri İşleme**: Pandas, NumPy
- **API**: CCXT (Cryptocurrency Exchange Trading Library)
- **Görselleştirme**: Plotly

### Teknik İndikatörler
- **RSI**: Göreceli Güç Endeksi
- **MACD**: Moving Average Convergence Divergence
- **Bollinger Bands**: Volatilite bantları
- **Stochastic Oscillator**: Momentum osilatörü
- **Trend Strength**: Trend gücü analizi

## Konfigürasyon

Uygulama ayarları `app/models/config.py` dosyasında bulunur:

- **INITIAL_BALANCE**: Başlangıç bakiyesi (varsayılan: 10000)
- **LEVERAGE**: Kaldıraç oranı (varsayılan: 1)
- **COMMISSION_RATE**: Komisyon oranı (varsayılan: 0.001)
- **DEFAULT_SYMBOL**: Varsayılan trading çifti (varsayılan: BTCUSDT)

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
