// Akıllı Optimizasyon JavaScript

let optimizationRunning = false;
let currentIteration = 0;
let maxIterations = 20;
let bestResult = null;
let optimizationHistory = [];
let lastImprovementGeneration = 0;
let stagnationCount = 0;

// Sayfa yüklendiğinde
document.addEventListener('DOMContentLoaded', function() {
    initializeOptimization();
    
    // Event listeners
    document.getElementById('startOptimization').addEventListener('click', startOptimization);
    document.getElementById('applyBestStrategy').addEventListener('click', applyBestStrategy);
});

// Optimizasyonu başlat
function initializeOptimization() {
    updateProgressRing(0);
    logMessage('SISTEM', 'Akıllı optimizasyon sistemi başlatıldı', 'success');
}

// Optimizasyonu başlat
async function startOptimization() {
    if (optimizationRunning) {
        logMessage('UYARI', 'Optimizasyon zaten çalışıyor!', 'warning');
        return;
    }

    // Parametreleri al
    const targetSymbol = document.getElementById('targetSymbol').value;
    const targetTimeframe = document.getElementById('targetTimeframe').value;
    const optimizationTarget = document.getElementById('optimizationTarget').value;
    maxIterations = parseInt(document.getElementById('maxIterations').value);
    const targetProfit = parseFloat(document.getElementById('targetProfit').value);

    // Başlangıç durumu
    optimizationRunning = true;
    currentIteration = 0;
    bestResult = null;
    optimizationHistory = [];
    
    // UI güncelle
    document.getElementById('startOptimization').disabled = true;
    document.getElementById('startOptimization').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimizasyon Çalışıyor...';
    
    logMessage('BAŞLAT', `Akıllı optimizasyon başlatıldı - Hedef: ${targetSymbol} (${targetTimeframe})`, 'info');
    logMessage('HEDEF', `Optimizasyon hedefi: ${optimizationTarget}, Hedef kar: ${targetProfit}%`, 'info');

    try {
        // Genetik algoritma ile optimizasyon
        await runGeneticOptimization(targetSymbol, targetTimeframe, optimizationTarget, targetProfit);
    } catch (error) {
        logMessage('HATA', `Optimizasyon hatası: ${error.message}`, 'danger');
    } finally {
        // Bitirme durumu
        optimizationRunning = false;
        document.getElementById('startOptimization').disabled = false;
        document.getElementById('startOptimization').innerHTML = '<i class="fas fa-play"></i> Optimizasyonu Başlat';
        
        if (bestResult) {
            document.getElementById('applyBestStrategy').disabled = false;
            logMessage('TAMAMLANDI', `Optimizasyon tamamlandı! En iyi kar: ${bestResult.profit.toFixed(2)}%`, 'success');
        }
    }
}

// Genetik algoritma ile optimizasyon
async function runGeneticOptimization(symbol, timeframe, target, targetProfit) {
    const populationSize = 15;  // Daha büyük popülasyon
    const mutationRate = 0.8;   // Çok yüksek mutasyon
    const crossoverRate = 0.5;  // Düşük çaprazlama
    
    // İlk popülasyonu oluştur
    let population = generateInitialPopulation(populationSize);
    
    logMessage('GENETİK', `İlk popülasyon oluşturuldu (${populationSize} birey)`, 'info');
    
    for (let generation = 1; generation <= maxIterations; generation++) {
        currentIteration = generation;
        updateProgressRing((generation / maxIterations) * 100);
        
        logMessage('İTERASYON', `Nesil ${generation}/${maxIterations} başlatıldı`, 'primary');
        
        // Popülasyonu değerlendir
        const evaluatedPopulation = await evaluatePopulation(population, symbol, timeframe);
        
        // En iyi bireyi bul
        const generationBest = evaluatedPopulation.reduce((best, current) => 
            current.fitness > best.fitness ? current : best
        );
        
        // Global en iyiyi güncelle
        if (!bestResult || generationBest.fitness > bestResult.fitness) {
            bestResult = {
                iteration: generation,
                parameters: { ...generationBest.parameters },
                profit: generationBest.results.profit_percentage,
                winRate: generationBest.results.win_rate,
                trades: generationBest.results.total_trades,
                sharpe: generationBest.results.sharpe_ratio,
                drawdown: generationBest.results.max_drawdown,
                fitness: generationBest.fitness
            };

            lastImprovementGeneration = generation;
            stagnationCount = 0;
            updateBestMetrics();
            updateBestParameters();
            logMessage('YENİ REKOR', `Yeni en iyi sonuç! Kar: ${bestResult.profit.toFixed(2)}%`, 'success');
        } else {
            stagnationCount++;
            if (stagnationCount >= 3) {
                logMessage('UYARI', `${stagnationCount} nesil gelişme yok! Mutasyon artırılıyor...`, 'warning');
                mutationRate = Math.min(0.9, mutationRate + 0.1);
            }
        }
        
        // Geçmişe ekle
        addToHistory(generation, generationBest);
        
        // Hedef kara ulaştık mı?
        if (generationBest.results.profit_percentage >= targetProfit) {
            logMessage('HEDEF', `Hedef kar (${targetProfit}%) ulaşıldı! Optimizasyon durduruluyor.`, 'success');
            break;
        }
        
        // Yeni nesil oluştur
        if (generation < maxIterations) {
            population = createNextGeneration(evaluatedPopulation, populationSize, crossoverRate, mutationRate);
            logMessage('EVRİM', `Yeni nesil oluşturuldu (Çaprazlama: ${crossoverRate}, Mutasyon: ${mutationRate})`, 'info');
        }
        
        // Kısa bekleme (UI güncellemesi için)
        await new Promise(resolve => setTimeout(resolve, 100));
    }
}

// İlk popülasyonu oluştur
function generateInitialPopulation(size) {
    const population = [];
    
    for (let i = 0; i < size; i++) {
        population.push({
            parameters: generateRandomParameters(),
            fitness: 0,
            results: null
        });
    }
    
    return population;
}

// Rastgele parametreler oluştur
function generateRandomParameters() {
    return {
        stopLoss: randomFloat(1.5, 8.0),
        takeProfit: randomFloat(3.0, 25.0),
        trailingStop: randomFloat(1.0, 5.0),
        basePosition: randomFloat(5.0, 30.0),
        maxPosition: randomFloat(15.0, 40.0),
        minPosition: randomFloat(3.0, 15.0),
        rsiLower: randomInt(20, 50),
        rsiUpper: randomInt(60, 90),
        emaSpread: randomFloat(0.1, 1.0),
        momentumScore: randomInt(3, 11),
        priceChange3: randomFloat(0.2, 2.0),
        priceChange10: randomFloat(0.5, 3.0),
        volatilityMax: randomFloat(2.0, 10.0),
        commission: 0.01,
        enableShort: Math.random() > 0.5,
        dynamicTrailing: true,
        adxMinimum: randomInt(15, 30),
        williamsRThreshold: randomInt(-90, -60),
        cciRange: randomInt(100, 300),
        useVWAP: Math.random() > 0.3,
        useParabolicSAR: Math.random() > 0.2,
        useIchimoku: Math.random() > 0.4,
        stochasticThreshold: `${randomInt(15, 35)},${randomInt(65, 85)}`
    };
}

// Popülasyonu değerlendir
async function evaluatePopulation(population, symbol, timeframe) {
    const evaluatedPopulation = [];
    
    for (let i = 0; i < population.length; i++) {
        const individual = population[i];
        
        try {
            // Simülasyon çalıştır
            const results = await runSimulationWithParameters(symbol, timeframe, individual.parameters);
            
            // Fitness hesapla
            const fitness = calculateFitness(results, document.getElementById('optimizationTarget').value);
            
            evaluatedPopulation.push({
                ...individual,
                results: results,
                fitness: fitness
            });
            
            logMessage('DEĞERLENDİRME', `Birey ${i+1}: Kar ${results.profit_percentage.toFixed(2)}%, Fitness: ${fitness.toFixed(2)}`, 'secondary');
            
        } catch (error) {
            logMessage('HATA', `Birey ${i+1} değerlendirme hatası: ${error.message}`, 'warning');
            evaluatedPopulation.push({
                ...individual,
                results: { profit_percentage: -100, win_rate: 0, total_trades: 0, sharpe_ratio: -10, max_drawdown: -1 },
                fitness: -1000
            });
        }
    }
    
    return evaluatedPopulation;
}

// Simülasyon çalıştır
async function runSimulationWithParameters(symbol, timeframe, parameters) {
    const response = await fetch('/api/run-simulation', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            symbol: symbol,
            timeframe: timeframe,
            start_date: '2025-01-01',
            initial_balance: 10000,
            leverage: 100,
            lot_size: 0.1,
            strategy_settings: parameters
        })
    });
    
    if (!response.ok) {
        throw new Error(`Simülasyon hatası: ${response.status}`);
    }
    
    return await response.json();
}

// Fitness hesapla
function calculateFitness(results, target) {
    const profit = results.profit_percentage || 0;
    const winRate = results.win_rate || 0;
    const trades = results.total_trades || 0;
    const sharpe = results.sharpe_ratio || 0;
    const drawdown = Math.abs(results.max_drawdown || 0);
    
    let fitness = 0;
    
    switch (target) {
        case 'profit':
            fitness = profit * 2 + winRate * 0.5 + Math.min(trades, 50) * 0.1 - drawdown * 100;
            break;
        case 'winrate':
            fitness = winRate * 2 + profit * 0.5 + Math.min(trades, 50) * 0.1 - drawdown * 50;
            break;
        case 'sharpe':
            fitness = sharpe * 50 + profit * 0.5 + winRate * 0.3 - drawdown * 30;
            break;
        case 'balanced':
            fitness = profit + winRate + sharpe * 10 + Math.min(trades, 50) * 0.2 - drawdown * 50;
            break;
    }
    
    // Negatif kar için ceza
    if (profit < 0) fitness -= Math.abs(profit) * 2;
    
    // Çok az işlem için ceza
    if (trades < 3) fitness -= 50;
    
    return fitness;
}

// Yardımcı fonksiyonlar
function randomFloat(min, max) {
    return Math.random() * (max - min) + min;
}

function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Progress ring güncelle
function updateProgressRing(percent) {
    const circle = document.getElementById('progressCircle');
    const circumference = 2 * Math.PI * 52;
    const offset = circumference - (percent / 100) * circumference;
    
    circle.style.strokeDasharray = circumference;
    circle.style.strokeDashoffset = offset;
    
    document.getElementById('progressPercent').textContent = Math.round(percent) + '%';
    document.getElementById('currentIteration').textContent = currentIteration;
    
    if (percent === 100) {
        document.getElementById('progressText').textContent = 'Tamamlandı';
    } else if (percent > 0) {
        document.getElementById('progressText').textContent = 'Çalışıyor...';
    } else {
        document.getElementById('progressText').textContent = 'Hazır';
    }
}

// En iyi metrikleri güncelle
function updateBestMetrics() {
    if (bestResult) {
        document.getElementById('bestProfit').textContent = bestResult.profit.toFixed(2) + '%';
        document.getElementById('bestWinRate').textContent = bestResult.winRate.toFixed(1) + '%';
        document.getElementById('bestTrades').textContent = bestResult.trades;
        
        document.getElementById('statusMessage').innerHTML = 
            `<strong>En İyi Sonuç:</strong> İterasyon ${bestResult.iteration} - ${bestResult.profit.toFixed(2)}% kar`;
        document.getElementById('statusMessage').className = 'alert alert-success';
    }
}

// En iyi parametreleri göster
function updateBestParameters() {
    if (bestResult) {
        const params = bestResult.parameters;
        const html = `
            <small>
                <strong>Stop Loss:</strong> ${params.stopLoss.toFixed(1)}%<br>
                <strong>Take Profit:</strong> ${params.takeProfit.toFixed(1)}%<br>
                <strong>RSI:</strong> ${params.rsiLower}-${params.rsiUpper}<br>
                <strong>Pozisyon:</strong> ${params.basePosition.toFixed(1)}%<br>
                <strong>Momentum:</strong> ${params.momentumScore}/12<br>
                <strong>Short:</strong> ${params.enableShort ? 'Evet' : 'Hayır'}
            </small>
        `;
        document.getElementById('bestParameters').innerHTML = html;
    }
}

// Geçmişe ekle
function addToHistory(iteration, best) {
    optimizationHistory.push({
        iteration: iteration,
        profit: best.results.profit_percentage,
        winRate: best.results.win_rate,
        trades: best.results.total_trades,
        sharpe: best.results.sharpe_ratio,
        drawdown: best.results.max_drawdown,
        fitness: best.fitness,
        parameters: best.parameters
    });
    
    updateHistoryTable();
}

// Geçmiş tablosunu güncelle
function updateHistoryTable() {
    const tbody = document.getElementById('optimizationHistory');
    tbody.innerHTML = '';
    
    optimizationHistory.forEach((item, index) => {
        const row = document.createElement('tr');
        row.className = 'iteration-row';
        
        if (bestResult && item.iteration === bestResult.iteration) {
            row.className += ' best-iteration';
        }
        
        const statusClass = item.profit > 0 ? 'success' : 'danger';
        const statusText = item.profit > 0 ? 'Karlı' : 'Zararlı';
        
        row.innerHTML = `
            <td>${item.iteration}</td>
            <td>${item.profit.toFixed(2)}%</td>
            <td>${item.winRate.toFixed(1)}%</td>
            <td>${item.trades}</td>
            <td>${item.sharpe.toFixed(2)}</td>
            <td>${(item.drawdown * 100).toFixed(2)}%</td>
            <td><span class="badge bg-${statusClass} status-badge">${statusText}</span></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewParameters(${index})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Parametreleri görüntüle
function viewParameters(index) {
    const item = optimizationHistory[index];
    const params = item.parameters;
    
    const content = `
        <h6>İterasyon ${item.iteration} Parametreleri</h6>
        <div class="row">
            <div class="col-6">
                <small>
                    <strong>Stop Loss:</strong> ${params.stopLoss.toFixed(2)}%<br>
                    <strong>Take Profit:</strong> ${params.takeProfit.toFixed(2)}%<br>
                    <strong>Trailing Stop:</strong> ${params.trailingStop.toFixed(2)}%<br>
                    <strong>Base Position:</strong> ${params.basePosition.toFixed(1)}%<br>
                    <strong>Max Position:</strong> ${params.maxPosition.toFixed(1)}%<br>
                    <strong>Min Position:</strong> ${params.minPosition.toFixed(1)}%<br>
                </small>
            </div>
            <div class="col-6">
                <small>
                    <strong>RSI Lower:</strong> ${params.rsiLower}<br>
                    <strong>RSI Upper:</strong> ${params.rsiUpper}<br>
                    <strong>EMA Spread:</strong> ${params.emaSpread.toFixed(2)}<br>
                    <strong>Momentum Score:</strong> ${params.momentumScore}/12<br>
                    <strong>Enable Short:</strong> ${params.enableShort ? 'Evet' : 'Hayır'}<br>
                    <strong>ADX Minimum:</strong> ${params.adxMinimum}<br>
                </small>
            </div>
        </div>
    `;
    
    // Modal göster (basit alert yerine)
    alert(`İterasyon ${item.iteration}\nKar: ${item.profit.toFixed(2)}%\nKazanma Oranı: ${item.winRate.toFixed(1)}%\nİşlem: ${item.trades}\nSharpe: ${item.sharpe.toFixed(2)}`);
}

// En iyi stratejiyi uygula
async function applyBestStrategy() {
    if (!bestResult) {
        logMessage('HATA', 'Henüz en iyi strateji bulunamadı!', 'danger');
        return;
    }
    
    try {
        const response = await fetch('/api/save-strategy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(bestResult.parameters)
        });
        
        if (response.ok) {
            logMessage('BAŞARILI', 'En iyi strateji kaydedildi ve uygulandı!', 'success');
            alert('En iyi strateji başarıyla kaydedildi!\n\nStrateji sayfasından detayları görüntüleyebilirsiniz.');
        } else {
            throw new Error('Strateji kaydetme hatası');
        }
    } catch (error) {
        logMessage('HATA', `Strateji kaydetme hatası: ${error.message}`, 'danger');
    }
}

// Yeni nesil oluştur (Genetik Algoritma)
function createNextGeneration(population, size, crossoverRate, mutationRate) {
    // Fitness'a göre sırala
    population.sort((a, b) => b.fitness - a.fitness);

    // En iyi %10'u seç (düşük elitizm)
    const eliteSize = Math.floor(size * 0.1);
    const newGeneration = population.slice(0, eliteSize).map(individual => ({
        parameters: { ...individual.parameters },
        fitness: 0,
        results: null
    }));

    // Kalan bireyleri çaprazlama ve mutasyon ile oluştur
    while (newGeneration.length < size) {
        // Ebeveyn seçimi (turnuva seçimi)
        const parent1 = tournamentSelection(population, 3);
        const parent2 = tournamentSelection(population, 3);

        let child;
        if (Math.random() < crossoverRate) {
            // Çaprazlama
            child = crossover(parent1, parent2);
        } else {
            // Ebeveynlerden birini kopyala
            child = { ...parent1 };
        }

        // Mutasyon
        if (Math.random() < mutationRate) {
            child = mutate(child);
        }

        newGeneration.push({
            parameters: child.parameters,
            fitness: 0,
            results: null
        });
    }

    // %20 tamamen rastgele birey ekle (çeşitlilik için)
    const randomCount = Math.floor(size * 0.2);
    for (let i = 0; i < randomCount && newGeneration.length < size; i++) {
        newGeneration.push({
            parameters: generateRandomParameters(),
            fitness: 0,
            results: null
        });
    }

    return newGeneration;
}

// Turnuva seçimi
function tournamentSelection(population, tournamentSize) {
    const tournament = [];
    for (let i = 0; i < tournamentSize; i++) {
        const randomIndex = Math.floor(Math.random() * population.length);
        tournament.push(population[randomIndex]);
    }

    return tournament.reduce((best, current) =>
        current.fitness > best.fitness ? current : best
    );
}

// Çaprazlama (Uniform Crossover)
function crossover(parent1, parent2) {
    const child = { parameters: {} };

    Object.keys(parent1.parameters).forEach(key => {
        if (Math.random() < 0.5) {
            child.parameters[key] = parent1.parameters[key];
        } else {
            child.parameters[key] = parent2.parameters[key];
        }
    });

    return child;
}

// Mutasyon
function mutate(individual) {
    const mutated = { parameters: { ...individual.parameters } };

    // Her parametreyi %60 olasılıkla mutasyona uğrat (çok yüksek)
    Object.keys(mutated.parameters).forEach(key => {
        if (Math.random() < 0.6) {
            switch (key) {
                case 'stopLoss':
                    mutated.parameters[key] = randomFloat(1.5, 8.0);
                    break;
                case 'takeProfit':
                    mutated.parameters[key] = randomFloat(3.0, 25.0);
                    break;
                case 'trailingStop':
                    mutated.parameters[key] = randomFloat(1.0, 5.0);
                    break;
                case 'basePosition':
                    mutated.parameters[key] = randomFloat(5.0, 30.0);
                    break;
                case 'maxPosition':
                    mutated.parameters[key] = randomFloat(15.0, 40.0);
                    break;
                case 'minPosition':
                    mutated.parameters[key] = randomFloat(3.0, 15.0);
                    break;
                case 'rsiLower':
                    mutated.parameters[key] = randomInt(20, 50);
                    break;
                case 'rsiUpper':
                    mutated.parameters[key] = randomInt(60, 90);
                    break;
                case 'emaSpread':
                    mutated.parameters[key] = randomFloat(0.1, 1.0);
                    break;
                case 'momentumScore':
                    mutated.parameters[key] = randomInt(3, 11);
                    break;
                case 'priceChange3':
                    mutated.parameters[key] = randomFloat(0.2, 2.0);
                    break;
                case 'priceChange10':
                    mutated.parameters[key] = randomFloat(0.5, 3.0);
                    break;
                case 'volatilityMax':
                    mutated.parameters[key] = randomFloat(2.0, 10.0);
                    break;
                case 'adxMinimum':
                    mutated.parameters[key] = randomInt(15, 30);
                    break;
                case 'williamsRThreshold':
                    mutated.parameters[key] = randomInt(-90, -60);
                    break;
                case 'cciRange':
                    mutated.parameters[key] = randomInt(100, 300);
                    break;
                case 'enableShort':
                    mutated.parameters[key] = Math.random() > 0.5;
                    break;
                case 'useVWAP':
                    mutated.parameters[key] = Math.random() > 0.3;
                    break;
                case 'useParabolicSAR':
                    mutated.parameters[key] = Math.random() > 0.2;
                    break;
                case 'useIchimoku':
                    mutated.parameters[key] = Math.random() > 0.4;
                    break;
                case 'stochasticThreshold':
                    mutated.parameters[key] = `${randomInt(15, 35)},${randomInt(65, 85)}`;
                    break;
            }
        }
    });

    return mutated;
}

// Log mesajı ekle
function logMessage(type, message, level = 'info') {
    const logDiv = document.getElementById('optimizationLog');
    const timestamp = new Date().toLocaleTimeString();

    const levelColors = {
        'success': 'text-success',
        'info': 'text-info',
        'warning': 'text-warning',
        'danger': 'text-danger',
        'primary': 'text-primary',
        'secondary': 'text-secondary'
    };

    const colorClass = levelColors[level] || 'text-light';

    const logEntry = `<span class="text-muted">[${timestamp}]</span> <span class="${colorClass}">[${type}]</span> ${message}<br>`;
    logDiv.innerHTML += logEntry;

    // Scroll to bottom
    logDiv.scrollTop = logDiv.scrollHeight;
}
