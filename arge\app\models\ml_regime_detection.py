#!/usr/bin/env python3
"""
Machine Learning Tabanlı Regime Detection
BTC/USDT için piyasa rejimi tespiti ve adaptif strateji
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class MLRegimeDetector:
    """
    Machine Learning tabanlı piyasa rejimi tespit sistemi
    """
    
    def __init__(self):
        self.is_trained = False
        self.regime_names = {
            0: "Bull_Market",      # Yükseliş piyasası
            1: "Bear_Market",      # Düşüş piyasası
            2: "Sideways_Market",  # Yatay piyasa
            3: "High_Volatility"   # Yüksek volatilite
        }
        
    def extract_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Piyasa rejimi tespiti için özellik çıkarımı
        """
        features = pd.DataFrame(index=df.index)
        
        # Fiyat bazlı özellikler
        features['returns'] = df['close'].pct_change()
        features['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        features['price_momentum_5'] = df['close'].pct_change(5)
        features['price_momentum_20'] = df['close'].pct_change(20)
        
        # Volatilite özellikleri
        features['volatility_5'] = features['returns'].rolling(5).std()
        features['volatility_20'] = features['returns'].rolling(20).std()
        features['volatility_ratio'] = features['volatility_5'] / features['volatility_20']
        
        # Trend özellikleri
        features['sma_5'] = df['close'].rolling(5).mean()
        features['sma_20'] = df['close'].rolling(20).mean()
        features['sma_50'] = df['close'].rolling(50).mean()
        features['trend_5_20'] = (features['sma_5'] - features['sma_20']) / features['sma_20']
        features['trend_20_50'] = (features['sma_20'] - features['sma_50']) / features['sma_50']
        
        # Momentum özellikleri
        features['rsi'] = self._calculate_rsi(df['close'])
        features['macd'], features['macd_signal'] = self._calculate_macd(df['close'])
        features['macd_histogram'] = features['macd'] - features['macd_signal']
        
        # Volume özellikleri (varsa)
        if 'volume' in df.columns:
            features['volume_sma'] = df['volume'].rolling(20).mean()
            features['volume_ratio'] = df['volume'] / features['volume_sma']
            features['price_volume'] = features['returns'] * features['volume_ratio']
        else:
            features['volume_ratio'] = 1
            features['price_volume'] = features['returns']
            
        # Bollinger Bands
        bb_period = 20
        bb_std = 2
        bb_sma = df['close'].rolling(bb_period).mean()
        bb_std_dev = df['close'].rolling(bb_period).std()
        features['bb_upper'] = bb_sma + (bb_std_dev * bb_std)
        features['bb_lower'] = bb_sma - (bb_std_dev * bb_std)
        features['bb_position'] = (df['close'] - bb_sma) / (bb_std_dev * bb_std)
        
        # Drawdown
        rolling_max = df['close'].expanding().max()
        features['drawdown'] = (df['close'] - rolling_max) / rolling_max
        
        # NaN değerleri temizle
        features = features.fillna(method='bfill').fillna(0)
        
        return features
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """RSI hesaplama"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """MACD hesaplama"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def detect_regimes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Piyasa rejimlerini tespit et
        """
        # Özellikleri çıkar
        features = self.extract_features(df)
        
        # Özellik seçimi (en önemli olanlar)
        feature_cols = [
            'returns', 'volatility_20', 'trend_5_20', 'trend_20_50',
            'rsi', 'macd_histogram', 'bb_position', 'drawdown',
            'price_momentum_20', 'volatility_ratio'
        ]
        
        X = features[feature_cols].dropna()
        
        if len(X) < 50:  # Yeterli veri yoksa
            df['regime'] = 2  # Sideways olarak işaretle
            df['regime_name'] = 'Sideways_Market'
            df['regime_confidence'] = 0.5
            return df
        
        # Veriyi normalize et (basit normalizasyon)
        X_scaled = self._normalize_features(X)

        # Basit clustering ile rejimleri tespit et
        regime_labels = self._simple_clustering(X_scaled)
        
        # Rejimleri yorumla
        regime_interpretation = self._interpret_regimes(X, regime_labels)
        
        # Sonuçları DataFrame'e ekle
        df = df.copy()
        df['regime'] = np.nan
        df['regime_name'] = 'Unknown'
        df['regime_confidence'] = 0.0
        
        # Rejimleri ata
        for i, idx in enumerate(X.index):
            if i < len(regime_labels):
                regime_id = regime_labels[i]
                df.loc[idx, 'regime'] = regime_id
                df.loc[idx, 'regime_name'] = regime_interpretation.get(regime_id, 'Unknown')
                df.loc[idx, 'regime_confidence'] = self._calculate_confidence(X_scaled[i])
        
        # NaN değerleri doldur
        df['regime'] = df['regime'].fillna(method='ffill').fillna(2)
        df['regime_name'] = df['regime_name'].fillna('Sideways_Market')
        df['regime_confidence'] = df['regime_confidence'].fillna(0.5)
        
        return df
    
    def _interpret_regimes(self, features: pd.DataFrame, labels: np.ndarray) -> Dict[int, str]:
        """
        K-means sonuçlarını piyasa rejimlerine çevir
        """
        interpretation = {}
        
        for regime_id in np.unique(labels):
            regime_mask = labels == regime_id
            regime_features = features[regime_mask]
            
            # Ortalama özellikler
            avg_returns = regime_features['returns'].mean()
            avg_volatility = regime_features['volatility_20'].mean()
            avg_trend = regime_features['trend_20_50'].mean()
            avg_drawdown = regime_features['drawdown'].mean()
            
            # Rejim yorumlama
            if avg_returns > 0.001 and avg_trend > 0.02 and avg_drawdown > -0.05:
                interpretation[regime_id] = "Bull_Market"
            elif avg_returns < -0.001 and avg_trend < -0.02 and avg_drawdown < -0.1:
                interpretation[regime_id] = "Bear_Market"
            elif avg_volatility > regime_features['volatility_20'].quantile(0.75):
                interpretation[regime_id] = "High_Volatility"
            else:
                interpretation[regime_id] = "Sideways_Market"
        
        return interpretation
    
    def _calculate_confidence(self, features: np.ndarray, regime_id: int) -> float:
        """
        Rejim tespiti güven skoru hesapla
        """
        try:
            # Cluster merkezine uzaklık
            center = self.kmeans.cluster_centers_[regime_id]
            distance = np.linalg.norm(features - center)
            
            # Güven skoru (0-1 arası)
            max_distance = np.max([np.linalg.norm(c - center) for c in self.kmeans.cluster_centers_])
            confidence = max(0, 1 - (distance / max_distance)) if max_distance > 0 else 0.5
            
            return confidence
        except:
            return 0.5
    
    def get_regime_strategy_params(self, current_regime: str, confidence: float) -> Dict:
        """
        Rejime göre strateji parametreleri öner
        """
        base_params = {
            'stopLoss': 4,
            'takeProfit': 20,
            'trailingStop': 2.5,
            'basePosition': 25,
            'maxPosition': 40,
            'minPosition': 15,
            'rsiLower': 25,
            'rsiUpper': 95,
            'emaSpread': 0.05,
            'momentumScore': 2,
            'commission': 0.01,
            'enableShort': False,
            'dynamicTrailing': True
        }
        
        # Rejime göre ayarlamalar
        if current_regime == "Bull_Market":
            # Boğa piyasasında agresif
            base_params.update({
                'takeProfit': 30,      # Daha yüksek hedef
                'basePosition': 35,    # Daha büyük pozisyon
                'maxPosition': 50,     # Maksimum pozisyon
                'momentumScore': 1,    # Daha fazla işlem
                'trailingStop': 2      # Daha sıkı takip
            })
            
        elif current_regime == "Bear_Market":
            # Ayı piyasasında konservatif
            base_params.update({
                'takeProfit': 15,      # Düşük hedef
                'stopLoss': 3,         # Sıkı stop loss
                'basePosition': 15,    # Küçük pozisyon
                'maxPosition': 25,     # Düşük maksimum
                'momentumScore': 4,    # Az işlem
                'enableShort': True    # Short pozisyon aç
            })
            
        elif current_regime == "High_Volatility":
            # Yüksek volatilitede hızlı
            base_params.update({
                'takeProfit': 25,      # Orta hedef
                'stopLoss': 2,         # Çok sıkı stop
                'trailingStop': 1.5,   # Çok sıkı takip
                'basePosition': 20,    # Orta pozisyon
                'momentumScore': 3     # Orta işlem
            })
            
        else:  # Sideways_Market
            # Yatay piyasada range trading
            base_params.update({
                'takeProfit': 15,      # Düşük hedef
                'stopLoss': 5,         # Geniş stop
                'basePosition': 30,    # Büyük pozisyon
                'momentumScore': 2,    # Fazla işlem
                'rsiLower': 30,        # RSI range
                'rsiUpper': 70
            })
        
        # Güven seviyesine göre ayarlama
        confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5-1.0 arası
        base_params['basePosition'] = int(base_params['basePosition'] * confidence_multiplier)
        base_params['maxPosition'] = int(base_params['maxPosition'] * confidence_multiplier)
        
        return base_params
    
    def analyze_regime_performance(self, df: pd.DataFrame) -> Dict:
        """
        Rejim bazlı performans analizi
        """
        if 'regime_name' not in df.columns:
            return {}
        
        analysis = {}
        
        for regime in df['regime_name'].unique():
            regime_data = df[df['regime_name'] == regime]
            
            if len(regime_data) > 1:
                returns = regime_data['close'].pct_change().dropna()
                
                analysis[regime] = {
                    'count': len(regime_data),
                    'avg_return': returns.mean(),
                    'volatility': returns.std(),
                    'sharpe': returns.mean() / returns.std() if returns.std() > 0 else 0,
                    'max_drawdown': ((regime_data['close'] / regime_data['close'].expanding().max()) - 1).min(),
                    'duration_days': len(regime_data)
                }
        
        return analysis

    def _normalize_features(self, X: pd.DataFrame) -> np.ndarray:
        """
        Basit feature normalizasyonu (z-score)
        """
        X_normalized = X.copy()
        for col in X.columns:
            mean_val = X[col].mean()
            std_val = X[col].std()
            if std_val > 0:
                X_normalized[col] = (X[col] - mean_val) / std_val
            else:
                X_normalized[col] = 0

        return X_normalized.values

    def _simple_clustering(self, X_scaled: np.ndarray) -> np.ndarray:
        """
        Basit kural tabanlı clustering
        Sklearn olmadan regime detection
        """
        n_samples = len(X_scaled)
        labels = np.zeros(n_samples)

        # Feature indeksleri (sırasıyla)
        # 0: returns, 1: volatility_20, 2: trend_5_20, 3: trend_20_50
        # 4: rsi, 5: macd_histogram, 6: bb_position, 7: drawdown
        # 8: price_momentum_20, 9: volatility_ratio

        for i in range(n_samples):
            features = X_scaled[i]

            returns = features[0] if len(features) > 0 else 0
            volatility = features[1] if len(features) > 1 else 0
            trend_short = features[2] if len(features) > 2 else 0
            trend_long = features[3] if len(features) > 3 else 0
            rsi = features[4] if len(features) > 4 else 0
            macd = features[5] if len(features) > 5 else 0
            bb_pos = features[6] if len(features) > 6 else 0
            drawdown = features[7] if len(features) > 7 else 0
            momentum = features[8] if len(features) > 8 else 0

            # Kural tabanlı sınıflandırma

            # Bull Market: Pozitif trend, düşük drawdown, pozitif momentum
            if (trend_short > 0.5 and trend_long > 0.3 and
                drawdown > -0.5 and momentum > 0.3 and returns > 0.2):
                labels[i] = 0  # Bull_Market

            # Bear Market: Negatif trend, yüksek drawdown, negatif momentum
            elif (trend_short < -0.5 and trend_long < -0.3 and
                  drawdown < -0.8 and momentum < -0.3 and returns < -0.2):
                labels[i] = 1  # Bear_Market

            # High Volatility: Yüksek volatilite, değişken momentum
            elif volatility > 1.5 or abs(returns) > 1.0:
                labels[i] = 3  # High_Volatility

            # Sideways Market: Düşük trend, orta volatilite
            else:
                labels[i] = 2  # Sideways_Market

        return labels

    def _calculate_confidence(self, features: np.ndarray) -> float:
        """
        Basit güven skoru hesaplama
        """
        try:
            # Feature değerlerinin mutlak değerlerinin ortalaması
            feature_strength = np.mean(np.abs(features))

            # Güven skoru (0-1 arası)
            confidence = min(1.0, feature_strength / 2.0)
            confidence = max(0.1, confidence)  # Minimum %10 güven

            return confidence
        except:
            return 0.5
