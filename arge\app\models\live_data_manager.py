"""
Canlı veri yöneticisi - Proxy sistemi ile rate limiting aşma
"""

import requests
import pandas as pd
import json
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

class LiveDataManager:
    """
    Canlı forex verilerini proxy sistemi ile çeken sınıf
    """
    
    def __init__(self):
        """
        Live Data Manager'ı başlat
        """
        self.base_url = "http://live-rates.com/rates"
        self.request_count = 0
        self.last_request_time = None
        self.cache = {}
        self.cache_duration = 300  # 5 dakika cache
        
        # Ücretsiz proxy listesi
        self.proxy_list = [
            # HTTP Proxies (ücretsiz)
            {"http": "http://8.210.83.33:80", "https": "http://8.210.83.33:80"},
            {"http": "http://47.74.152.29:8888", "https": "http://47.74.152.29:8888"},
            {"http": "http://20.111.54.16:80", "https": "http://20.111.54.16:80"},
            {"http": "http://47.88.87.74:1080", "https": "http://47.88.87.74:1080"},
            {"http": "http://103.127.1.130:80", "https": "http://103.127.1.130:80"},
            {"http": "http://185.32.6.129:8090", "https": "http://185.32.6.129:8090"},
            {"http": "http://103.149.162.194:80", "https": "http://103.149.162.194:80"},
            {"http": "http://103.127.1.130:80", "https": "http://103.127.1.130:80"},
            # Daha fazla proxy eklenebilir
        ]
        
        self.current_proxy_index = 0
        self.failed_proxies = set()
        
        # Forex çifti mapping
        self.symbol_mapping = {
            'EUR/USD': 'EURUSD',
            'GBP/USD': 'GBPUSD', 
            'USD/JPY': 'USDJPY',
            'AUD/USD': 'AUDUSD',
            'USD/CHF': 'USDCHF',
            'EUR/GBP': 'EURGBP',
            'EUR/JPY': 'EURJPY',
            'GBP/JPY': 'GBPJPY'
        }
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def get_working_proxy(self) -> Optional[Dict]:
        """
        Çalışan bir proxy bul
        
        Returns:
            Dict: Proxy bilgileri veya None
        """
        available_proxies = [p for i, p in enumerate(self.proxy_list) 
                           if i not in self.failed_proxies]
        
        if not available_proxies:
            # Tüm proxy'ler başarısız, listeyi sıfırla
            self.failed_proxies.clear()
            available_proxies = self.proxy_list
        
        if available_proxies:
            return random.choice(available_proxies)
        
        return None
    
    def test_proxy(self, proxy: Dict) -> bool:
        """
        Proxy'nin çalışıp çalışmadığını test et
        
        Args:
            proxy (Dict): Proxy bilgileri
            
        Returns:
            bool: Proxy çalışıyor mu
        """
        try:
            response = requests.get(
                "http://httpbin.org/ip",
                proxies=proxy,
                timeout=10
            )
            return response.status_code == 200
        except:
            return False
    
    def fetch_live_rates(self, use_proxy: bool = True) -> Optional[Dict]:
        """
        Canlı kurları çek
        
        Args:
            use_proxy (bool): Proxy kullan
            
        Returns:
            Dict: Kur verileri veya None
        """
        # Cache kontrolü
        cache_key = "live_rates"
        if cache_key in self.cache:
            cache_time, cache_data = self.cache[cache_key]
            if time.time() - cache_time < self.cache_duration:
                self.logger.info("📦 Cache'den veri döndürülüyor")
                return cache_data
        
        # Rate limiting kontrolü
        if self.last_request_time:
            time_diff = time.time() - self.last_request_time
            if time_diff < 60:  # 1 dakika bekleme
                self.logger.warning(f"⏳ Rate limiting: {60-time_diff:.1f} saniye bekle")
                return None
        
        proxy = None
        if use_proxy:
            proxy = self.get_working_proxy()
            if proxy:
                self.logger.info(f"🌐 Proxy kullanılıyor: {proxy['http']}")
        
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache'
            }
            
            response = requests.get(
                self.base_url,
                proxies=proxy,
                headers=headers,
                timeout=15
            )
            
            self.last_request_time = time.time()
            self.request_count += 1
            
            if response.status_code == 200:
                data = response.json()
                
                # Cache'e kaydet
                self.cache[cache_key] = (time.time(), data)
                
                self.logger.info(f"✅ Canlı veri alındı: {len(data)} kur")
                return data
            else:
                self.logger.error(f"❌ API hatası: {response.status_code}")
                if proxy and use_proxy:
                    # Proxy başarısız, listeden çıkar
                    proxy_index = self.proxy_list.index(proxy)
                    self.failed_proxies.add(proxy_index)
                
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Veri çekme hatası: {str(e)}")
            if proxy and use_proxy:
                # Proxy başarısız, listeden çıkar
                try:
                    proxy_index = self.proxy_list.index(proxy)
                    self.failed_proxies.add(proxy_index)
                except:
                    pass
            
            return None
    
    def get_forex_rate(self, symbol: str) -> Optional[float]:
        """
        Belirli bir forex çifti için kur al
        
        Args:
            symbol (str): Forex çifti (EUR/USD)
            
        Returns:
            float: Kur değeri veya None
        """
        # Önce proxy ile dene
        data = self.fetch_live_rates(use_proxy=True)
        
        # Proxy başarısız ise direkt dene
        if not data:
            self.logger.info("🔄 Proxy başarısız, direkt bağlantı deneniyor")
            data = self.fetch_live_rates(use_proxy=False)
        
        if not data:
            return None
        
        # Symbol mapping
        api_symbol = self.symbol_mapping.get(symbol, symbol.replace('/', ''))
        
        # Veriyi ara
        for rate_data in data:
            if isinstance(rate_data, dict):
                # Farklı API formatları için
                if 'symbol' in rate_data and rate_data['symbol'] == api_symbol:
                    return float(rate_data.get('rate', rate_data.get('price', 0)))
                elif 'pair' in rate_data and rate_data['pair'] == api_symbol:
                    return float(rate_data.get('rate', rate_data.get('price', 0)))
                elif api_symbol in rate_data:
                    return float(rate_data[api_symbol])
        
        self.logger.warning(f"⚠️ {symbol} için kur bulunamadı")
        return None
    
    def get_multiple_rates(self, symbols: List[str]) -> Dict[str, float]:
        """
        Birden fazla forex çifti için kurları al
        
        Args:
            symbols (List[str]): Forex çiftleri listesi
            
        Returns:
            Dict[str, float]: Symbol -> Kur mapping
        """
        rates = {}
        
        # Tek seferde tüm verileri al
        data = self.fetch_live_rates(use_proxy=True)
        
        if not data:
            # Proxy başarısız ise direkt dene
            data = self.fetch_live_rates(use_proxy=False)
        
        if not data:
            self.logger.error("❌ Hiçbir veri kaynağından veri alınamadı")
            return rates
        
        # Her symbol için kur bul
        for symbol in symbols:
            rate = self.get_rate_from_data(data, symbol)
            if rate:
                rates[symbol] = rate
        
        return rates
    
    def get_rate_from_data(self, data: List, symbol: str) -> Optional[float]:
        """
        Veri listesinden belirli symbol için kur bul
        
        Args:
            data (List): API'den gelen veri
            symbol (str): Forex çifti
            
        Returns:
            float: Kur değeri veya None
        """
        api_symbol = self.symbol_mapping.get(symbol, symbol.replace('/', ''))
        
        for rate_data in data:
            if isinstance(rate_data, dict):
                # Farklı API formatları için
                if 'symbol' in rate_data and rate_data['symbol'] == api_symbol:
                    return float(rate_data.get('rate', rate_data.get('price', 0)))
                elif 'pair' in rate_data and rate_data['pair'] == api_symbol:
                    return float(rate_data.get('rate', rate_data.get('price', 0)))
                elif api_symbol in rate_data:
                    return float(rate_data[api_symbol])
        
        return None
    
    def create_dataframe_from_rate(self, symbol: str, rate: float) -> pd.DataFrame:
        """
        Tek bir kur değerinden DataFrame oluştur
        
        Args:
            symbol (str): Forex çifti
            rate (float): Kur değeri
            
        Returns:
            pd.DataFrame: OHLCV formatında DataFrame
        """
        now = datetime.now()
        
        # Basit OHLCV verisi oluştur (gerçek uygulamada daha karmaşık olabilir)
        data = {
            'timestamp': [now],
            'open': [rate],
            'high': [rate * 1.001],  # %0.1 spread simülasyonu
            'low': [rate * 0.999],
            'close': [rate],
            'volume': [1000000]  # Sabit volume
        }
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        return df
    
    def get_historical_simulation(self, symbol: str, periods: int = 100) -> pd.DataFrame:
        """
        Mevcut kurdan geriye dönük simüle edilmiş veri oluştur
        
        Args:
            symbol (str): Forex çifti
            periods (int): Veri noktası sayısı
            
        Returns:
            pd.DataFrame: Simüle edilmiş historical data
        """
        current_rate = self.get_forex_rate(symbol)
        
        if not current_rate:
            self.logger.error(f"❌ {symbol} için mevcut kur alınamadı")
            return pd.DataFrame()
        
        # Geriye dönük tarihler oluştur
        end_time = datetime.now()
        timestamps = [end_time - timedelta(hours=i) for i in range(periods)]
        timestamps.reverse()
        
        # Rastgele fiyat hareketi simülasyonu
        prices = []
        current_price = current_rate
        
        for i in range(periods):
            # Rastgele hareket (%0.5 max)
            change = random.uniform(-0.005, 0.005)
            current_price *= (1 + change)
            prices.append(current_price)
        
        # Son fiyatı gerçek kurla eşitle
        prices[-1] = current_rate
        
        # OHLCV verisi oluştur
        data = []
        for i, (timestamp, price) in enumerate(zip(timestamps, prices)):
            spread = price * 0.0001  # 1 pip spread
            data.append({
                'timestamp': timestamp,
                'open': price,
                'high': price + spread,
                'low': price - spread,
                'close': price,
                'volume': random.randint(500000, 2000000)
            })
        
        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)
        
        return df
    
    def get_proxy_status(self) -> Dict:
        """
        Proxy durumunu döndür
        
        Returns:
            Dict: Proxy istatistikleri
        """
        total_proxies = len(self.proxy_list)
        failed_proxies = len(self.failed_proxies)
        working_proxies = total_proxies - failed_proxies
        
        return {
            'total_proxies': total_proxies,
            'working_proxies': working_proxies,
            'failed_proxies': failed_proxies,
            'request_count': self.request_count,
            'last_request': self.last_request_time,
            'cache_size': len(self.cache)
        }
